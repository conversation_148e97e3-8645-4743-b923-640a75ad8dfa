package net.tartan.platform.integration.service;

import lombok.extern.slf4j.Slf4j;
import net.tartan.platform.integration.entity.OaAutoUserInfo;
import net.tartan.platform.integration.entity.UserInfo;
import net.tartan.platform.integration.mapper.OaAutoUserMapper;
import net.tartan.platform.integration.mapper.ProjectMapper;
import net.tartan.platform.integration.service.impl.oa.AutoOaFormServiceImpl;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

@SpringBootTest
@Slf4j
public class OaAutoSendTest {
    @Autowired
    private ProjectMapper projectMapper;
    @Autowired
    private OAClientService clientService;
    @Autowired
    private AutoOaFormServiceImpl autoOaFormService;
    @Autowired
    private OAClientService oaClientService;
    @Autowired
    private OaAutoUserMapper oaAutoUserMapper;
    @Autowired
    private IUserInfoService userInfoService;
    @Autowired
    private RotarySystemServiceImpl rotarySystemServiceImpl;

    // 通过excel导入名单
    @Test
    void testImportUserList() throws IOException {

        // 读取excel内容
        List<OaAutoUserInfo> autoUserList = new ArrayList<>();
        String reloadPath = "D:\\软件团队\\员工通讯录1.xlsx";

        //1.获取工作簿
        XSSFWorkbook workbook = new XSSFWorkbook(reloadPath);
        //2.获取工作表
        XSSFSheet sheet = workbook.getSheetAt(0);
        //获取行
        int lastRowNum = sheet.getLastRowNum();

        for (int i = 2; i <= lastRowNum; i++) { // 跳过第前两行行表头，从第三行开始读
            Row rowValue = sheet.getRow(i);
            if (rowValue == null) {
                continue; // 跳过空行
            }

            OaAutoUserInfo autoUserInfo = new OaAutoUserInfo();
            autoUserInfo.setAutoType(1L);

            // 总成序列号 (第0列)
            Cell cell0 = rowValue.getCell(0);
            if (cell0 != null) {
                String name = getStringValue(cell0);
                UserInfo userInfo = userInfoService.getUserByName(name);
                if(userInfo == null){
                    log.warn("该用户不存在:{}", name);
                    continue;
                }
                autoUserInfo.setUserId(userInfo.getUserId());
            }


            autoUserList.add(autoUserInfo);
        }

        for(OaAutoUserInfo info : autoUserList){
            //保存
            oaAutoUserMapper.insert(info);
            System.out.println(info.toString());
        }
//        for(OaAutoUserInfo info :autoUserList ){
//
//            System.out.println(info.toString());
//        }
    }

    // 测试发送
    @Test
    void testOAToken(){
        autoOaFormService.autoSendByList();
    }


    private String getStringValue(Cell cell) {
        if (cell == null) return null;

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    // 避免科学计数法
                    return new BigDecimal(cell.getNumericCellValue()).toPlainString();
                }
            default:
                return cell.toString().trim();
        }
    }

}
