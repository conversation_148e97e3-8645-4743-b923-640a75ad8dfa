<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.tartan.platform.integration.mapper.UserInfoMapper">

    <select id="listAll" parameterType="net.tartan.platform.integration.beans.vo.UserAndRoleVo" resultType="net.tartan.platform.integration.beans.vo.UserAndRoleVo">
        select m.name,
                ro.id as role_id,
                ro.name as role_name,
                m.email,
                m.phone_number,
                m.is_enable,
                m.is_delete,
                ai.user_id,
                ai.username,
                ai.password,
                ai.icon,
                ai.create_time,
                ai.update_time,
                dp.department_id,
                dp.name as department_name
        from user_info ai
                join member_info m on ai.member_id = m.member_id
                join department dp on m.org_department_id = dp.department_id
                left join user_info_role_relation uirr on uirr.user_id = ai.user_id
                left join `role` ro on ro.id = uirr.role_id
        <where>
            <if test="username!=null and username!=''">
                and ai.username = #{username}
            </if>
            <if test="name!=null and name!=''">
                and m.name = #{name}
            </if>
            <if test="departmentId!=null and departmentId!=''">
                and dp.department_id = #{departmentId}
            </if>
            <if test="phoneNumber!=null and phoneNumber!=''">
                and m.phone_number = #{phoneNumber}
            </if>
            <if test="userId!=null and userId!=''">
                and ai.user_id = #{userId}
            </if>
        </where>
    </select>
    <select id="listByRole" resultType="net.tartan.platform.integration.beans.vo.UserInfoVo">
        select mb.name,
               mb.email,
               mb.phone_number,
               mb.is_enable,
               mb.is_delete,
               ai.user_id,
               ai.username,
               ai.password,
               ai.icon,
               ai.create_time,
               ai.update_time,
               dp.department_id,
               dp.name as department_name
        from user_info_role_relation arr
                 join user_info ai on arr.user_id = ai.user_id and arr.role_id = #{roleId}
                 join member_info mb on mb.member_id = ai.member_id
                 join department dp on dp.department_id = mb.org_department_id
    </select>
    <select id="listByMenId" resultType="net.tartan.platform.integration.beans.vo.UserInfoVo">
        select mb.name,
               mb.email,
               mb.phone_number,
               mb.is_enable,
               mb.is_delete,
               ai.user_id,
               ai.username,
               ai.password,
               ai.icon,
               ai.create_time,
               ai.update_time,
               dp.department_id,
               dp.name as department_name
        from role_menu_info_relation rmir
                 join user_info_role_relation arr on rmir.role_id = arr.role_id and rmir.menu_id = #{menuId}
                 join user_info ai on arr.user_id = ai.user_id
                 join member_info mb on mb.member_id = ai.member_id
                 join department dp on dp.department_id = mb.org_department_id
    </select>
    <select id="findByDepartmentId" resultType="net.tartan.platform.integration.beans.vo.UserInfoVo">
        select m.name,
               m.email,
               m.phone_number,
               m.is_enable,
               m.is_delete,
               ai.user_id,
               ai.username,
               ai.password,
               ai.icon,
               ai.create_time,
               ai.update_time,
               dp.department_id,
               dp.name as department_name
        from user_info ai
                 join member_info m on ai.member_id = m.member_id
                 join department dp on m.org_department_id = dp.department_id
        where m.org_department_id = #{departmentId};
    </select>
    <select id="filter" resultType="java.lang.Long">
        select user_id from user_info where user_id in
        <foreach collection="userIdList" item="userId" separator="," open="(" close=")">
            #{userId}
        </foreach>
    </select>

    <select id="getAllCompanys" resultType="net.tartan.platform.integration.entity.Department">
        select distinct name,department_id ,path from department d where LENGTH(path) = 12 and is_delete =0 and path!='000000010001' order by path
    </select>

    <select id="getPrimaryDepartments" resultType="net.tartan.platform.integration.entity.Department">
        select distinct name,department_id ,path from department d where LENGTH(path) = 16 and is_delete =0 order by path
    </select>

    <select id="getUserAndDepartments" resultType="java.util.Map">
        select u.user_id as id,u.username,u.member_id ,m.name ,m.org_department_id ,d.name,
        left(d.path,16) as path
        from user_info u
        left join member_info m on u.member_id = m.member_id
        left join department d on m.org_department_id = d.department_id
        where m.org_department_id !=-1
        and m.is_enable =1
        and m.is_delete =0
        order by path
    </select>
    <select id="getMemberNameByUserId" resultType="java.lang.String">
        SELECT mi.`name` FROM `user_info` ui
        join member_info mi on ui.member_id=mi.member_id
        where ui.user_id = #{userId}
    </select>
    <select id="getUserByName" resultType="net.tartan.platform.integration.entity.UserInfo">
        select ui.*
        from member_info mi
                 LEFT JOIN user_info ui on mi.member_id = ui.member_id
        where mi.name = #{userName}
        limit 1;
    </select>

</mapper>
