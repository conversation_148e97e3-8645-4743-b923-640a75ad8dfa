package net.tartan.platform.integration.entity.repair;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *  RMA返修单
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "component_repair_rma")
public class RepairRma implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "repair_id", type = IdType.AUTO)
    private Long repairId;

    /**
     * 返修单号
     */
    private String repairCode;

    /**
     * 与mwd关联的Id
     */
    private Long mwdId;

    /**
     * 作业编号
     */
    private String jobNumber;

    /**
     * 井号
     */
    private String wellNumber;

    /**
     * 接收状态
     * 未接收：-1，已接收，1
     */
    private Integer received;

    /**
     * 返修人
     */
    private String sendUser;

    /**
     * 返修时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sendDate;

    /**
     * 接收人
     */
    private String receiveUser;

    /**
     * 接收时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date receiveDate;

    /**
     * 备注
     */
    private String notes;

    ///////

    /**
     * 品名(主要用于没有序列号的工单，知道修的叫啥名)
     */
    private String invName;

    /**
     * 序列号，批次号
     */
    private String serialNumber;

    /**
     * 母件品名
     */
    private String parentInvName;

    /**
     * 母件序列号
     */
    private String parentSerialNumber;

    /**
     * 现场联系人
     */
    private String contactUser;

    /**
     * 现场联系人的联系方式
     */
    private String contactNumber;

    /**
     * 维修人员/检修人
     */
    private String repairUser;

    /**
     * 固件版本号
     */
    private String versionNumber;

//    /**
//     * 入井趟次
//     */
//    private String run;

//    /**
//     * 入井时间
//     */
//    private BigDecimal inWellHour;

    /**
     * 本次入井时间（等于mwd工单中 detail里的 InWellHour）
     */
    private BigDecimal currentInWellHour;

    /**
     * 总入井时间
     */
    private BigDecimal totalInWellHrs;

//    /**
//     * 循环时间
//     */
//    private BigDecimal circulateHrs;

    /**
     * 最高温度
     */
    private BigDecimal maxBht;

    /**
     * 本次最高温度（等于mwd工单中 detail里的 maxBht）
     */
    private BigDecimal currentMaxBht;

//    /**
//     * 循环温度
//     */
//    private BigDecimal circulateBht;

    /**
     * 返回原因
     */
    private String returnReason;

    // 车间检查信息 -------
    // 常温功能检查
    private Integer normalTemperatureFunctionCheck = 0;

    // 高温测试
    private Integer highTemperatureTest = 0;

    // 振动测试
    private Integer vibrationTest = 0;

    // 旋转测试
    private Integer rotationTest = 0;

    // 短传性能测试
    private Integer shortTransmissionPerformanceTest = 0;

    // log数据存储测试
    private Integer logDataStorageTest = 0;

    // 绝缘检查
    private Integer insulationCheck = 0;

    // 本体检查
    private Integer bodyCheck = 0;

    // 盖板检查
    private Integer coverCheck = 0;

    // 本体线路RingOut测量
    private Integer bodyLineRingOutMeasurement = 0;

    // 线路固封情况检查
    private Integer lineSealingConditionCheck = 0;

    // 接头状态检查
    private Integer connectorStatusCheck = 0;

    // EC物理状态检查
    private Integer ecPhysicalStatusCheck = 0;

    // 伽马探头物理状态检查
    private Integer gammaProbePhysicalStatusCheck = 0;

    // 电池物理状态检查
    private Integer batteryPhysicalStatusCheck = 0;

    // 车间诊断初步结论
    private String workshopPreliminaryDiagnosisConclusion = "";


    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
