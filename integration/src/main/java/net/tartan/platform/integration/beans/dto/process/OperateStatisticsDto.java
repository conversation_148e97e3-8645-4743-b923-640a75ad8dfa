package net.tartan.platform.integration.beans.dto.process;

import lombok.Data;
import net.tartan.platform.common.enums.EnumProjectType;
import net.tartan.platform.common.enums.EnumStatisticsType;
import net.tartan.platform.common.enums.EnumTimeType;
import net.tartan.platform.common.enums.EnumWorkOrderType;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 工具操作明细
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-17
 */
@Data
public class OperateStatisticsDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 统计类型
     */
    private EnumStatisticsType statisticsType;
    /**
     * 设备编号
     */
    private List<String> deviceNumber;
    /**
     * 项目类型
     */
    private List<EnumProjectType> projectType;

    /**
     * 工单类型
     */
    private List<EnumWorkOrderType> workOrderType ;

    private EnumTimeType timeType;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 查询月份
     */
    private String month;

    /**
     * 人员
     */
    private String operateId;
}
