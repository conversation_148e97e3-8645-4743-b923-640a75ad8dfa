package net.tartan.platform.integration.entity.dailyreport.RSS;


import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.constant.PfConstant;
import net.tartan.platform.integration.entity.dailyreport.MwdBhaItem;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = PfConstant.RSS_BHA_REPORT)
public class RSSBHAReport {
    @Transient
    private Long id;

    /**
     * 报告类型
     */
    @Transient
    private String reportType;

    @Id
    @JsonIgnore
    private String reportId;

    /**
     * 作业id
     */
    @NotNull
    private Long jobId;
    private String jobNumber;

    /**
     * 趟次
     */
    @NotNull
    private Integer run;

    /**
     * 公司
     */
    private String company;

    /**
     * 承包商
     */
    private String contractor;

    /**
     * 井队号
     */
    private String rigNo;

    /**
     * 开始时间
     */
    private String startDate;

    /**
     * 井号
     */
    private String wellNumber;
    /**
     * 位置
     */
    private String location;

    /**
     * 入井井深
     */
    private BigDecimal depthIn;

    /**
     * 出井井深
     */
    private BigDecimal depthOut;

    private String bha;

    /**
     * cadence
     */
    private String cadence;

    /**
     * 目的
     */
    private String purpose;

    /**
     * 钻具组合
     */
    private List<MwdBhaItem> itemList;
}
