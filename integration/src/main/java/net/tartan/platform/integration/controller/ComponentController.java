package net.tartan.platform.integration.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.common.beans.ResultCode;
import net.tartan.platform.integration.beans.query.ComponentTestQuery;
import net.tartan.platform.integration.beans.query.ToolQuery;
import net.tartan.platform.integration.beans.vo.*;
import net.tartan.platform.integration.entity.Component;
import net.tartan.platform.integration.entity.ComponentTest;
import net.tartan.platform.integration.exception.BusinessException;
import net.tartan.platform.integration.service.IComponentService;
import net.tartan.platform.integration.service.IComponentTestService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <p>
 * 设备库存信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-09
 */
@RestController
@RequestMapping("/component")
public class ComponentController {
    @Autowired
    private IComponentService componentService;
    @Autowired
    private IComponentTestService componentTestService;

    @PostMapping("list/{current}/{size}")
    public CommonResult assemble(@PathVariable long current,
                                 @PathVariable long size,
                                 @RequestBody ToolQuery toolQuery) {
        IPage<ComponentVo> page = new Page<>(current, size);
        componentService.list(page, toolQuery);
        return CommonResult.success(page);
    }

    @PostMapping("infoList")
    public CommonResult listBaseInfo(@RequestBody ToolQuery toolQuery) {
        List<ComponentVo> page = componentService.listBaseInfo(toolQuery);
        return CommonResult.success(page);
    }

    @PostMapping("add")
    public CommonResult add(@RequestBody ComponentVo vo) {
        if(ObjectUtils.isEmpty(vo)){
            throw new BusinessException(ResultCode.VALIDATE_FAILED);
        }
        componentService.add(vo);
        return CommonResult.success();
    }

    @PostMapping("update")
    public CommonResult update(@RequestBody ComponentInfoDetailVo vo) {
        if(ObjectUtils.isEmpty(vo)){
            throw new BusinessException(ResultCode.VALIDATE_FAILED);
        }
        componentService.updateComponent(vo);
        return CommonResult.success();
    }

    @PostMapping("tempType")
    public CommonResult getTempType(@RequestBody ToolQuery toolQuery) {
        if(ObjectUtils.isEmpty(toolQuery)){
            toolQuery = new ToolQuery();
        }
        return CommonResult.success(componentService.getTempType(toolQuery));
    }



    @PostMapping("nameList")
    public CommonResult nameList(@RequestBody ToolQuery toolQuery) {
        List<Component> invNameList = componentService.getInvNameList(toolQuery);
        return CommonResult.success(invNameList);
    }

    @PostMapping("getByQuery")
    public CommonResult getByInvNameAndSN(@RequestBody ToolQuery toolQuery) {
        if (ObjectUtils.isEmpty(toolQuery) || StringUtils.isEmpty(toolQuery.getInvName()) || StringUtils.isEmpty(toolQuery.getSerialNumber())) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED);
        }
        List<Component> invNameList = componentService.getByQuery(toolQuery);
        return CommonResult.success(invNameList);

    }

    @PostMapping("history/list/{current}/{size}")
    public CommonResult historyList(@PathVariable long current,
                                    @PathVariable long size,
                                    @RequestBody ToolQuery toolQuery) {
        IPage<ComponentServiceVo> page = new Page<>(current, size);
        componentService.getComponentServiceList(page, toolQuery);
        return CommonResult.success(page);
    }

    // 聚合后的服役历史列表，将相同的部件进行分组合并，并单独统计出每一个的详细信息（history/detail）
    @PostMapping("historyDetail/list/{current}/{size}")
    public CommonResult historyList_v2(@PathVariable long current,
                                       @PathVariable long size,
                                       @RequestBody ToolQuery toolQuery) {
        IPage<ComponentInfoDetailVo> page = new Page<>(current, size);
        componentService.getComponentServiceList_v2(page, toolQuery);
        return CommonResult.success(page);
    }

    @PostMapping("historyDetail")
    public CommonResult historyDetail(@RequestBody ToolQuery toolQuery) {
        ComponentInfoDetailVo componentInfoDetailVo = componentService.getComponentServiceHistoryDetail(toolQuery);
        return CommonResult.success(componentInfoDetailVo);
    }

    // 聚合统计当前核心部件的数据
    @PostMapping("core/list")
    public CommonResult coreList(@RequestBody ToolQuery toolQuery) {
        if (toolQuery.getDeviceId() == null || toolQuery.getDeviceId() <= 0) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED);
        }
        return CommonResult.success(componentService.getCoreList(toolQuery));
    }

    @PostMapping("out/list/{current}/{size}")
    public CommonResult outList(@PathVariable long current,
                                @PathVariable long size,
                                @RequestBody ToolQuery toolQuery) {
        IPage<ComponentServiceVo> page = new Page<>(current, size);
        componentService.getComponentOutList(page, toolQuery);
        return CommonResult.success(page);
    }

//    @PostMapping("repair/list/{current}/{size}")
//    public CommonResult repairList(@PathVariable long current,
//                                   @PathVariable long size,
//                                   @RequestBody ToolQuery toolQuery) {
//        IPage<ComponentRepairHistoryVo> page = new Page<>(current, size);
//        componentService.getComponentRepairHistory(page, toolQuery);
//        return CommonResult.success(page);
//    }

//    @PostMapping("history/detail")
//    public CommonResult getDetailInfo(@RequestBody ToolQuery toolQuery) {
//        if (Objects.isNull(toolQuery)) {
//            throw new BusinessException(ResultCode.VALIDATE_FAILED);
//        }
//        if (toolQuery.getInvName() == null || toolQuery.getInvName().isEmpty()) {
//            throw new BusinessException(ResultCode.VALIDATE_FAILED);
//        }
//        if (toolQuery.getSerialNumber() == null || toolQuery.getSerialNumber().isEmpty()) {
//            throw new BusinessException(ResultCode.VALIDATE_FAILED);
//        }
//        ComponentRepairDetailVo componentRepairDetailVo = componentService.getDetail(toolQuery);
//        return CommonResult.success(componentRepairDetailVo);
//    }

    @PostMapping("test/list/{current}/{size}")
    public CommonResult getComponentTestList(@PathVariable long current,
                                             @PathVariable long size,
                                             @RequestBody ComponentTestQuery query) {
        IPage<ComponentTestVo> page = new Page<>(current, size);
        componentTestService.getListByPage(page, query);
        return CommonResult.success(page);
    }

    @PostMapping("test/history/list")
    public CommonResult getComponentTestHistoryList(@RequestBody ToolQuery toolQuery) {
        if (StringUtils.isEmpty(toolQuery.getSerialNumber())) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED);
        }
        List<ComponentTestServiceHistoryVo> componentTestServiceHistoryVos = componentTestService.getComponentServiceHistoryTestList(toolQuery.getSerialNumber());
        return CommonResult.success(componentTestServiceHistoryVos);
    }

    @PostMapping("test/add")
    public CommonResult addComponentTest(@RequestBody ComponentTest componentTest) {
        componentTest.setSerialNumber(new DateTime().toString("yyyyMMddHHmmss"));
        componentTestService.save(componentTest);
        return CommonResult.success();
    }

    @PostMapping("test/update")
    public CommonResult updateComponentTest(@RequestBody ComponentTest componentTest) {
        componentTestService.updateById(componentTest);
        return CommonResult.success();
    }

    @PostMapping("test/delete")
    public CommonResult deleteComponentTest(@RequestBody ComponentTest componentTest) {
        componentTestService.removeById(componentTest.getComponentTestId());
        return CommonResult.success();
    }

    @PostMapping("test/sn/fuzzyList")
    public CommonResult getComponentTestFuzzyList(@RequestParam("key") String key) {
        List<ComponentTest> componentTests = componentTestService.getComponentTestFuzzyList(key);
        return CommonResult.success(componentTests);
    }

    @PostMapping("test/export")
    public CommonResult exportComponentTest(@RequestBody ComponentTest componentTest) {
        componentTestService.exportComponentTest(componentTest);
        return CommonResult.success();
    }


    /**
     * 模糊查询序列号
     *
     * @param toolQuery
     * @return
     */
    @PostMapping("getSerialNumberList")
    public CommonResult getSerialNumberList(@RequestBody(required = false) ToolQuery toolQuery) {

        return CommonResult.success(componentTestService.serialNumberList(toolQuery));
    }

}
