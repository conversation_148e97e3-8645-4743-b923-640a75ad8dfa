package net.tartan.platform.integration.beans.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.enums.EnumEmailBusinessType;
import net.tartan.platform.integration.entity.EmailTemplateRelation;

import java.util.List;

@Data
@NoArgsConstructor
public class EmailTemplateVo {

    private Long id;

    private EnumEmailBusinessType emailBusinessType;

    private String emailTitle;

    private String emailContent;

    private List<EmailTemplateRelation> sendList;

    private List<EmailTemplateRelation> ccList;
}
