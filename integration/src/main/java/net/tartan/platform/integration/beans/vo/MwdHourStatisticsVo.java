package net.tartan.platform.integration.beans.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MwdHourStatisticsVo {
    /**
     * 设备类型
     */
    private String type;
    /**
     * 工时
     */
    private Double hour;
    /**
     * 时间
     */
    private String date;

    // 最小颗粒度：byMonth, byDay
    private String statisticType;

    // finish, receive
    private String searchFor;

    private String year;

    // 同一年中选择的月份[]
    private List<String> monthList;

    private Integer amount;

    private String weekNumber;

    private String memberId;

    private String startTime;
    private String endTime;

}
