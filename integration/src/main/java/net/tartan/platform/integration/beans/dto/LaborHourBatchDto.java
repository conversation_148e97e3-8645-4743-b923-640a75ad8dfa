package net.tartan.platform.integration.beans.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LaborHourBatchDto {

    /**
     * 工时考勤日期 （填报当天日期）
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date laborDate;

    /**
     * 星期几
     */
    private String week;

    private List<LaborHourDto> infoList;

    // false 表示非测试数据 走正常日期校验
    // true 表示测试数据 跳过日期校验
    private boolean testLine = false;
}
