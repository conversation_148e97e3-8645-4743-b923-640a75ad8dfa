package net.tartan.platform.integration.beans.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.integration.entity.MwdLoadOutDetail;

import java.util.Date;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MwdLoadOutVo {
    private Long loadOutId;

    private Long deviceId;
    private String invName;
    private String serialNumber;

    /**
     * 清点人员
     */
    private String inCheckMember;
    private String outCheckMember;

    /**
     * 出库日期
     */
    private String dateOut;
    /**
     * 入库日期
     */
    private String dateIn;

    /**
     * 地点（如果是上客户井 那么则没有井号只有地点
     */
    private String location;

    /**
     * 井号
     */
    private String wellNumber;

    private Long status;
    private String statusStr;

    private String jobNumber;

    /**
     * 出入库清单明细信息
     */
    private List<MwdLoadOutDetailVo> loadOutDetailList;

    /**
     * 是否需要初始化
     * 1 需要
     * -1 不需要
     * 默认是不需要
     */
    private int needInit = -1;

    /**
     * 创建时间
     */
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date updateTime;
}
