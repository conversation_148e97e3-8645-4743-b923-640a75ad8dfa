package net.tartan.platform.integration.entity.mwdCert;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.constant.PfConstant;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 近钻头合格证
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = PfConstant.MWD_CERT_REPORT)
public class MwdAtBitInfo extends MwdCertBaseInfo{

    /**
     * 出厂序列状态
     * 生产序列显示为Pd,风险序列显示为Ri，试验序列显示为Te
     */
    private String riskType = "";

    /**
     * 探伤检测
     */
    private String dmgCheck = "合格";

    /**
     * 功能测试
     */
    private String functionTest = "合格";

    /**
     * 绝缘测试
     */
    private String insulationTest = "合格";

    /**
     * 电池电压
     */
    private String batteryVoltage = "0";

    /**
     * 发射电压
     */
    private String shootVoltage = "0";

    /**
     * 伽马测试
     */
    private String gammaTest = "Pass";

    /**
     * 绝缘电阻
     */
    private String insulationPart = "0";

    /**
     * 井斜测定
     */
    private String deviationTest = "通过";

    /**
     * EC型号
     */
    private String ecType = "NBT";

    /**
     * 检测结果
     */
    private String checkResult = "仪器按照达坦近钻检查测试规范《TSC/ZY-MWD-AB-MSS-001-1.1》《TSC/JM-MWD-AB-SWI-003-1.0》进行出厂检测通过。";

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
    }
}
