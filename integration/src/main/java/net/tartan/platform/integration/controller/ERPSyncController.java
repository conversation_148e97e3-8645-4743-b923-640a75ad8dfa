package net.tartan.platform.integration.controller;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import lombok.extern.slf4j.Slf4j;
import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.integration.service.ERPSyncService;

/**
 * ERP数据同步控制器
 * 核心功能：自动检测并同步ERP中新增的付款申请单到OA系统
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Slf4j
@RestController
@RequestMapping("/api/erp-sync")
public class ERPSyncController {

    @Autowired
    private ERPSyncService erpSyncService;

    /**
     * 增量同步付款申请单
     * 自动检测ERP数据库中新增的付款申请单并同步到OA系统
     *
     * @return 同步结果统计
     */
    @PostMapping("/sync")
    public CommonResult<Map<String, Object>> syncNewPaymentApplications() {
        log.info("开始增量同步ERP新增付款申请单到OA系统");

        try {
            ERPSyncService.SyncResult result = erpSyncService.syncNewPaymentApplications();

            // 构建返回数据
            Map<String, Object> data = new HashMap<>();
            data.put("totalCount", result.getTotalCount());
            data.put("successCount", result.getSuccessCount());
            data.put("failedCount", result.getFailedCount());
            data.put("executeTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            log.info("增量同步完成 - 总数: {}, 成功: {}, 失败: {}",
                    result.getTotalCount(), result.getSuccessCount(), result.getFailedCount());

            if (result.isSuccess()) {
                return CommonResult.success(data, "增量同步完成");
            } else {
                data.put("errorMessage", result.getErrorMessage());
                return CommonResult.failed("增量同步部分失败");
            }

        } catch (Exception e) {
            log.error("增量同步付款申请单失败", e);
            return CommonResult.failed("增量同步失败: " + e.getMessage());
        }
    }

}
