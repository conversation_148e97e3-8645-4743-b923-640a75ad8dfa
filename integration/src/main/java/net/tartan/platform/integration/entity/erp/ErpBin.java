package net.tartan.platform.integration.entity.erp;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 库位信息/CHT/儲位資料/ENU/Location Data
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("BIN")
public class ErpBin implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId("BIN_ID")
    private String binId;

    /**
     * 库位编号
     */
    @TableField("BIN_CODE")
    private String binCode;

    /**
     * 库位说明
     */
    @TableField("BIN_NAME")
    private String binName;

    @TableField("WAREHOUSE_ID")
    private String warehouseId;

    /**
     * 备注
     */
    @TableField("REMARK")
    private String remark;

    /**
     * 主要
     */
    @TableField("MAIN")
    private Boolean main;

    @TableField("STORAGE_LIMIT_ID")
    private String storageLimitId;

//    /**
//     * 自定义字段0
//     */
//    @TableField("UDF001")
//    private Double udf001;
//
//    /**
//     * 自定义字段1
//     */
//    @TableField("UDF002")
//    private Double udf002;
//
//    /**
//     * 自定义字段2
//     */
//    @TableField("UDF003")
//    private Double udf003;
//
//    /**
//     * 自定义字段3
//     */
//    @TableField("UDF011")
//    private Double udf011;
//
//    /**
//     * 自定义字段4
//     */
//    @TableField("UDF012")
//    private Double udf012;
//
//    /**
//     * 自定义字段5
//     */
//    @TableField("UDF013")
//    private Double udf013;
//
//    /**
//     * 自定义字段6
//     */
//    @TableField("UDF021")
//    private String udf021;
//
//    /**
//     * 自定义字段7
//     */
//    @TableField("UDF022")
//    private String udf022;
//
//    /**
//     * 自定义字段8
//     */
//    @TableField("UDF023")
//    private String udf023;
//
//    /**
//     * 自定义字段9
//     */
//    @TableField("UDF024")
//    private String udf024;
//
//    /**
//     * 自定义字段10
//     */
//    @TableField("UDF025")
//    private String udf025;
//
//    /**
//     * 自定义字段11
//     */
//    @TableField("UDF026")
//    private String udf026;
//
//    /**
//     * 自定义字段12
//     */
//    @TableField("UDF041")
//    private LocalDateTime udf041;
//
//    /**
//     * 自定义字段13
//     */
//    @TableField("UDF042")
//    private LocalDateTime udf042;
//
//    /**
//     * 自定义字段14
//     */
//    @TableField("UDF051")
//    private String udf051;
//
//    /**
//     * 自定义字段15
//     */
//    @TableField("UDF052")
//    private String udf052;
//
//    /**
//     * 自定义字段16
//     */
//    @TableField("UDF053")
//    private String udf053;
//
//    /**
//     * 自定义字段17
//     */
//    @TableField("UDF054")
//    private String udf054;

    /**
     * 版本号，不要随意更改
     */
    @TableField("Version")
    private byte[] version;

    /**
     * 单据状态属性
     */
    @TableField("ApproveStatus")
    private String approvestatus;

    /**
     * 修改日期
     */
    @TableField("ApproveDate")
    private Date approvedate;

    /**
     * 修改人
     */
    @TableField("ApproveBy")
    private String approveby;

    /**
     * 创建日期
     */
    @TableField("CreateDate")
    private Date createdate;

    /**
     * 最后修改日期
     */
    @TableField("LastModifiedDate")
    private Date lastmodifieddate;

    /**
     * 修改日期
     */
    @TableField("ModifiedDate")
    private Date modifieddate;

    /**
     * 创建者
     */
    @TableField("CreateBy")
    private String createby;

    /**
     * 最后修改者
     */
    @TableField("LastModifiedBy")
    private String lastmodifiedby;

    /**
     * 修改者
     */
    @TableField("ModifiedBy")
    private String modifiedby;



}
