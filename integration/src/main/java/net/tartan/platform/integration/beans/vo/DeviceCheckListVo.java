package net.tartan.platform.integration.beans.vo;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.enums.EnumDeviceUseStatus;
import net.tartan.platform.common.enums.EnumRiskType;
import net.tartan.platform.common.enums.EnumRubberType;

import java.math.BigDecimal;
import java.util.Date;

@Data
@NoArgsConstructor
public class DeviceCheckListVo {

    private Long deviceId;

    /**
     * 仪器类型
     */
    private Long deviceType;
    /**
     * 仪器库存类型 库房盘点按照这个来分类
     */
    private Long stockType;
    /**
     * 品名
     */
    private String invName;
//    /**
//     * 品号
//     */
//    private String invCode;
//    /**
//     * 批次号
//     */
//    private String lotCode;

    /**
     * 序列号
     */
    private String serialNumber;

    /**
     * 序列号是否为有效序列号
     * valid - 有效
     * invalid - 无效
     */
    private String isValid;

    /**
     * 风险类型
     */
    @TableField(value = "risk_type", updateStrategy= FieldStrategy.IGNORED)
    private EnumRiskType riskType;

    /**
     * 上次维修时间
     */
    private String lastRepairDate;

//    /**
//     * 额定温度
//     */
//    private Double standardTemperature;
//
//    /**
//     * 最高温度
//     */
//    private Double maxBht;
//
//    /**
//     * 累计时长
//     */
//    private Double totalTime;

// 井下仪器参数

    /**
     * 仪器的使用状态
     */
    private EnumDeviceUseStatus useStatus;

    //螺杆参数信息
    /**
     * 弯度:单弯 1 / 可调 0
     */
    private Integer curve;

    /**
     * 角度
     */
    private BigDecimal angle;

    /**
     * 扣型
     */
    private String claspType;

    /**
     * 泥浆类型: 水基 1  / 油基 0
     */
    private String mudType;

    /**
     * 最大外径
     */
    private BigDecimal odMax;

    /**
     * 是否带扶正器：带 1 / 不带 0
     */
    private Integer takeStb;

    /**
     * 扶正器尺寸
     */
    private BigDecimal stbSize;

    /**
     * 扶正器描述
     */
    private String stbDescribe;

    //非核心部件
    /**
     * 上TC静圈
     */
    private String upTcStaticCircle;
    /**
     * 上TC动圈
     */
    private String upTcDynamicCircle;
    /**
     * 下TC静圈
     */
    private String downTcStaticCircle;
    /**
     * 下TC动圈
     */
    private String downTcDynamicCircle;

    /**
     * 串轴承
     */
    private String bearing;

    /**
     * 水帽
     */
    private String waterCap;
    /**
     * 万向轴总成
     */
    private String cardanShaft;

    /**
     * 耐温
     */
    private BigDecimal endureTemperature;

    /**
     * 压差
     */
    private BigDecimal pressureSub;

    /**
     * 扭矩
     */
    private BigDecimal torque;

    //定子
    /**
     * 橡胶类型
     */
    private EnumRubberType rubberType;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 仪器状态
     * data_dictionary 表中 business_type 为 20 的所有均为仪器状态
     */
    private Long status;

    // 新增字段

    //received/send date 接收/发出日期
    private String receivedSendDate;

    //配置数量（串数）
    private String configQuantity;

    //location地点
    private String location;

    //note
    private String note;

//    //配置类型 （基础配置齐备不含仪器、LWD-远端方位Gamma、LWD-近钻头方位Gamma、LWD-自然Gamma、常规MWD测斜、NA）
//    private String configType;

    //owner所属
    private String owner;

    //clientName客户名（如果所属是client则需要填具体的客户名）
    private String clientName;

    //规格（近钻类型）（温度类型（其部件core的temp_type））
    private String specification;

    //kitNumber所属的kit箱
    private String kitNumber;

    //上井建议
    private String wellSuggest;

    //完工时间（根据工单的完成时间来更新）
    private String finishDate;

    //新旧状态（1号、新）
    private String newOldStatus;

    /**
     * 仪器创建人
     */
    private Long createdBy;
    private String createdByStr;

    /**
     * 仪器最后修改人
     */
    private Long lastModifiedBy;
    private String lastModifiedByStr;

    /**
     * 公母扣型
     */
    private String buckleType;

    /**
     * 材料号
     */
    private String materialNumber;
}
