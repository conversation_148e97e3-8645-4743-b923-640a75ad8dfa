package net.tartan.platform.integration.beans.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MWDInstrumentUsageStatisticsVo {
  /** 存货编码 */
  private String invCode;
  /** 存货名称 */
  private String invName;
  /** 批次号 */
  private String serialNumber;
  /** 井号 */
  private String wellNumber;
  /** 作业号id */
  private Long jobId;
  /** 作业号 */
  private String jobNumber;
  /** 循环温度 */
  private BigDecimal circulateBht;
  /** 最高温度 */
  private BigDecimal maxBht;
  /** 入井时长 */
  private BigDecimal total;
  /** 日期 */
  private String date;
  /** 开钻日期yyyy-MM-dd */
  private String dateIn;
  /** 完钻日期yyyy-MM-dd */
  private String dateOut;
  /** 循环时间 */
  private BigDecimal circulateHrs;
  /** 开趟钻数 */
  private Integer run;
  /** 存货型号 */
  private String invStd;
  /** 入井时长 */
  private BigDecimal hour;

}
