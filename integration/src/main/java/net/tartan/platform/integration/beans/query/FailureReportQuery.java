package net.tartan.platform.integration.beans.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FailureReportQuery {


    /**
     * 作业编号
     */
    private Long jobId;


    /**
     * 作业号，工单号
     *  - from JobInfo
     */
    private String jobNumber;

    /**
     * 井号
     *  - from JobInfo
     */
    private String wellId;

    /**
     * 井名
     *  - from WellInfo
     */
    private String wellNumber;

    /**
     * ONLY_FAILURE_REPORT 只看有失效报告的
     * ONLY_MWD_REPORT 只看有维修报告的
     * ALL_REPORT 全查
     */
    private String filterType;
}
