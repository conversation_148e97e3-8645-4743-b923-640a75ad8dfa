package net.tartan.platform.integration.entity.erp;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("PROJECT")
public class Project implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 关联部门
     */
    @TableField("Owner_Dept")
    private String ownerDept;

    /**
     * 关联员工
     */
    @TableField("Owner_Emp")
    private String ownerEmp;

    /**
     * 主键
     */
    @TableId("PROJECT_ID")
    private String projectId;

    /**
     * 项目编号
     */
    @TableField("PROJECT_CODE")
    private String projectCode;

    /**
     * 项目名称
     */
    @TableField("PROJECT_NAME")
    private String projectName;

    /**
     * 项目描述
     */
    @TableField("PROJECT_DESC")
    private String projectDesc;

    /**
     * 备注
     */
    @TableField("REMARK")
    private String remark;

    /**
     * 开始日期
     */
    @TableField("START_DATE")
    private Date startDate;

    /**
     * 截止日期
     */
    @TableField("END_DATE")
    private Date endDate;

    /**
     * 责任部门
     */
    @TableField("ADMIN_UNIT_ID")
    private String adminUnitId;

    /**
     * 结案状态
     */
    @TableField("CLOSE_STATUS")
    private String closeStatus;

    /**
     * 结案日期
     */
    @TableField("CLOSE_DATE")
    private Date closeDate;

    /**
     * 结案说明
     */
    @TableField("CLOSE_DESC")
    private String closeDesc;

    /**
     * 子项目
     */
    @TableField("SUB_PROJECT")
    private Boolean subProject;

    /**
     * 母项目编号
     */
    @TableField("PARENT_PROJECT_ID")
    private String parentProjectId;

    /**
     * 项目已使用
     */
    @TableField("USED")
    private Boolean used;

    /**
     * 内码
     */
    @TableField("SYS_CODE")
    private String sysCode;

    /**
     * 结案人员
     */
    @TableField("CLOSE_EMPLOYEE_ID")
    private String closeEmployeeId;

    /**
     * 责任人员
     */
    @TableField("EMPLOYEE_ID")
    private String employeeId;


    /**
     * 表单所在的流程实例的编号
     */
    @TableField("ProcessInstanceId")
    private String processinstanceid;

    /**
     * 创建日期
     */
    @TableField("CreateDate")
    private Date createdate;

    /**
     * 最后修改日期
     */
    @TableField("LastModifiedDate")
    private Date lastmodifieddate;

    /**
     * 修改日期
     */
    @TableField("ModifiedDate")
    private Date modifieddate;

    /**
     * 创建者
     */
    @TableField("CreateBy")
    private String createby;

    /**
     * 最后修改者
     */
    @TableField("LastModifiedBy")
    private String lastmodifiedby;

    /**
     * 修改者
     */
    @TableField("ModifiedBy")
    private String modifiedby;

    /**
     * 附件
     */
    @TableField("Attachments")
    private String attachments;

    /**
     * 版本号，不要随意更改
     */
    @TableField("Version")
    private byte[] version;

    /**
     * 单据状态属性
     */
    @TableField("ApproveStatus")
    private String approvestatus;

    /**
     * 修改日期
     */
    @TableField("ApproveDate")
    private Date approvedate;

    /**
     * 修改人
     */
    @TableField("ApproveBy")
    private String approveby;

    @TableField("Owner_Org_RTK")
    private String ownerOrgRtk;

    @TableField("Owner_Org_ROid")
    private String ownerOrgRoid;


//    /**
//     * 自定义字段0
//     */
//    @TableField("UDF001")
//    private Double udf001;
//
//    /**
//     * 自定义字段1
//     */
//    @TableField("UDF002")
//    private Double udf002;
//
//    /**
//     * 自定义字段2
//     */
//    @TableField("UDF003")
//    private Double udf003;
//
//    /**
//     * 自定义字段3
//     */
//    @TableField("UDF011")
//    private Double udf011;
//
//    /**
//     * 自定义字段4
//     */
//    @TableField("UDF012")
//    private Double udf012;
//
//    /**
//     * 自定义字段5
//     */
//    @TableField("UDF013")
//    private Double udf013;
//
//    /**
//     * 自定义字段6
//     */
//    @TableField("UDF021")
//    private String udf021;
//
//    /**
//     * 自定义字段7
//     */
//    @TableField("UDF022")
//    private String udf022;
//
//    /**
//     * 自定义字段8
//     */
//    @TableField("UDF023")
//    private String udf023;
//
//    /**
//     * 自定义字段9
//     */
//    @TableField("UDF024")
//    private String udf024;
//
//    /**
//     * 自定义字段10
//     */
//    @TableField("UDF025")
//    private String udf025;
//
//    /**
//     * 自定义字段11
//     */
//    @TableField("UDF026")
//    private String udf026;
//
//    /**
//     * 自定义字段12
//     */
//    @TableField("UDF041")
//    private LocalDateTime udf041;
//
//    /**
//     * 自定义字段13
//     */
//    @TableField("UDF042")
//    private LocalDateTime udf042;
//
//    /**
//     * 自定义字段14
//     */
//    @TableField("UDF051")
//    private String udf051;
//
//    /**
//     * 自定义字段15
//     */
//    @TableField("UDF052")
//    private String udf052;
//
//    /**
//     * 自定义字段16
//     */
    @TableField("UDF053")
    private String udf053;

    /**
     * 自定义字段17
     */
    @TableField("UDF054")
    private String udf054;
}
