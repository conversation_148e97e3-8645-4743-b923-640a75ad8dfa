package net.tartan.platform.integration.constants;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class ComponentTypeList {
    public static final List<String> MWD_COMPONENT_NAME = Collections.unmodifiableList(
            Arrays.asList("AZ-DM Controller", "AZ-DM Gamma", "AZ-Gamma","AZ-Gamma Controller","At Bit EC",
                    "At Bit Gamma","Ball Screw","Battery1","Battery2","Core","DAQ","Driver","Gamma","MPU","Motor","NBR EC","Sensor","TPS")
    );

    public static final List<String> PIT_TOOLS_COMPONENT_NAME = Collections.unmodifiableList(
            Arrays.asList("万向轴壳体", "代用接头", "传动轴","传动轴壳体","定子","本体上接头","本体下接头","转子","防掉接头")
    );
}
