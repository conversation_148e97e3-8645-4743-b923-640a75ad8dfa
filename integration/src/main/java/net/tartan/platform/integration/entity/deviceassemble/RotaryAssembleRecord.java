package net.tartan.platform.integration.entity.deviceassemble;

import lombok.Data;
import net.tartan.platform.integration.beans.vo.RotaryTreeVO;

import java.util.Date;

@Data
public class RotaryAssembleRecord {
    /**
     * 品名
     */
    private String invName;
//    /**
//     * 规格
//     */
//    private String invStd;

    /**
     * 序列号，erp的批次号
     */
    private String serialNumber;
    /**
     * 备注
     */
    private String note;

    // 入库部件树
    private RotaryTreeVO inTree;

    // 出库部件树
    private RotaryTreeVO outTree;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
