package net.tartan.platform.integration.beans.vo;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

// 工时管理-工时明细的视图数据
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LaborHourStatisticsVo {

    private IPage<LaborHourVo> page;

    // 工时天数 (用总工时/每天工作时长8小时得出)
    private BigDecimal totalLaborDays;

    private BigDecimal totalLaborHours;

}
