package net.tartan.platform.integration.beans.vo;

import lombok.Data;

import java.util.List;

/**
 * 存货树形结构VO
 */
@Data
public class InventoryTreeVo {
    
    /**
     * 存货名称
     */
    private String invName;
    
    /**
     * 存货编码
     */
    private String invCode;
    
    /**
     * 层级
     */
    private Integer level;
    
    /**
     * 排序
     */
    private Integer sort;
    
    /**
     * 子节点
     */
    private List<InventoryTreeVo> children = new java.util.ArrayList<>();
} 