package net.tartan.platform.integration.entity.visuallog;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * <AUTHOR>
 * @since 2023-10-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class VlResultsSet implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Long resultsSetId;
    /**
     * 作业号
     */
    private Long jobId;

    private Long bitRunId;

    private BigDecimal bitDepthValue;

    private BigDecimal measDepthValue;

    private Integer offBottom;

    private String comment;

    private Date tDate;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


}
