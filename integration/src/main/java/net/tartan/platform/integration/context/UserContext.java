package net.tartan.platform.integration.context;


import java.util.Objects;

public class UserContext {

    private static final ThreadLocal<ContextInfo> userLocal = new ThreadLocal<>();

    public static void setContextInfo(ContextInfo contextInfo) {
        userLocal.set(contextInfo);
    }

    public static void setUserInfo(Long id, String username, boolean isAdmin) {
        ContextInfo info = new ContextInfo(id, username, isAdmin);
        userLocal.set(info);
    }
    public static void clean() {
        userLocal.remove();
    }

    public static Long getUserId() {
        return userLocal.get().getId();
    }

    public static ContextInfo getUser() {
        return userLocal.get();
    }

    public static String getUserName() {
        return userLocal.get().getUsername();
    }

    public static void setToken(String token) {
        userLocal.get().setToken(token);
    }

    public static String getToken() {
        return userLocal.get().getToken();
    }

    public static boolean isAdmin() {
        final ContextInfo contextInfo = userLocal.get();
        if (Objects.isNull(contextInfo)) {
            return false;
        }
        return contextInfo.isAdmin();
    }

}
