package net.tartan.platform.integration.entity.deviceassemble;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import net.tartan.platform.common.constant.PfConstant;
import net.tartan.platform.integration.entity.deviceassemble.repaircheck.BaseCheckData;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.validation.constraints.NotNull;

@Data
@Document(collection = PfConstant.PIT_TOOLS_REPAIR_CHECK)
public class PitToolsRepairCheckData<T extends BaseCheckData>{

    @Id
    private String checkId;

    /**
     * 台账id
     */
    @NotNull
    private Long pitToolsId;

    private String deviceTypeStr;
    private Long deviceType;
    private T checkData;

    public <RESULT> RESULT getResult(Class<RESULT> requiredType) {
        return JSON.parseObject(JSON.toJSONString(this.checkData), requiredType);
    }

//    public <MODEL extends BaseCheckData> MODEL getModel(Class<MODEL> requiredType) {
//        if (requiredType == null || requiredType.isInstance(this.checkData)) {
//            return (MODEL) this.checkData;
//        }
//        throw new BusinessException(ResultCode.PARAMS_ERROR);
//    }
}
