package net.tartan.platform.integration.beans.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class JobStatisticsCondition {
    private String year;
    private String wellNumber;
    private String jobNumber;
    private String startDate;
    private String endDate;

    public String getEndDate() {
        if (StringUtils.isNotBlank(year)) {
            DateTime date = DateTime.parse(year, DateTimeFormat.forPattern("yyyy"));
            this.endDate = date.plusYears(1).toString("yyyy");
        }
        return this.endDate;
    }

    public String getStartDate() {
        if (StringUtils.isNotBlank(year)) {
            this.startDate = year;
        }
        return this.startDate;
    }
}
