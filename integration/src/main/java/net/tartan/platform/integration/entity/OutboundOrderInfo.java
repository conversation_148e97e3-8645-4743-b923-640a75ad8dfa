package net.tartan.platform.integration.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 出库单信息表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = false)
public class OutboundOrderInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    // 领取单号
    private String orderId;
    // 仪器id
    private Long deviceId;
    // 领取原因
    private String reason;
    // 领取人
    private String receiverName;
    // 领取时间
    private String receiveDate;
    // 核对人
    private String checkerName;
    // 核对时间
    private String checkDate;
    // mwd工单号
    private Long mwdId;

    private Date createTime;

    private Date updateTime;

}
