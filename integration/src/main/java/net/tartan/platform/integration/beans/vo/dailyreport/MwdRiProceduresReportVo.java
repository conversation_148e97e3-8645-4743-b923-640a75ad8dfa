package net.tartan.platform.integration.beans.vo.dailyreport;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.enums.EnumReportType;
import net.tartan.platform.integration.entity.dailyreport.MwdRiProceduresAction;

import java.util.List;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MwdRiProceduresReportVo {

    private EnumReportType reportType;

    private long lastModified;

    /**
     * 操作人
     */
    private String operator;

    /**
     * mwd操作人
     */
    private String mwdOperator;

    /**
     * 签名日期yyyy-MM-dd
     */
    private String signatureDate;

    /**
     * 截图文件key
     */
    private String screenShoot;

    /**
     * 工具
     */
    private List<MwdRiProceduresAction> actionList;


}
