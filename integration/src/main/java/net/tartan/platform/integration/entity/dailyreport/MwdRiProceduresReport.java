package net.tartan.platform.integration.entity.dailyreport;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.constant.PfConstant;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.List;

/**
 * mwd_ri_procedures_report
 *
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Document(PfConstant.MWD_RIP_REPORT)
public class MwdRiProceduresReport implements Serializable {
    @Transient
    private Long id;

    @Id
    @JsonIgnore
    private String reportId;

    /**
     * 作业id
     */
    private Long jobId;

    /**
     * 操作人
     */
    private String operator;

    /**
     * mwd操作人
     */
    private String mwdOperator;

    /**
     * 签名日期yyyy-MM-dd
     */
    private String signatureDate;

    /**
     * 截图文件key
     */
    private String screenShoot;

    /**
     * 工具
     */
    private List<MwdRiProceduresAction> actionList;

    private static final long serialVersionUID = 1L;
}
