package net.tartan.platform.integration.entity.dailyreport;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;

/**
 * mwd_offset_info
 * <AUTHOR>
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MwdOffsetInfo{
    private Long id;

    /**
     * mwd sensor offset report id
     */
    private Long offsetReportId;

    /**
     * Tool String Setup
     */
    private String toolStrSetup;

    /**
     * Module Length
     */
    private BigDecimal moduleLen;

    private BigDecimal directional;

    private BigDecimal gamma;

    private BigDecimal gap;

    private BigDecimal log2;

    private BigDecimal log3;

    private BigDecimal log4;
}
