package net.tartan.platform.integration.beans.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LaborHourByDaysVo {

    private Long projectId;

    private String memberId;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date laborDate;

    /**
     * 星期几
     */
    private String week;

    private BigDecimal totalLaborHours;

    /**
     * 上报人数
     */
    private Integer userCount;

    /**
     * 这一天需要填报的总数
     */
    private Integer userTotal;

    /**
     * 审核状态
     * 用来判断是否有审批
     * null 无审核
     * 0 审核中
     * -1 驳回
     * 1 审核通过
     */
    private Integer approveStatus;
}
