package net.tartan.platform.integration.beans.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProjectLaborHourQuery {

    private String projectNumber;
    private String projectName;
    private String projectManagerName;
    private String projectManagerId;

    private String submitMemberId;
    private List<String> submitMemberIdList;
    private String memberName;

    private String submitDate;
    private String startDate;
    private String endDate;

    private String costHourCondition;

    // 区域划分 可选SH SC
    private String sector;
    private Integer directorUserId;

    private String orderBy; // startDate endDate
    private String orderType;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date laborDate;

    private List<Long> projectIdList;

    // 需要批量处理的审批id列表
    private List<String> reviewIdList;

    /**
     * 需要批量执行的操作结果
     * REJECTED 拒绝
     * APPROVED 通过
     */
    private String reviewResult;

    private String reviewStatus;
//    private List<String> reviewStatusList;

    private String currentReviewMemberId;

    private List<String> memberIdList;
}
