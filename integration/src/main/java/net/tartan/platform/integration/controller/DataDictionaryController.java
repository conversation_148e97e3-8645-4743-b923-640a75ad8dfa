package net.tartan.platform.integration.controller;

import lombok.extern.slf4j.Slf4j;
import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.integration.entity.DataDictionary;
import net.tartan.platform.integration.entity.DataDictionaryRelation;
import net.tartan.platform.integration.service.DataDictionaryRelationService;
import net.tartan.platform.integration.service.DataDictionaryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * dataDictionary 数据字典
 */
@Slf4j
@RestController
@RequestMapping("dataDictionary")
public class DataDictionaryController {
    @Autowired
    private DataDictionaryService dataDictionaryService;
    @Autowired
    private DataDictionaryRelationService dataDictionaryRelationService;

    @PostMapping("list")
    public CommonResult deviceList(@RequestBody(required = false) DataDictionary dataDictionary) {
        List<DataDictionary> dictionaries = dataDictionaryService.list(dataDictionary);
        return CommonResult.success(dictionaries);
    }
    @PostMapping("relationList")
    public CommonResult relationList(@RequestBody(required = false) DataDictionaryRelation dataDictionaryRelation) {
        List<DataDictionaryRelation> dictionaries = dataDictionaryRelationService.relationList(dataDictionaryRelation);
        return CommonResult.success(dictionaries);
    }

    @PostMapping("add")
    public CommonResult addDevice(@RequestBody @Valid DataDictionary dataDictionary) {
        dataDictionaryService.addDevice(dataDictionary);
        return CommonResult.success();
    }

    @PostMapping("update")
    public CommonResult updateDevice(@RequestBody @Valid DataDictionary dataDictionary) {
        dataDictionaryService.updateById(dataDictionary);
        return CommonResult.success();
    }

    @PostMapping("delete")
    public CommonResult deleteDevice(@RequestBody @Valid DataDictionary dataDictionary) {
        dataDictionaryService.removeById(dataDictionary.getId());
        return CommonResult.success();
    }
}
