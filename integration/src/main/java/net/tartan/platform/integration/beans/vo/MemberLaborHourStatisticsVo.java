package net.tartan.platform.integration.beans.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MemberLaborHourStatisticsVo {

    private Long projectId;
    /**
     * 项目名
     */
    private String projectName;
    /**
     * 项目编号
     */
    private String projectNumber;

    /**
     * 项目经理的memberId
     * 与member_info的member_id关联
     */
    private String projectManagerId;

    private String projectManagerName;

    private String directorUserId;

    /**
     * 项目状态，0 -> 进行中， 1 -> 已结项
     */
    private Integer projectStatus;


    /**
     * 提交人姓名
     */
    private String submitUserName;

    /**
     * 提交人的memberId
     */
    private String submitMemberId;

    private String departmentName;

    private BigDecimal costHour;

    private BigDecimal totalCostHour;

    private String percentHour;


}
