package net.tartan.platform.integration.entity.demandOrder;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 需求单详情列表
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DemandOrderInfoDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "demand_detail_id", type = IdType.AUTO)
    private Long demandDetailId;

    private Long demandId;

    /**
     * 物品名
     */
    private String itemName;

    /**
     * 仪器类型
     */
    private Long deviceType;

    /**
     * 序列号列表
     */
//    private List<String> serialNumberList;
    private String serialNumberListStr;

    private Integer requireAmount; // 需求数量
    private Integer actualAmount; // 实发数量
    private String note;

    // T 表示原始需求， F 表示实际响应的需求
    private String isOriginNeeds;
}
