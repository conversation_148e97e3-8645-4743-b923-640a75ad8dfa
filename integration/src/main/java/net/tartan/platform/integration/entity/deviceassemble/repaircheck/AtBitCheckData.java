package net.tartan.platform.integration.entity.deviceassemble.repaircheck;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class AtBitCheckData extends BaseCheckData {
    private List<CheckDataModel> atBitChecksList;

    public AtBitCheckData init() {
        AtBitCheckData atBitCheckData = new AtBitCheckData();

        atBitCheckData.setAtBitChecksList(atBitChecksListInit());

        //初始化故障诊断
        atBitCheckData.setFaultDiagnosis(new FaultDiagnosis().init());
        return atBitCheckData;
    }

    public List<CheckDataModel> atBitChecksListInit() {
        List<CheckDataModel> checkDataModelList = new ArrayList<>();

        CheckDataModel checkDataModel0 = new CheckDataModel();
        checkDataModel0.setName("At bit Battery Vo ltage 近钻头电池电压");
        checkDataModel0.setValue("~20v(HV) or ~10v(LV)");
        checkDataModel0.setIn("");
        checkDataModel0.setOut("");
        checkDataModelList.add(checkDataModel0);

        CheckDataModel checkDataModel1 = new CheckDataModel();
        checkDataModel1.setName("Incoming Gamma Counts 伽马值");
        checkDataModel1.setValue("~65");
        checkDataModel1.setIn("");
        checkDataModel1.setOut("");
        checkDataModelList.add(checkDataModel1);

        CheckDataModel checkDataModel2 = new CheckDataModel();
        checkDataModel2.setName("Gamma Source Counts 伽马源测试值");
        checkDataModel2.setValue(">100");
        checkDataModel2.setIn("");
        checkDataModel2.setOut("");
        checkDataModelList.add(checkDataModel2);

        CheckDataModel checkDataModel3 = new CheckDataModel();
        checkDataModel3.setName("Battery Plate #1 Condition 电池盖板1 状况");
        checkDataModel3.setValue("");
        checkDataModel3.setIn("");
        checkDataModel3.setOut("");
        checkDataModelList.add(checkDataModel3);

        CheckDataModel checkDataModel4 = new CheckDataModel();
        checkDataModel4.setName("Battery Plate #2 Condition 电池盖板2 状况");
        checkDataModel4.setValue("");
        checkDataModel4.setIn("");
        checkDataModel4.setOut("");
        checkDataModelList.add(checkDataModel4);

        CheckDataModel checkDataModel5 = new CheckDataModel();
        checkDataModel5.setName("Directional Plate Condition 电子模块盖板 状况");
        checkDataModel5.setValue("");
        checkDataModel5.setIn("");
        checkDataModel5.setOut("");
        checkDataModelList.add(checkDataModel5);

        CheckDataModel checkDataModel6 = new CheckDataModel();
        checkDataModel6.setName("Gamma Plate Condition 伽马模块盖板 状况");
        checkDataModel6.setValue("");
        checkDataModel6.setIn("");
        checkDataModel6.setOut("");
        checkDataModelList.add(checkDataModel6);

        CheckDataModel checkDataModel7 = new CheckDataModel();
        checkDataModel7.setName("Gamma wires condition 伽马连接线 状况");
        checkDataModel7.setValue("");
        checkDataModel7.setIn("");
        checkDataModel7.setOut("");
        checkDataModelList.add(checkDataModel7);

        CheckDataModel checkDataModel8 = new CheckDataModel();
        checkDataModel8.setName("Communication port condition 通信口 状况");
        checkDataModel8.setValue("");
        checkDataModel8.setIn("");
        checkDataModel8.setOut("");
        checkDataModelList.add(checkDataModel8);

        CheckDataModel checkDataModel9 = new CheckDataModel();
        checkDataModel9.setName("EC module matches Voltage status\nEC模块和电压匹配(YES/NO)");
        checkDataModel9.setValue("");
        checkDataModel9.setIn("");
        checkDataModel9.setOut("");
        checkDataModelList.add(checkDataModel9);

        CheckDataModel checkDataModel10 = new CheckDataModel();
        checkDataModel10.setName("*Receiver status\n* 接收器状态(YES/NO)");
        checkDataModel10.setValue("");
        checkDataModel10.setIn("");
        checkDataModel10.setOut("");
        checkDataModelList.add(checkDataModel10);

        CheckDataModel checkDataModel11 = new CheckDataModel();
        checkDataModel11.setName("*Gap sub status * 绝缘短节状态(YES/NO)");
        checkDataModel11.setValue("");
        checkDataModel11.setIn("");
        checkDataModel11.setOut("");
        checkDataModelList.add(checkDataModel11);

        CheckDataModel checkDataModel12 = new CheckDataModel();
        checkDataModel12.setName("Ceramic ring Resistance 陶瓷环电阻");
        checkDataModel12.setValue("New (>1MΩ)\nUsed (>1KΩ)");
        checkDataModel12.setIn("");
        checkDataModel12.setOut("");
        checkDataModelList.add(checkDataModel12);

        CheckDataModel checkDataModel13 = new CheckDataModel();
        checkDataModel13.setName("Transmitting Vol");
        checkDataModel13.setValue("发射电压");
        checkDataModel13.setIn("");
        checkDataModel13.setOut("");
        checkDataModelList.add(checkDataModel13);

        CheckDataModel checkDataModel14 = new CheckDataModel();
        checkDataModel14.setName("Transmitting Cycle");
        checkDataModel14.setValue("发射周期");
        checkDataModel14.setIn("");
        checkDataModel14.setOut("");
        checkDataModelList.add(checkDataModel14);

        return checkDataModelList;
    }
}
