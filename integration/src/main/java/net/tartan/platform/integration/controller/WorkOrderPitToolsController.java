package net.tartan.platform.integration.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.common.beans.ResultCode;
import net.tartan.platform.common.enums.EnumPitToolsWorkOrderType;
import net.tartan.platform.integration.beans.dto.pitTools.WorkOrderPitToolsDto;
import net.tartan.platform.integration.beans.query.OaToolQuery;
import net.tartan.platform.integration.beans.query.WorkOrderPitToolsQuery;
import net.tartan.platform.integration.beans.vo.WorkOrderPitToolsDetailResponse;
import net.tartan.platform.integration.beans.vo.WorkOrderPitToolsVo;
import net.tartan.platform.integration.entity.PitToolsRepairTypeModel;
import net.tartan.platform.integration.entity.deviceassemble.PitToolsDeviceAssembleRecord;
import net.tartan.platform.integration.entity.WorkOrderPitTools;
import net.tartan.platform.integration.exception.BusinessException;
import net.tartan.platform.integration.service.pitToolsService.IWorkOrderPitToolsService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;


/**
 * <p>
 * 井下工具的工单信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-29
 */
@RestController
@RequestMapping("/workOrderPitTools")
public class WorkOrderPitToolsController {
    @Autowired
    IWorkOrderPitToolsService workOrderPitToolsService;

    //获取oa中钻具通知单的基础信息
    @PostMapping("oa/getBaseInfoList")
    public CommonResult getOaBaseInfo(@RequestBody(required = false) OaToolQuery query) {
        if (query == null) {
            query = new OaToolQuery();
        }
        return CommonResult.success(workOrderPitToolsService.selectBaseInfoByQuery(query));
    }

    //根据钻具通知单的id 查询出里面的仪器详情列表
    @PostMapping("oa/getDeviceList")
    public CommonResult getDeviceList(@RequestParam("no") String no) {
        if(StringUtils.isEmpty(no)){
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        return CommonResult.success(workOrderPitToolsService.selectToolListById(no));
    }

    @GetMapping("info")
    public CommonResult info(@RequestParam("pitToolsId") long pitToolsId) {
        WorkOrderPitToolsVo workOrderPitToolsVo = workOrderPitToolsService.info(pitToolsId);
        return CommonResult.success(workOrderPitToolsVo);
    }
    @PostMapping("list/{current}/{size}")
    public CommonResult list(@PathVariable long current,
                             @PathVariable long size,
                             @RequestBody(required = false) WorkOrderPitToolsQuery query) {
        IPage<WorkOrderPitToolsDetailResponse> page = new Page<>(current, size);
        if (query == null) {
            query = new WorkOrderPitToolsQuery();
        }
        workOrderPitToolsService.list(page, query);
        return CommonResult.success(page);
    }

    @PostMapping("add")
    public CommonResult add(@RequestBody @Valid WorkOrderPitToolsDto orderPitToolsDto) {
        Long pitToolsId = workOrderPitToolsService.add(orderPitToolsDto);
        WorkOrderPitToolsVo workOrderPitToolsVo = workOrderPitToolsService.info(pitToolsId);
        return CommonResult.success(workOrderPitToolsVo);
    }

    @PostMapping("update")
    public CommonResult update(@RequestBody @Valid WorkOrderPitToolsVo orderPitToolsVo) {
        if (orderPitToolsVo.getPitToolsId() == null) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        workOrderPitToolsService.update(orderPitToolsVo);
        WorkOrderPitToolsVo workOrderPitToolsVo = workOrderPitToolsService.info(orderPitToolsVo.getPitToolsId());
        return CommonResult.success(workOrderPitToolsVo);
    }
    @PostMapping("delete")
    public CommonResult delete(@RequestBody @Valid WorkOrderPitToolsVo orderPitToolsVo) {
        if (orderPitToolsVo.getPitToolsId() == null) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        workOrderPitToolsService.delete(orderPitToolsVo.getPitToolsId());
        return CommonResult.success();
    }
    /**
     * 台账完成存档
     *
     * @param orderPitTools
     * @return
     */
    @PostMapping("finish")
    public CommonResult finish(@RequestBody @Valid WorkOrderPitTools orderPitTools) {
        if (orderPitTools.getPitToolsId() == null) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        workOrderPitToolsService.updateById(orderPitTools);
        return CommonResult.success();
    }

    /**EnumPitToolsWorkOrderType workOrderType
     * 仪器的组装与拆卸
     * @param record
     * @return
     */
    @PostMapping("assemble/{workOrderType}")
    public CommonResult assemble(@RequestBody @Valid PitToolsDeviceAssembleRecord record,
                                 @PathVariable EnumPitToolsWorkOrderType workOrderType) {
        if (record.getPitToolsId() == null) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        workOrderPitToolsService.assemble(record, workOrderType);
        return CommonResult.success();
    }

    /**
     * 检查更新的组装与拆卸信息，
     * 查看新装上的核心部件是否已经被使用了
     * @param record
     * @return
     */
    @PostMapping("assembleCheck")
    public CommonResult assembleCheck(@RequestBody @Valid PitToolsDeviceAssembleRecord record) {
        return CommonResult.success(workOrderPitToolsService.assembleCheck(record));
    }

//    /**
//     * 部件维修
//     * @param componentRepairPitToolsDto
//     * @return
//     */
//    @PostMapping("component/repair")
//    public CommonResult componentRepair(@RequestBody @Valid ComponentRepairPitToolsDto componentRepairPitToolsDto) {
//        workOrderPitToolsService.componentRepair(componentRepairPitToolsDto);
//        return CommonResult.success();
//    }

    @GetMapping("component/repairType")
    public CommonResult componentRepair() {
        PitToolsRepairTypeModel pitToolsRepairTypeModel = new PitToolsRepairTypeModel();
        return CommonResult.success(pitToolsRepairTypeModel);
    }

//    /**
//     * 工时统计
//     */
//    @PostMapping("labourHour")
//    public CommonResult getPitToolsLabourHour(@RequestBody PitToolsHourStatisticsQuery pitToolsHourStatisticsQuery) {
//        String year = pitToolsHourStatisticsQuery.getYear();
//        String memberId = pitToolsHourStatisticsQuery.getMemberId();
//        String startTime = pitToolsHourStatisticsQuery.getStartTime();
//        String endTime = pitToolsHourStatisticsQuery.getEndTime();
//        return CommonResult.success(workOrderPitToolsService.getPitToolsLabourHour(year, memberId, startTime, endTime));
//    }

    /**
     * 井下工单导出
     */
    @PostMapping("order/export")
    public CommonResult orderExport(@RequestBody WorkOrderPitToolsQuery query) {
        if (query == null|| query.getWorkOrderType() == null) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        workOrderPitToolsService.orderExportExcel(query);
        return CommonResult.success();
    }


}
