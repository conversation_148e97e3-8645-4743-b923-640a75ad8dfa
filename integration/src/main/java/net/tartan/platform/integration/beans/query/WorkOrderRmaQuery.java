package net.tartan.platform.integration.beans.query;

import lombok.Data;
import net.tartan.platform.common.enums.EnumRiskType;

/**
 * <p>
 * rma工单查询参数
 * </p>
 */
@Data
public class WorkOrderRmaQuery {

    private Long mwdId;

    private Long rmaId;

    /**
     * 工单单号
     */
    private String rmaNumber;

    /**
     * 创建部门名
     */
    private String departmentName;

    /**
     * 开始日期
     */
    private String startTime;

    /**
     * 结束日期
     */
    private String endTime;

    /**
     * 核心部件品名
     */
    private String invName;
    /**
     * 核心部件序列号
     */
    private String serialNumber;
    /**
     * 作业号
     */
    private String jobNumber;

    /**
     * 维修状态1：已完成，0：未完成，-1：滞留
     */
    private Integer finish;

    /**
     * 项目号（井号）
     */
    private String wellNumber;


    private String orderBy;

    private String orderType;

    private String receiveDate;
}

