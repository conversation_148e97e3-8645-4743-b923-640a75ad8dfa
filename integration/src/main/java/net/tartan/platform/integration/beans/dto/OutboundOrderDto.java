package net.tartan.platform.integration.beans.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 出库单信息表DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OutboundOrderDto {
    private Long id;
    // 领取单号
    private String orderId;

    // 仪器基础信息
    private Long deviceId;
    private Long deviceType;
    private String deviceTypeStr;
    private String invName;
    private String serialNumber;

    // 所有者
    private String owner;

    // 领取原因
    private String reason;
    // 领取人
    private String receiverName;
    // 领取时间
    private String receiveDate;
    // 核对人
    private String checkerName;
    // 核对时间
    private String checkDate;

    private Long mwdId;
    // mwd工单号
    private String mwdNumber;

    private String notes;

    /**
     * 返回原因
     */
    private String returnReason;

    private Date createTime;

    private Date updateTime;

    private List<OutboundOrderDto> batchList;
    private WorkOrderMwdDto workOrderMwdDto;
}
