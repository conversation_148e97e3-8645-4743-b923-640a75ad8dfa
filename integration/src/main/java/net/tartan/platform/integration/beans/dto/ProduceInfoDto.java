package net.tartan.platform.integration.beans.dto;

import lombok.*;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProduceInfoDto {

    private Long produceId;

    /**
     * 生产编号
     */
    private String produceCode;

    /**
     * 生产类型
     */
    private String produceType;

    /**
     * 父节点
     */
    private Long parentId;
    /**
     * 任务头结点
     */
    private Long ancestors;

    /**
     * 是否有子节点0：没有，1：有
     */
    private Integer leaf;

    /**
     * 生产名称
     */
    private String produceName;

    /**
     * 生产描述
     */
    private String produceDesc;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 负责人
     */
    private Long director;

    /**
     * 生产状态 0：未开始 1：已开始 2：已结束 3：拒收 4：中止 5：归档
     */
    private Integer produceStatus;

    private Double progress;

    /**
     * 单据编号
     */
    private String receiptCode;

    /**
     * 计划开始日期
     */
    private String planStartDate;

    /**
     * 计划结束日期
     */
    private String planEndDate;

    /**
     * 实际开始日期
     */
    private String actualStartDate;

    /**
     * 实际结束日期
     */
    private String actualEndDate;

    /**
     * 任务模板id
     */
    private String templateId;
}
