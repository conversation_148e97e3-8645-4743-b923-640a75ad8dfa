package net.tartan.platform.integration.beans.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProjectInfoDto {

    private Long id;

    /**
     * 项目明
     */
    private String projectName;

    /**
     * 项目编号
     */
    private String projectNumber;

    /**
     * 项目状态，0 -> 进行中， 1 -> 已结项
     */
    private Integer projectStatus;

    /**
     * 项目经理的memberId
     * 与member_info的member_id关联
     */
    private String projectManagerId;

    /**
     * 总监
     * （审批的第二个节点）
     */
    private String projectDirectorId;

    /**
     * 开始时间
     */
    private String startDate;

    /**
     * 结束时间
     */
    private String endDate;

    /**
     * 创建人姓名
     */
    private String createBy;

    /**
     * 最后编辑人姓名
     */
    private String lastUpdateBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    private List<String> projectMemberList;
}
