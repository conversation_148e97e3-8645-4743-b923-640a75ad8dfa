package net.tartan.platform.integration.entity.process;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 工艺流程详情
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ProcessFlowDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "flow_id", type = IdType.AUTO)
    private Long flowId;

    /**
     * 工艺流程卡id
     */
    private Long processFlowId;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 工序名称
     */
    private String flowName;

    /**
     * 工序描述
     */
    private String flowDesc;

    /**
     * 工序要求数量
     */
    private Integer processRequiredQuantity;

    /**
     * 结束日期
     */
    private String endDate;

    /**
     * 标准工时
     */
    private Double standardHours;

    /**
     * 准备工时
     */
    private Double prepareHours;

    /**
     * 是否外包0：不外包 1：外包
     */
    private Integer outsource;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


}
