package net.tartan.platform.integration.beans.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.enums.EnumDeviceUseStatus;
import net.tartan.platform.common.enums.EnumRiskType;
import net.tartan.platform.common.enums.EnumRubberType;
import net.tartan.platform.integration.entity.Device;

import java.math.BigDecimal;

/**
 * 仪器统计基础信息
 */
@Data
@NoArgsConstructor
public class DeviceStatisticsDetailVo {
    /**
     * 仪器id
     */
    private Long deviceId;
    /**
     * 仪器品名
     */
    private String invName;
    /**
     * 序列号
     */
    private String serialNumber;

    /**
     * 仪器类型
     */
    private Long deviceType;
    private String deviceTypeStr;

    /**
     * 最高温度
     */
    private BigDecimal maxBht;

    /**
     * 总循环时间
     */
    private BigDecimal totalCirculateHrs;

    /**
     * 总入井时间
     */
    private BigDecimal totalInWellHrs;

    /**
     * 服役次数
     */
    private Integer serveTotalCount;

    /**
     * 仪器风险类型
     */
    private EnumRiskType riskType;

    /**
     * 最后创建的工单号
     */
    private String lastMwdNumber;

    /**
     * 对应mwdNumber的mwdId
     */
    private Long mwdId;

    //螺杆部件额外信息
    //万向轴壳体
    /**
     * 弯度类型:单弯 1 / 可调 0
     */
    private Integer curve;

    /**
     * 弯度
     */
    private BigDecimal angle;

    /**
     * 扶正器描述
     */
    private String stbDescribe;

    /**
     * 耐温
     */
    private BigDecimal endureTemperature;

    /**
     * 压差
     */
    private BigDecimal pressureSub;

    /**
     * 扭矩
     */
    private BigDecimal torque;

    /**
     * 泥浆类型
     */
    private String mudType;

    /**
     * 最大外径
     */
    private BigDecimal odMax;

    /**
     * 仪器的使用状态（井下工具）
     */
    private EnumDeviceUseStatus useStatus;


    /**
     * 井下工具的信息回传
     */
    private Device pitToolsDevice;

    //仪器状态
    private Long status;
    private String statusStr;
}
