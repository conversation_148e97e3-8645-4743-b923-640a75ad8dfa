package net.tartan.platform.integration.entity.dailyreport.RSS;


import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.constant.PfConstant;
import net.tartan.platform.integration.entity.dailyreport.MwdDailyActivity;
import net.tartan.platform.integration.entity.dailyreport.MwdWorkSummary;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = PfConstant.RSS_DAILY_REPORT)
public class RSSDailyReport implements Serializable {

    @Id
    @JsonIgnore
    private String reportId;

    @Transient
    private Long id;

    /**
     * 报告类型
     */
    @Transient
    private String reportType;

    /**
     * 报告井id
     */
    @NotNull
    private Long jobId;
    private String jobNumber;
    private String jobType;

    /**
     * 工程师
     */
    private String engineer;

    /**
     * 日期
     */
    @NotEmpty
    private String date;

    /**
     * 仪器箱
     */
    private String kit;

    /**
     * 开始日期
     */
    private String startDate;

    /**
     * 施工天数
     */
    private Integer constructionDays;

    /**
     * 负责人
     */
    private String cellManager;

    private String dd1;

    private String dd2;

    private String mwd1;

    private String mwd2;

    /**
     * 趟次
     */
    private Integer run;

    /**
     * BPA使用时间
     */
    private BigDecimal bpaHrs;

    /**
     * BPA SN
     */
    private String bpaSn;

    /**
     * 开始井深
     */
    private BigDecimal startDepth;

    /**
     * 结束井深
     */
    private BigDecimal endDepth;

    /**
     * 泥浆数据
     */
    private String mudData;

    /**
     * 泥浆类型
     */
    private String mudType;

    /**
     * 泥浆密度
     */
    private BigDecimal mudDen;

    /**
     * 粘度
     */
    private BigDecimal viscosity;

    /**
     * 破乳电压
     */
    private BigDecimal emulsionbreakingVoltage;

    /**
     * HTHP
     */
    private BigDecimal hthp;

    /**
     * 泥饼
     */
    private BigDecimal filterCake;

    /**
     * 油水比
     */
    private String oilWaterRatio;

    /**
     * 固相含量
     */
    private BigDecimal solidContent;

    /**
     * 含沙量
     */
    private BigDecimal sandContent;

    /**
     * 含油量
     */
    private String oilContent;

    /**
     * 酸碱度
     */
    private BigDecimal ph;

    /**
     * 塑性粘度
     * plastic viscosity
     */
    private BigDecimal pv;

    /**
     * 动切力
     */
    private BigDecimal vp;

    /**
     * 初终切
     */
    private String cjp;

    /**
     * 六速
     */
    private String ls;

    /**
     * 氯根 lg
     */
    private BigDecimal lg;

    /**
     * 失水
     */
    private BigDecimal fl;

    /**
a     * 温度
     */
    private BigDecimal temp;

    /**
     * 总修正角
     * TAC
     */
    private BigDecimal tac;
    /**
     * 伽马零长
     * gammaDist
     */
    private BigDecimal gammaDist;

    /**
     * 近钻零长
     * atBitDist
     */
    private BigDecimal atBitDist;

    /**
     * 测斜零长
     * bitElecCDist
     */
    private BigDecimal bitElecCDist;

    /**
     * 电池电压
     * batv
     */
    private BigDecimal batv;

    /**
     * 脉宽
     * Plsw
     */
    private BigDecimal plsw;

    /**
     * 脉冲高度
     * PlsH
     */
    private BigDecimal plsH;

    /**
     * 磁倾角
     * Ndip
     */
    private BigDecimal ndip;

    /**
     * 磁场强度
     * Nmag
     */
    private BigDecimal nmag;

    /**
     * 重力场
     * Gt
     */
    private BigDecimal gt;

    /**
     * 24小时计划
     */
    private String plan;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 工具列表
     */
    private List<RSSTool> toolList;

    /**
     * OOS列表
     */
    private List<RSSOos> oosList;

    /**
     * 活动列表
     */
    private List<MwdDailyActivity> activityList;

    /**
     * 工作总结
     */
    private List<MwdWorkSummary> workSummaryList;

    private static final long serialVersionUID = 1L;

}
