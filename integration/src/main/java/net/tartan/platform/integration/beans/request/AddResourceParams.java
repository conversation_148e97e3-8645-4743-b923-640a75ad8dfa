package net.tartan.platform.integration.beans.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class AddResourceParams implements Serializable {
    /**
     * 资源分类ID
     */
    @NotNull
    private Long categoryId;

    /**
     * 资源名称
     */
    @NotEmpty
    private String name;

    /**
     * 资源URL
     */
    @NotEmpty
    private String url;

    /**
     * 描述
     */
    private String description;
}
