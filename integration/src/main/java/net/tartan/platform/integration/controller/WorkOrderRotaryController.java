//package net.tartan.platform.integration.controller;
//
//import com.baomidou.mybatisplus.core.metadata.IPage;
//import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
//import net.tartan.platform.common.beans.CommonResult;
//import net.tartan.platform.common.beans.ResultCode;
//import net.tartan.platform.integration.beans.vo.WorkOrderMwdDetailResponse;
//import net.tartan.platform.integration.beans.vo.WorkOrderMwdVo;
//import net.tartan.platform.integration.entity.WorkOrderMwd;
//import net.tartan.platform.integration.entity.deviceassemble.MwdDeviceAssembleRecord;
//import net.tartan.platform.integration.exception.BusinessException;
//import org.springframework.web.bind.annotation.*;
//
//import javax.validation.Valid;
//
///**
// * <p>
// * mwd工单信息表 前端控制器
// * </p>
// *
// * <AUTHOR>
// * @since 2022-05-09
// */
//@RestController
//@RequestMapping("/workOrderMwd")
//public class WorkOrderRotaryController {
//
//    @PostMapping("list/{current}/{size}")
//    public CommonResult list(@PathVariable long current,
//                             @PathVariable long size,
//                             @RequestBody(required = false) WorkOrderRotaryQuery query) {
//        IPage<WorkOrderMwdDetailResponse> page = new Page<>(current, size);
//        if (query == null) {
//            query = new WorkOrderRotaryQuery();
//        }
//        workOrderRotaryService.list(page, query);
//        return CommonResult.success(page);
//    }
//
//    @PostMapping("add")
//    public CommonResult add(@RequestBody @Valid WorkOrderRotaryDTO orderRotaryDTO) {
//        if (orderRotaryDTO.getRotaryNumber() == null) {
//            throw new BusinessException(ResultCode.PARAMS_ERROR);
//        }
//        Long rotaryNumber = workOrderRotaryService.add(orderRotaryDTO);
//        WorkOrderRotaryVO workOrderRotaryVO = workOrderRotaryService.info(rotaryNumber);
//        return CommonResult.success(workOrderRotaryVO);
//    }
//
//    @GetMapping("info")
//    public CommonResult info(@RequestParam("rotaryId") long rotaryId) {
//        WorkOrderRotaryVO workOrderRotaryVO = workOrderRotaryService.info(rotaryId);
//        return CommonResult.success(workOrderRotaryVO);
//    }
//
//    @PostMapping("getSerialNumber")
//    public CommonResult serialNumberQuery(@RequestParam("serialNumber") String serialNumber) {
//
//        return CommonResult.success(workOrderRotaryService.selectSerialNumberQuery(serialNumber));
//    }
//
//    @PostMapping("update")
//    public CommonResult update(@RequestBody @Valid WorkOrderRotaryVO workOrderRotaryVO) {
//        if (workOrderRotaryVO.getRotaryId() == null) {
//            throw new BusinessException(ResultCode.PARAMS_ERROR);
//        }
//        workOrderRotaryService.update(orderMwdVo);
//        WorkOrderRotaryVO orderRotaryVO = workOrderRotaryService.info(orderMwdVo.getMwdId());
//        return CommonResult.success(orderRotaryVO);
//    }
//
//    @PostMapping("delete")
//    public CommonResult delete(@RequestBody WorkOrderRotaryQuery query) {
//        if (query == null || query.getRotaryId() == null) {
//            throw new BusinessException(ResultCode.PARAMS_ERROR);
//        }
//        workOrderRotaryService.delete(query.getRotaryId());
//        return CommonResult.success();
//    }
//
//    @PostMapping("finish")
//    public CommonResult finish(@RequestBody @Valid WorkOrderRotary workOrderRotary) {
//        if (workOrderRotary.getRotaryId() == null) {
//            throw new BusinessException(ResultCode.PARAMS_ERROR);
//        }
//        workOrderRotaryService.updateFinish(workOrderRotary);
//        return CommonResult.success();
//    }
//
//    /**
//     * 检查更新的组装与拆卸信息，
//     * 查看新装上的部件是否已经被使用了
//     * @param record
//     * @return
//     */
//    @PostMapping("assembleCheck")
//    public CommonResult assembleCheck(@RequestBody @Valid RotaryDeviceAssembleRecord record) {
//        return CommonResult.success(workOrderRotaryService.assembleCheck(record));
//    }
//
//    //
//
//
//
//
//
//}
