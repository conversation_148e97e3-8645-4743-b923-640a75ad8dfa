package net.tartan.platform.integration.entity.dailyreport;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * mwd_ri_procedures_action
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MwdRiProceduresAction{
    private Long id;

    /**
     * rig in procedures report id
     */
    private Long ripReportId;

    /**
     * 编号
     */
    private String serialNumber;

    /**
     * mwd操作人
     */
    private String action;

    /**
     * 日期yyyy-MM-dd
     */
    private String date;

    private String initial;
}
