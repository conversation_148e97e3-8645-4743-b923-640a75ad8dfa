package net.tartan.platform.integration.beans.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DemandOrderInfoDetailVo {

    /**
     * 主键id
     */
    private Long demandDetailId;

    private Long demandId;

    /**
     * 物品名
     */
    private String itemName;

    /**
     * 仪器类型
     */
    private Long deviceType;

    /**
     * 序列号列表
     */
//    private List<String> serialNumberList;
    private String serialNumberListStr;

    private Integer requireAmount; // 需求数量
    private Integer actualAmount; // 实发数量
    private String note;
}
