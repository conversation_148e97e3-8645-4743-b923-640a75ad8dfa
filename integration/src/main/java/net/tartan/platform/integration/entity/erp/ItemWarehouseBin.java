package net.tartan.platform.integration.entity.erp;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 存货余额明细信息/CHT/存貨餘額明細資料/ENU/Inventory Balance Details
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ITEM_WAREHOUSE_BIN")
public class ItemWarehouseBin implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId("ITEM_WAREHOUSE_BIN_ID")
    private String itemWarehouseBinId;

    /**
     * 库存数量
     */
    @TableField("INVENTORY_QTY")
    private Double inventoryQty;

    /**
     * 版本号，不要随意更改
     */
    @TableField("Version")
    private byte[] version;

    /**
     * 最后修改日期
     */
    @TableField("LastModifiedDate")
    private Date lastmodifieddate;

    private String itemCode;

    @TableField("LOT_CODE")
    private String lotCode;

    private String serialNumber;
    /**
     * 仓库
     */
    @TableField("WAREHOUSE_ID")
    private String warehouseId;
    private String warehouseCode;

    @TableField("ITEM_SPECIFICATION")
    private String itemSpecification;

    /**
     * 库位
     */
    @TableField("BIN_ID")
    private String binId;
    private String binCode;

//
//    /**
//     * 关联部门
//     */
//    @TableField("Owner_Dept")
//    private String ownerDept;
//
//    /**
//     * 关联员工
//     */
//    @TableField("Owner_Emp")
//    private String ownerEmp;
//
//    /**
//     * 主键
//     */
//    @TableId("ITEM_WAREHOUSE_BIN_ID")
//    private String itemWarehouseBinId;
//
//    /**
//     * 库存数量
//     */
//    @TableField("INVENTORY_QTY")
//    private Double inventoryQty;
//
//    /**
//     * 第二数量
//     */
//    @TableField("SECOND_QTY")
//    private Double secondQty;
//
//    /**
//     * 上次盘点日
//     */
//    @TableField("LAST_COUNT_DATE")
//    private LocalDateTime lastCountDate;
//
//    /**
//     * 最后入库日
//     */
//    @TableField("LAST_RECEIPT_DATE")
//    private LocalDateTime lastReceiptDate;
//
//    /**
//     * 最后出库日
//     */
//    @TableField("LAST_ISSUE_DATE")
//    private LocalDateTime lastIssueDate;
//
//    /**
//     * 备注
//     */
//    @TableField("REMARK")
//    private String remark;
//
//    /**
//     * 品号
//     */
//    @TableField("ITEM_ID")
//    private String itemId;
//
//    /**
//     * 批号
//     */
//    @TableField("ITEM_LOT_ID")
//    private String itemLotId;
//
//    /**
//     * 特征码
//     */
//    @TableField("ITEM_FEATURE_ID")
//    private String itemFeatureId;
//
//    /**
//     * 仓库
//     */
//    @TableField("WAREHOUSE_ID")
//    private String warehouseId;
//
//    /**
//     * 库位
//     */
//    @TableField("BIN_ID")
//    private String binId;
//
//    @TableField("LAST_QC_DATE")
//    private LocalDateTime lastQcDate;
//
//    /**
//     * 创建日期
//     */
//    @TableField("CreateDate")
//    private LocalDateTime createdate;
//
//    /**
//     * 最后修改日期
//     */
//    @TableField("LastModifiedDate")
//    private LocalDateTime lastmodifieddate;
//
//    /**
//     * 修改日期
//     */
//    @TableField("ModifiedDate")
//    private LocalDateTime modifieddate;
//
//    /**
//     * 创建者
//     */
//    @TableField("CreateBy")
//    private String createby;
//
//    /**
//     * 最后修改者
//     */
//    @TableField("LastModifiedBy")
//    private String lastmodifiedby;
//
//    /**
//     * 修改者
//     */
//    @TableField("ModifiedBy")
//    private String modifiedby;
//
//    /**
//     * 版本号，不要随意更改
//     */
//    @TableField("Version")
//    private LocalDateTime version;
//
//    /**
//     * 自定义字段0
//     */
//    @TableField("UDF001")
//    private Double udf001;
//
//    /**
//     * 自定义字段1
//     */
//    @TableField("UDF002")
//    private Double udf002;
//
//    /**
//     * 自定义字段2
//     */
//    @TableField("UDF003")
//    private Double udf003;
//
//    /**
//     * 自定义字段3
//     */
//    @TableField("UDF011")
//    private Double udf011;
//
//    /**
//     * 自定义字段4
//     */
//    @TableField("UDF012")
//    private Double udf012;
//
//    /**
//     * 自定义字段5
//     */
//    @TableField("UDF013")
//    private Double udf013;
//
//    /**
//     * 自定义字段6
//     */
//    @TableField("UDF021")
//    private String udf021;
//
//    /**
//     * 自定义字段7
//     */
//    @TableField("UDF022")
//    private String udf022;
//
//    /**
//     * 自定义字段8
//     */
//    @TableField("UDF023")
//    private String udf023;
//
//    /**
//     * 自定义字段9
//     */
//    @TableField("UDF024")
//    private String udf024;
//
//    /**
//     * 自定义字段10
//     */
//    @TableField("UDF025")
//    private String udf025;
//
//    /**
//     * 自定义字段11
//     */
//    @TableField("UDF026")
//    private String udf026;
//
//    /**
//     * 自定义字段12
//     */
//    @TableField("UDF041")
//    private LocalDateTime udf041;
//
//    /**
//     * 自定义字段13
//     */
//    @TableField("UDF042")
//    private LocalDateTime udf042;
//
//    /**
//     * 自定义字段14
//     */
//    @TableField("UDF051")
//    private String udf051;
//
//    /**
//     * 自定义字段15
//     */
//    @TableField("UDF052")
//    private String udf052;
//
//    /**
//     * 自定义字段16
//     */
//    @TableField("UDF053")
//    private String udf053;
//
//    /**
//     * 自定义字段17
//     */
//    @TableField("UDF054")
//    private String udf054;
//
//    /**
//     * 附件
//     */
//    @TableField("Attachments")
//    private String attachments;
//
//    /**
//     * 表单所在的流程实例的编号
//     */
//    @TableField("ProcessInstanceId")
//    private String processinstanceid;
//
//    /**
//     * 单据状态属性
//     */
//    @TableField("ApproveStatus")
//    private String approvestatus;
//
//    /**
//     * 修改日期
//     */
//    @TableField("ApproveDate")
//    private LocalDateTime approvedate;
//
//    /**
//     * 修改人
//     */
//    @TableField("ApproveBy")
//    private String approveby;
//
//    @TableField("Owner_Org_RTK")
//    private String ownerOrgRtk;
//
//    @TableField("Owner_Org_ROid")
//    private String ownerOrgRoid;
//
//    @TableField("BO_ID_RTK")
//    private String boIdRtk;
//
//    @TableField("BO_ID_ROid")
//    private String boIdRoid;
}
