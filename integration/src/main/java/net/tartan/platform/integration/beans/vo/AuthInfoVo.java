package net.tartan.platform.integration.beans.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.integration.entity.Department;
import net.tartan.platform.integration.entity.MemberInfo;
import net.tartan.platform.integration.entity.Role;
import net.tartan.platform.integration.entity.UserInfo;

import java.util.List;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AuthInfoVo {
    private UserInfo accountInfo;
    private MemberInfo memberInfo;
    private Department department;
    private List<Role> roleList;
    private List<RouterVo> menuList;
    private List<String> btnPermissionList;
}
