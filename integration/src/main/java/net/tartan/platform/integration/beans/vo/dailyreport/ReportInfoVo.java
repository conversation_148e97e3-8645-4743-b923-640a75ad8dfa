package net.tartan.platform.integration.beans.vo.dailyreport;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.enums.EnumReportType;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ReportInfoVo {
    private String date;
    private String run;
    @JsonIgnore
    private String key;
    private long lastModified;
    private EnumReportType reportType;
    private int status;
}
