package net.tartan.platform.integration.entity.dailyreport;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.constant.PfConstant;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * mwd_run_report
 *
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Document(collection = PfConstant.MWD_RUN_REPORT)
public class MwdRunReport implements Serializable {
    @Transient
    private Long id;

    @Id
    @JsonIgnore
    private String reportId;

    /**
     * cover id
     */
    @NotNull
    private Long jobId;
    private String jobNumber;
    /**
     * 趟次
     */
    @NotNull
    private Integer run;

    /**
     * 井眼尺寸mm
     */
    private BigDecimal holeSize;

    /**
     * 排量m3/min
     */
    private BigDecimal flowRate;

    /**
     * 泥浆类型
     */
    private String mudType;

    /**
     * 泥浆密度
     */
    private BigDecimal mudWeight;

    /**
     * 泥浆粘度
     */
    private BigDecimal mudViscosity;

    /**
     * 含砂/固相
     */
    private String sandSolids;

    /**
     * 入井时间
     */
    private String dateIn;

    /**
     * 出井时间
     */
    private String dateOut;

    /**
     * 入井深度
     */
    private BigDecimal depthIn;

    /**
     * 出井深度
     */
    private BigDecimal depthOut;

    /**
     * 脉宽
     */
    private BigDecimal pulseWidth;

    /**
     * 限流环内径
     */
    private BigDecimal orificeId;

    /**
     * 蘑菇头外径
     */
    private BigDecimal popTipOd;

    /**
     * 无磁内径
     */
    private BigDecimal mwdMonelId;

    /**
     * 无磁外径
     */
    private BigDecimal mwdMonelOd;

    /**
     * 设计方位
     */
    private BigDecimal driftAzimuth;

    /**
     * 磁偏角
     */
    private BigDecimal magDec;

    /**
     * 伽马零长
     */
    private BigDecimal distBitGamma;

    /**
     * 测斜零长
     */
    private BigDecimal distBitElec;

    /**
     * 探管内角差
     */
    private BigDecimal imoDeg;

    /**
     * 近钻头零长
     */
    private BigDecimal atBitDist;

    /**
     * 钻头厂家
     */
    private String bitMfg;

    /**
     * 钻头类型
     */
    private String insertToothPdc;

    /**
     * 钻头型号
     */
    private String bitModel;

    /**
     * 螺杆厂家
     */
    private String motorMfg;

    /**
     * 螺杆尺寸
     */
    private String motorSize;

    /**
     * 定子/转子
     */
    private String mLobesStage;

    /**
     * 度数
     */
    private BigDecimal deg;

    /**
     * 钻头
     */
    private String bitReason;

    /**
     * 螺杆
     */
    private String motorReason;

    /**
     * 仪器
     */
    private String mwdReason;

    /**
     * 其他
     */
    private String otherReason;

    /**
     * 地面信号
     */
    private BigDecimal surfData;

    /**
     * 地面信号
     */
    private BigDecimal btmData;

    /**
     * 通电
     */
    private BigDecimal electricalData;

    /**
     * 循环
     */
    private BigDecimal circulatingData;

    /**
     * 纯钻
     */
    private BigDecimal drillingData;

    /**
     * 概述
     */
    private List<MwdRunSummary> summaryList;

    /**
     * 工具
     */
    private List<MwdTool> toolList;

    private static final long serialVersionUID = 1L;

    // 新增
    private Double maxBht;
    private Double ss;
}
