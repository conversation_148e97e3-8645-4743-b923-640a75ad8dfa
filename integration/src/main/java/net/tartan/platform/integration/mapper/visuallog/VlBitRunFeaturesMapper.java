package net.tartan.platform.integration.mapper.visuallog;

import net.tartan.platform.integration.entity.visuallog.VlBitRunFeatures;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-10-08
 */
public interface VlBitRunFeaturesMapper extends BaseMapper<VlBitRunFeatures> {

    List<VlBitRunFeatures> getListByRunIdList(@Param("runIdList") List<Long> runIdList, @Param("jobId") long jobId);
}
