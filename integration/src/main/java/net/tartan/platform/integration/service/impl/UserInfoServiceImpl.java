package net.tartan.platform.integration.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.tartan.platform.common.beans.ResultCode;
import net.tartan.platform.integration.beans.vo.UserAndRoleVo;
import net.tartan.platform.integration.beans.vo.UserInfoVo;
import net.tartan.platform.integration.entity.Department;
import net.tartan.platform.integration.entity.UserInfo;
import net.tartan.platform.integration.exception.BusinessException;
import net.tartan.platform.integration.mapper.UserInfoMapper;
import net.tartan.platform.integration.service.IMemberInfoService;
import net.tartan.platform.integration.service.IUserInfoRoleRelationService;
import net.tartan.platform.integration.service.IUserInfoService;
import net.tartan.platform.integration.service.JobUserRelationService;
import net.tartan.platform.integration.utils.CommonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.constraints.NotBlank;
import java.util.*;

/**
 * <p>
 * 账户信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-29
 */
@Service
public class UserInfoServiceImpl extends ServiceImpl<UserInfoMapper, UserInfo> implements IUserInfoService {

    @Autowired
    private UserInfoMapper userInfoMapper;
    @Autowired
    private IMemberInfoService memberService;
    @Autowired
    private PasswordEncoder passwordEncoder;
    @Autowired
    private IUserInfoRoleRelationService userInfoRoleRelationService;
    @Autowired
    private JobUserRelationService jobUserRelationService;

    @Override
    public List<UserAndRoleVo> listAll(UserAndRoleVo userInfo) {
        List<UserAndRoleVo> result = userInfoMapper.listAll(userInfo);
        List<UserAndRoleVo> re = CommonUtil.roleToList(result);
        return re;
    }

    @Override
    public List<UserInfoVo> listByRole(long roleId) {
        return userInfoMapper.listByRole(roleId);
    }

    @Override
    public List<UserInfoVo> listByMenuId(long menuId) {
        return userInfoMapper.listByMenId(menuId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(UserInfo userInfo) {
        @NotBlank String username = userInfo.getUsername();
        @NotBlank String password = userInfo.getPassword();
        final long memberId = userInfo.getMemberId();
        //查询账户是否存在
        final boolean existByUsername = this.isExistByUsername(username);
        if (existByUsername) {
            throw new BusinessException(ResultCode.ACCOUNT_EXIST);
        }
        //查询人员是否存在
        boolean memberExist = memberService.isExist(memberId);
        if (!memberExist) {
            throw new BusinessException(ResultCode.MEMBER_NOT_EXIST);
        }
        //查询人员是否已分配账号
        final boolean existByMemberId = this.isExistByMemberId(memberId);
        if (existByMemberId) {
            throw new BusinessException(ResultCode.MEMBER_HAVE_BEEN_ASSIGNED);
        }
        Date now = new Date();
        userInfo.setCreateTime(now);
        userInfo.setUpdateTime(now);
        userInfo.setPassword(passwordEncoder.encode(password));
        userInfoMapper.insert(userInfo);
    }

    @Override
    public boolean isExist(long userId) {
        LambdaQueryWrapper<UserInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserInfo::getUserId, userId);
        final Integer count = userInfoMapper.selectCount(queryWrapper);
        return count > 0;
    }

    public boolean isExistByUsername(String username) {
        LambdaQueryWrapper<UserInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserInfo::getUsername, username);
        final Integer count = userInfoMapper.selectCount(queryWrapper);
        return count > 0;
    }

    public boolean isExistByMemberId(long memberId) {
        LambdaQueryWrapper<UserInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserInfo::getMemberId, memberId);
        final Integer count = userInfoMapper.selectCount(queryWrapper);
        return count > 0;
    }

    @Override
    public List<Long> filter(List<Long> userIdList) {
        return userInfoMapper.filter(userIdList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(long userId) {
        //删除用户角色关系
        userInfoRoleRelationService.delete(userId);
        //删除用户作业关系
        jobUserRelationService.deleteByUser(userId);
        //删除用户
        userInfoMapper.deleteById(userId);
    }

    @Override
    public void batchDelete(List<Long> userIdList) {
        userIdList.forEach(this::delete);
    }

    @Override
    public boolean isAdmin(Long userId) {
        return userInfoRoleRelationService.isAdmin(userId);
    }

    @Override
    public List<Map<String, Objects>> listByOrgTree() {
        List result = new ArrayList();
        try {
            List<Department> companys = userInfoMapper.getAllCompanys();//获取所有公司
            List<Department> departments = userInfoMapper.getPrimaryDepartments();//获取所有公司下的一级部门
            List<Map<String, Objects>> userInfos = userInfoMapper.getUserAndDepartments();//获取系统中存在的用户及部门信息

            List<Map<String, Objects>> depList = new ArrayList();
            for (Department dep : departments) {
                //先找到该部门下所有的用户，放入集合中
                List childrens = new ArrayList();
                for (Map user : userInfos) {
                    if (dep.getPath().equals(user.get("path"))) {
                        childrens.add(user);
                    }
                }
                //如果集合不为空，即部门存在用戶
                if (!childrens.isEmpty()) {
                    Map temp_map = new HashMap();
                    temp_map.put("name", dep.getName());
                    temp_map.put("id", dep.getDepartmentId());
                    temp_map.put("path", dep.getPath());
                    temp_map.put("children", childrens);
                    depList.add(temp_map);
                }
            }
            //逻辑大体同上
            for (Department com : companys) {
                List childrens = new ArrayList();
                for (Map<String, Objects> map : depList) {
                    String path = map.get("path") + "";
                    if (com.getPath().equals(path.substring(0, 12))) {
                        childrens.add(map);
                    }
                }
                //如果集合不为空
                if (!childrens.isEmpty()) {
                    Map temp_map = new HashMap();
                    temp_map.put("name", com.getName());
                    temp_map.put("id", com.getDepartmentId());
                    temp_map.put("path", com.getPath());
                    temp_map.put("children", childrens);
                    result.add(temp_map);
                }
            }
        } catch (Exception e) {
            log.error("获取人员组织树出错:{}", e);
        }
        return result;
    }

    @Override
    public String getMemberNameByUserId(Long userId) {
        return baseMapper.getMemberNameByUserId(userId);
    }

    @Override
    public UserInfo getUserByName(String userName) {
        return baseMapper.getUserByName(userName);
    }

}
