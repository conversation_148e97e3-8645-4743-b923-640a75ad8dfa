package net.tartan.platform.integration.entity.dailyreport;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * project_well_activity
 * <AUTHOR>
public class ProjectDailyActivity implements Serializable {
    private Long id;

    /**
     * 井日报id
     */
    private Long wellReportId;

    /**
     * 日期
     */
    private String date;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 开始时间
     */
    private String endTime;

    /**
     * 起始井深
     */
    private BigDecimal startDepth;

    /**
     * 结束井深
     */
    private BigDecimal endDepth;

    /**
     * 段长
     */
    private BigDecimal segLength;

    /**
     * 钻进时长
     */
    private BigDecimal drillDuration;

    /**
     * 钻进方式
     */
    private String drillType;

    /**
     * 钻进方式描述
     */
    private String drillTypeDesc;

    /**
     * 钻压
     */
    private BigDecimal drillPressure;

    /**
     * 地面钻速
     */
    private BigDecimal groundDrillRate;

    /**
     * 测深
     */
    private BigDecimal md;

    /**
     * 井斜
     */
    private BigDecimal deviation;

    /**
     * 方位
     */
    private BigDecimal direction;

    /**
     * 工具面
     */
    private BigDecimal toolFace;

    /**
     * 异常分析
     */
    private String note;

    /**
     * 排量（L/S）
     */
    private BigDecimal cc;

    /**
     * 泵压（Mpa）
     */
    private BigDecimal pp;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getWellReportId() {
        return wellReportId;
    }

    public void setWellReportId(Long wellReportId) {
        this.wellReportId = wellReportId;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public BigDecimal getStartDepth() {
        return startDepth;
    }

    public void setStartDepth(BigDecimal startDepth) {
        this.startDepth = startDepth;
    }

    public BigDecimal getEndDepth() {
        return endDepth;
    }

    public void setEndDepth(BigDecimal endDepth) {
        this.endDepth = endDepth;
    }

    public BigDecimal getSegLength() {
        return segLength;
    }

    public void setSegLength(BigDecimal segLength) {
        this.segLength = segLength;
    }

    public BigDecimal getDrillDuration() {
        return drillDuration;
    }

    public void setDrillDuration(BigDecimal drillDuration) {
        this.drillDuration = drillDuration;
    }

    public String getDrillType() {
        return drillType;
    }

    public void setDrillType(String drillType) {
        this.drillType = drillType;
    }

    public String getDrillTypeDesc() {
        return drillTypeDesc;
    }

    public void setDrillTypeDesc(String drillTypeDesc) {
        this.drillTypeDesc = drillTypeDesc;
    }

    public BigDecimal getDrillPressure() {
        return drillPressure;
    }

    public void setDrillPressure(BigDecimal drillPressure) {
        this.drillPressure = drillPressure;
    }

    public BigDecimal getGroundDrillRate() {
        return groundDrillRate;
    }

    public void setGroundDrillRate(BigDecimal groundDrillRate) {
        this.groundDrillRate = groundDrillRate;
    }

    public BigDecimal getMd() {
        return md;
    }

    public void setMd(BigDecimal md) {
        this.md = md;
    }

    public BigDecimal getDeviation() {
        return deviation;
    }

    public void setDeviation(BigDecimal deviation) {
        this.deviation = deviation;
    }

    public BigDecimal getDirection() {
        return direction;
    }

    public void setDirection(BigDecimal direction) {
        this.direction = direction;
    }

    public BigDecimal getToolFace() {
        return toolFace;
    }

    public void setToolFace(BigDecimal toolFace) {
        this.toolFace = toolFace;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public BigDecimal getCc() {
        return cc;
    }

    public void setCc(BigDecimal cc) {
        this.cc = cc;
    }

    public BigDecimal getPp() {
        return pp;
    }

    public void setPp(BigDecimal pp) {
        this.pp = pp;
    }
}
