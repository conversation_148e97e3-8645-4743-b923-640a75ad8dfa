package net.tartan.platform.integration.entity.deviceassemble.repaircheck;

import lombok.Data;

/**
 * <p>
 * 领料单/替换清单
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-09
 */
@Data
public class ReplaceItem {

    /**
     * 序号
     */
    private Integer SequenceNumber;


    /**
     * 单号
     */
    private String docNo;

    /**
     * 存货编码
     */
    private String invCode;
    private String serialNumber;

    /**
     * 存货名称
     */
    private String invName;

    /**
     * 存货规格
     */
    private String invStd;

    /**
     * 数量
     */
    private Integer quantity;
    private String unitName;

    /**
     * 备注
     */
    private String note;

//    private String bomId;
    private Long deviceType;
}
