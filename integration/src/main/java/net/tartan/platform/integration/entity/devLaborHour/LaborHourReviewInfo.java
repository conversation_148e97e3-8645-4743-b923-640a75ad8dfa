package net.tartan.platform.integration.entity.devLaborHour;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.constant.PfConstant;
import net.tartan.platform.common.enums.EnumReviewStatus;
import net.tartan.platform.integration.beans.dto.LaborHourDto;
import net.tartan.platform.integration.beans.vo.LaborHourVo;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 工时审批基础信息
 * 外层数据
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = PfConstant.PROJECT_LABOR_HOUR_REVIEW)
public class LaborHourReviewInfo {

    /**
     * 申请id
     */
    @Id
    private String id;

    /**
     * 申请人的memberId
     */
    private String memberId;

    /**
     * 所属项目编号
     */
    private Long projectId;

    /**
     * 工时日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date laborDate;

    /**
     * 提交申请的日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date submitDate;

    /**
     * 申请调整工时的详情
     * 里面所有的详情都是属于同一个项目下的
     */
    private List<LaborHourVo> laborHourInfos;

    /**
     * 审核记录
     */
    private List<LaborHourReviewDetail> reviewDetailList;

    //以下三条都是根据审核记录得出的结果 reviewDetailList
    /**
     * 审批状态
     * 与 EnumReviewStatus 枚举的内容一致
     * reviewDetailList中：
     *      只要有一个驳回 就是驳回
     *      只要有一个未审批 就是进行中
     *      全通过 则是已通过
     * 用户可以将进行中的修改为撤销 撤销后则不能再审批
     */
    private EnumReviewStatus reviewStatus;

    /**
     * 当前代办人姓名
     */
    private String currentReviewMemberName;

    /**
     * 当前代办人id
     */
    private String currentReviewMemberId;

    private String week;
    private String submitUserName;
    private String departmentName;
    private BigDecimal totalHour;
    /**
     * 项目名
     */
    private String projectName;

    /**
     * 是否锁定（只要有一个节点审批通过了，就算锁定了）
     * 0 -> 未锁定， 1 -> 已锁定
     */
    private Integer isLocked;


}
