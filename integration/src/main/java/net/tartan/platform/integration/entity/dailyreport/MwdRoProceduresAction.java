package net.tartan.platform.integration.entity.dailyreport;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * mwd_ro_procedures_action
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MwdRoProceduresAction{
    private Long id;

    /**
     * rig out procedures report id
     */
    private Long ropReportId;

    /**
     * 编号
     */
    private String serialNumber;

    /**
     * mwd操作人
     */
    private String action;

    /**
     * 日期yyyy-MM-dd
     */
    private String date;

    private String initial;

}
