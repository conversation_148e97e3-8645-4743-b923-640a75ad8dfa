package net.tartan.platform.integration.beans.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import net.tartan.platform.integration.entity.MemberInfo;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
public class MemberInfoVo {
    private long current;
    private long total;
    private long pages;
    private long size;
    private List<MemberInfo> memberInfoList;

    public MemberInfoVo() {
        this.current = 1;
        this.size = 10;
    }

}
