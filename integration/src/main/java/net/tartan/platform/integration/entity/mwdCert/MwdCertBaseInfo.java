package net.tartan.platform.integration.entity.mwdCert;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.constant.PfConstant;
import net.tartan.platform.integration.entity.deviceassemble.repaircheck.ReplaceItem;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.List;

/**
 * MWD仪器合格证 公共部分的参数
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = PfConstant.MWD_CERT_REPORT)
public class MwdCertBaseInfo {

    @Id
    private String id;

    private Long deviceType;
    private Long mwdId;

    /**
     * 质量体系文件编号
     * 格式为 TSC/ZY/MWD: + 年份
     * 如：TSC/ZY/MWD:2020
     */
    private String fileNo = "";

    /**
     * 质量体系编号
     * 默认值：TSC/ZY-MWD-AZDM-002-1.0
     */
    private String qualityNo = "TSC/ZY-MWD-AZDM-002-1.0";

    /**
     * NO：
     * 格式为 序列号+年份的后两位+月+日
     * 例如：AZ-DM1114-240514
     */
    private String no = "";

    /**
     * 版本/修改
     * 默认值： 1
     */
    private String versionNo = "1";

    /**
     * 仪器编号
     * 从工单里取序列号
     */
    private String serialNumber = "";

    /**
     * 出厂日期（默认用证书的创建日期）
     * 格式为 yyyy/MM/dd
     */
    private String createDate = "";

    /**
     * 维修工单
     * 从工单里取mwdNumber
     */
    private String mwdNumber = "";

    /**
     * 维修等级
     * 可选值：一级维护、二级维护、三级维护、四级维护
     */
    private String repairLevel = "";

    /**
     * 维护人员
     */
    private String repairUser = "";

    /**
     * 主管人员
     */
    private String supervisorUser = "丁俊";

    /**
     * 质检人员
     */
    private String qualityCheckUser = "何永达";

    /**
     * 更换部件明细
     * 从工单里取更换列表
     */
    private List<ReplaceItem> replaceItemList = new ArrayList<>();

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
    }
}

    /*
ReplaceItem:
private String invName;
private Integer quantity;
private String unitName;
private String note;

     */

/*


 */
