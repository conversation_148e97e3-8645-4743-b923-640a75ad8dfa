package net.tartan.platform.integration.entity.dailyreport.RSS;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.constant.PfConstant;
import net.tartan.platform.integration.entity.dailyreport.MwdTool;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * rss_tools_onsite_report
 * <AUTHOR>
 */

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = PfConstant.RSS_TOOLSONSITE)
public class RSSToolsOnsiteReport {
    @Transient
    private Long id;

    /**
     * 报告类型
     */
    @Transient
    private String reportType;

    @Id
    @JsonIgnore
    private String reportId;

    /**
     * 作业id
     */
    @NotNull
    private Long jobId;
    private String jobNumber;

    /**
     * 工具
     */
    private List<MwdTool> toolList;
}
