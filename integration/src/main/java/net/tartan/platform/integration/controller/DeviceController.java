package net.tartan.platform.integration.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.common.beans.ResultCode;
import net.tartan.platform.integration.beans.dto.DeviceDTO;
import net.tartan.platform.integration.beans.query.DeviceStatisticsDetailQuery;
import net.tartan.platform.integration.beans.query.ToolQuery;
import net.tartan.platform.integration.beans.vo.*;
import net.tartan.platform.integration.beans.vo.devicerisktype.DeviceRiskTypeVo;
import net.tartan.platform.integration.entity.Device;
import net.tartan.platform.integration.exception.BusinessException;
import net.tartan.platform.integration.service.IDeviceComponentRelationService;
import net.tartan.platform.integration.service.IDeviceService;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 *
 * 设备库存信息表 前端控制器
 * <AUTHOR>
 * @since 2022-05-09
 */
@RestController
@RequestMapping("/device")
public class DeviceController {

    @Autowired
    private IDeviceService deviceService;
    @Autowired
    private IDeviceComponentRelationService deviceComponentRelationService;

//    @PostMapping("/template/list")
//    public CommonResult templateList(@RequestBody(required = false) ToolQuery query) {
//        List<DeviceTemplate> deviceTemplateList = deviceService.templateList(query);
//        return CommonResult.success(deviceTemplateList);
//    }

    @PostMapping("list/{current}/{size}")
    public CommonResult list(@PathVariable long current, @PathVariable long size, @RequestBody(required = false) ToolQuery query) {
        IPage<Device> page = new Page<>(current, size);
        if (query == null) {
            query = new ToolQuery();
        }
        deviceService.getListByPage(page, query);
        return CommonResult.success(page);
    }

    @PostMapping("listByType/{current}/{size}")
    public CommonResult listByType(@PathVariable long current, @PathVariable long size, @RequestBody(required = false) ToolQuery query) {
        IPage<DeviceVo> page = new Page<>(current, size);
        if (query == null) {
            query = new ToolQuery();
        }
        IPage<?> result = deviceService.getListByType(page, query);
        return CommonResult.success(result);
    }

    @PostMapping("insert")
    public CommonResult insertDevice(@RequestBody DeviceVo deviceVo) {
        deviceService.insertDevice(deviceVo);
        return CommonResult.success(
                deviceService.info(
                        ToolQuery.builder()
                                .deviceId(deviceVo.getDeviceId())
                                .build()
                )
        );
    }
    @PostMapping("statisticsByType")
    public CommonResult statisticsByType(@RequestBody ToolQuery toolQuery) {
        return CommonResult.success(
                deviceService.groupByStatusAndCountX(toolQuery)
        );
    }

    @PostMapping("getValueList")
    public CommonResult getCounterValueList(@RequestBody ToolQuery toolQuery) {
        return CommonResult.success(
                deviceService.getCounterValueList(toolQuery)
        );
    }

    @PostMapping("infoNew")
    public CommonResult infoNew(@RequestBody ToolQuery query) {
        Object device = deviceService.info(query);
        return CommonResult.success(device);
    }

    @PostMapping("update")
    public CommonResult update(@RequestBody DeviceDTO deviceDTO) {
//        if(StringUtils.isEmpty(device.getSerialNumber())){
//            throw new BusinessException(ResultCode.PARAMS_ERROR);
//        }
        deviceService.updateDevice(deviceDTO);
        return CommonResult.success(
                deviceService.info(
                        ToolQuery.builder()
                                .deviceId(deviceDTO.getDeviceId())
                                .build()
                )
        );
    }

    @PostMapping("batchUpdate")
    public CommonResult batchUpdate(@RequestBody DeviceBatchUpdateVo batchUpdateVo) {
//        if(StringUtils.isEmpty(device.getSerialNumber())){
//            throw new BusinessException(ResultCode.PARAMS_ERROR);
//        }
        deviceService.batchUpdateDevice(batchUpdateVo);
        return CommonResult.success();
    }

    @PostMapping("batchUpdateTest")
    public CommonResult batchUpdateTest(@RequestBody DeviceBatchUpdateVo batchUpdateVo) {
//        if(StringUtils.isEmpty(device.getSerialNumber())){
//            throw new BusinessException(ResultCode.PARAMS_ERROR);
//        }
        deviceService.batchUpdateDeviceTest(batchUpdateVo);
        return CommonResult.success();
    }

    @PostMapping("info")
    public CommonResult info(@RequestBody ToolQuery query) {
        Device device = deviceService.findBySN(query.getSerialNumber());
        return CommonResult.success(device);
    }

    @PostMapping("riskType")
    public CommonResult getRiskType(@RequestBody ToolQuery query) {
        DeviceRiskTypeVo deviceRiskTypeVo = deviceService.getRiskType(query.getSerialNumber());
        return CommonResult.success(deviceRiskTypeVo);
    }

    /**
     * @param query 根据条件批量查询
     * @return
     */
    @PostMapping("info/batch")
    public CommonResult getRiskTypeBatch(@RequestBody ToolQuery query) {
        List<DeviceVo> deviceList = deviceService.getDeviceListByQuery(query);
        return CommonResult.success(deviceList);
    }

    /**
     * 序列号合法性校验
     * 1: 在device表
     * 0: 在ERP但是不在device表中
     * -1: 非法序列号
     *
     * @param query
     * @return
     */
    @PostMapping("serialNumber/listCheck")
    public CommonResult serialNumberListCheck(@RequestBody ToolQuery query) {
        if (query.getSerialNumber() == null && ObjectUtils.isEmpty(query.getSerialNumberList())) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        List<DeviceCheckListVo> checkListVos = deviceService.serialNumberCheck(query);
        return CommonResult.success(checkListVos);
    }

    @PostMapping("serialNumber/check")
    public CommonResult serialNumberCheck(@RequestBody ToolQuery query) {
        if (query.getSerialNumber() == null) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        Integer status = deviceService.serialNumberCheck(query.getSerialNumber());
        return CommonResult.success(status);
    }

    @PostMapping("transfer/list/{current}/{size}")
    public CommonResult transfer(@PathVariable long current, @PathVariable long size, @RequestBody(required = false) ToolQuery query) {
        IPage<TransferDocVo> page = new Page<>(current, size);
        if (query == null) {
            query = new ToolQuery();
        }
        deviceService.getTransferList(page, query);
        return CommonResult.success(page);
    }


    @GetMapping("statistics")
    public CommonResult statistics() {
        List<DeviceStatisticsVo> deviceStatisticsVoList = deviceService.statistics();
        return CommonResult.success(deviceStatisticsVoList);
    }

    /**
     * 仪器统计列表
     */
    @PostMapping("statistic/list/{current}/{size}")
    public CommonResult statisticList(@PathVariable long current, @PathVariable long size,
                                    @RequestBody(required = false) DeviceStatisticsDetailQuery query) {

        if (query == null) {
            query = new DeviceStatisticsDetailQuery();
        }
        IPage<DeviceStatisticsDetailVo> page = new Page<>(current, size);
        return CommonResult.success(deviceService.statisticList(page, query));
    }

    @PostMapping("/export")
    public CommonResult export(@RequestBody(required = false) ToolQuery toolQuery) {

        if (toolQuery == null ) {
            toolQuery = new ToolQuery();
        }
        deviceService.export(toolQuery);
        return CommonResult.success();
    }

    /**
     * 日报仪器列表查询
     * （调拨单数据）
     * @param transferDocVo
     * @return
     */
    @PostMapping("docTool/list/{current}/{size}")
    public CommonResult docToolList(@PathVariable long current, @PathVariable long size,
                                    @RequestBody(required = false) TransferDocVo transferDocVo) {

        if (transferDocVo.getDocNo() == null) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        IPage<TransferDocVo> page = new Page<>(current, size);
        return CommonResult.success(deviceService.docToolList(page, transferDocVo));
    }

    /**
     * 模糊查询序列号
     *
     * @param toolQuery
     * @return
     */
    @PostMapping("getSerialNumberList")
    public CommonResult getSerialNumberList(@RequestBody(required = false) ToolQuery toolQuery) {

        return CommonResult.success(deviceService.serialNumberList(toolQuery));
    }
    /**
     * 旋导仪器导出
     *
     * @param toolQuery
     * @return
     */
    @PostMapping("rssMaintenance/export")
    public CommonResult rssMaintenanceExport(@RequestBody ToolQuery toolQuery) {

        deviceService.rssMaintenanceExport(toolQuery);
        return CommonResult.success();
    }

    /**
     * 获取仪器中任意字段的推荐值
     * 和 getCounterValueList 作用重复了 暂时用不到
     * @param paramName
     * @return
     */
    @PostMapping("recommendList")
    public CommonResult recommendList(@RequestParam("paramName") String paramName) {

        List<String> valueList = deviceService.selectParamList(paramName);
        return CommonResult.success(valueList);
    }

    /**
     * 获取仪器库存统计
     * @return
     */
    @GetMapping("inventory")
    public CommonResult inventory() {
        List<DeviceInventoryVo> inventoryList = deviceService.getInventoryStatistics();
        return CommonResult.success(inventoryList);
    }

    @PostMapping("updateRelation")
    public CommonResult updateRelation(@RequestParam("deviceId") Long deviceId,
                                       @RequestParam("componentIdList") List<Long> componentIdList) {
        deviceComponentRelationService.updateDeviceComponentRelation(deviceId, componentIdList);
        return CommonResult.success();
    }
    
}
