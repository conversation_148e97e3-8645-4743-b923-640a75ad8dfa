package net.tartan.platform.integration.entity.mwdCert;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CertApprovalInfo {

    private Long id;
    private Long mwdId;
    //审批状态： 0-未审批，-1-未通过，2-通过
    private Integer approvalStatus;//功能保留 但根据何永达的反馈 暂时不需要开启审批限制 合格证可自由创建和下载
    private Long createdBy;
    private Long approvedBy;

    private String createTime;
    private String updateTime;

}
