package net.tartan.platform.integration.beans.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 模板DTO
 */
@Data
public class TemplateDTO {
    /**
     * 模板ID
     */
    private Integer id;
    
    /**
     * 模板名称
     */
    private String templateName;
    
    /**
     * 模板代码
     */
    private String templateCode;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 创建时间
     */
    private Date createdAt;
    
    /**
     * 更新时间
     */
    private Date updatedAt;
    
    /**
     * 模板项目列表
     */
    private List<TemplateItemDTO> items;
} 