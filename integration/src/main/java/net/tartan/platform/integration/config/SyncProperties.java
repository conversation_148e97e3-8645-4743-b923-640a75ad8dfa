package net.tartan.platform.integration.config;

import java.util.Map;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import lombok.Data;

/**
 * 数据同步配置属性
 * 
 * <AUTHOR>
 * @since 2025-07-11
 */
@Data
@Component
@ConfigurationProperties(prefix = "sync")
public class SyncProperties {

    /**
     * 调度器配置
     */
    private Scheduler scheduler = new Scheduler();

    /**
     * 重试配置
     */
    private Retry retry = new Retry();

    /**
     * 健康检查配置
     */
    private HealthCheck healthCheck = new HealthCheck();

    /**
     * 付款申请单配置
     */
    private PaymentApplication paymentApplication = new PaymentApplication();

    /**
     * OA系统配置
     */
    private Oa oa = new Oa();

    @Data
    public static class Scheduler {
        /**
         * 调度器总开关
         */
        private boolean enabled = true;
    }

    @Data
    public static class Retry {
        /**
         * 重试功能开关
         */
        private boolean enabled = true;
        
        /**
         * 重试Cron表达式
         */
        private String cron = "0 */30 * * * ?";
        
        /**
         * 最大重试次数
         */
        private int maxRetryCount = 3;
    }

    @Data
    public static class HealthCheck {
        /**
         * 健康检查开关
         */
        private boolean enabled = true;
        
        /**
         * 健康检查Cron表达式
         */
        private String cron = "0 0 * * * ?";
    }

    @Data
    public static class PaymentApplication {
        /**
         * 付款申请单同步开关
         */
        private boolean enabled = true;
        
        /**
         * Cron表达式
         */
        private String cron = "0 */2 * * * ?";
        
        /**
         * 模板代码
         */
        private String templateCode = "YFSHD_001";
        
        /**
         * ERP表名
         */
        private String erpTable = "payment_application";
        
        /**
         * 时间字段
         */
        private String timeField = "created_time";
        
        /**
         * 单据编号字段
         */
        private String docNoField = "doc_no";
        
        /**
         * 发起人字段
         */
        private String senderField = "applicant_code";
        
        /**
         * 字段映射配置
         */
        private FieldMappings fieldMappings = new FieldMappings();
    }

    @Data
    public static class FieldMappings {
        /**
         * 主表字段映射
         */
        private Map<String, String> main;
        
        /**
         * 从表配置
         */
        private SubTable sub;
    }

    @Data
    public static class SubTable {
        /**
         * 从表名
         */
        private String table;
        
        /**
         * 外键字段
         */
        private String foreignKey;
        
        /**
         * 从表字段映射
         */
        private Map<String, String> fields;
    }

    @Data
    public static class Oa {
        /**
         * OA基础URL - 基于实际测试确认的地址
         */
        private String baseUrl = "http://101.132.136.140:8096/seeyon/rest";

        /**
         * REST接口配置
         */
        private Rest rest = new Rest();

        /**
         * HTTP配置
         */
        private Http http = new Http();
    }

    @Data
    public static class Rest {
        /**
         * 推送数据接口URL - 基于ERP反编译代码确认的路径
         */
        private String pushUrl = "/customSendForm";

        /**
         * 认证配置
         */
        private Auth auth = new Auth();
    }

    @Data
    public static class Auth {
        /**
         * Token获取URL - 基于ERP反编译代码确认的路径
         */
        private String tokenUrl = "/token";

        /**
         * 用户名 - 从ERP数据库配置表获取
         */
        private String username = "rest_erp";

        /**
         * 密码 - 从ERP数据库配置表获取
         */
        private String password = "tartanoa2022";
    }

    @Data
    public static class Http {
        /**
         * 连接超时时间(毫秒)
         */
        private int connectTimeout = 30000;
        
        /**
         * 读取超时时间(毫秒)
         */
        private int readTimeout = 60000;
        
        /**
         * 最大重试次数
         */
        private int maxRetry = 3;
        
        /**
         * 重试间隔(毫秒)
         */
        private int retryInterval = 5000;
    }
}
