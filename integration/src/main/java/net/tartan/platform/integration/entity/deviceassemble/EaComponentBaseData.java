package net.tartan.platform.integration.entity.deviceassemble;

import lombok.Data;
import net.tartan.platform.common.enums.EnumRiskType;

import java.math.BigDecimal;

/**
 * ea工单 维修检查 部件基础数据
 */
@Data
public class EaComponentBaseData {

    private Long componentId;

    /**
     * 部件名称
     */
    private String invName;

    /**
     * 序列号，erp的批次号
     */
    private String serialNumber;

    private String versionNumber;

    //更新版本 （可更新
    private String updateVersionNumber;

    // 旋导部件参数
    // 品号
    private String invCode;
    // 最后修改时间
    private String lastAssemblyDate;
    // 上级部件id
    private Long parentComponentId;
    // 祖先id（所属于哪个总成中）
    private Long ancestorDeviceId;
    // 部件可用层级
    private Integer level;
    private Boolean isLeaf;



    /**
     * 风险类型
     */
    private EnumRiskType riskType;
    // 更新风险类型 （可更新
    private EnumRiskType updateRiskType;
    /**
     * 风险值
     */
    private BigDecimal riskValue;
    // 更新风险值 （可更新
    private BigDecimal updateRiskValue;
    /**
     * 最高温度
     */
    private BigDecimal maxBht;
    /**
     * 修正最高温度 （可更新
     */
    private BigDecimal reviseMaxBht;
    /**
     * 总入井时间
     */
    private BigDecimal totalInWellHrs;
    /**
     * 修正使用时长（可更新
     */
    private BigDecimal reviseTotalHours;
    /**
     * 制造商
     */
    private String manufacturer;

    /**
     * 备注
     */
    private String note;
}
