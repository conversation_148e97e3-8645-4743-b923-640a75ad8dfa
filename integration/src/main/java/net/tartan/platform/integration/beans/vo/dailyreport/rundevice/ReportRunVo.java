package net.tartan.platform.integration.beans.vo.dailyreport.rundevice;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.enums.EnumReportType;
import net.tartan.platform.integration.entity.dailyreport.MwdRunSummary;
import net.tartan.platform.integration.entity.dailyreport.MwdTool;

import java.math.BigDecimal;
import java.util.List;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReportRunVo {

    private String wellNumber;

    private Long jobId;

    /**
     * 趟次
     */
    private Integer run;
    /**
     * 最高温度
     */
    private BigDecimal maxBht;

    private String mwdSummary;

    /**
     * 工具
     */
    private List<ReportRunMwdTool> toolList;
}
