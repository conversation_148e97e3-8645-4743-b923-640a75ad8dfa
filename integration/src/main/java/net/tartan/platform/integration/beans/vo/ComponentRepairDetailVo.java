package net.tartan.platform.integration.beans.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
public class ComponentRepairDetailVo {

    /**
     * 配件名称
     */
    private String invName;
    /**
     * 序列号
     */
    private String serialNumber;

    /**
     * 最高温度
     */
    private BigDecimal maxBht;

    /**
     * 总循环时间
     */
    private BigDecimal totalCirculateHrs;

    /**
     * 总入井时间
     */
    private BigDecimal totalInWellHrs;

    /**
     * 服役次数
     */
    private Integer serveTotalCount;

}
