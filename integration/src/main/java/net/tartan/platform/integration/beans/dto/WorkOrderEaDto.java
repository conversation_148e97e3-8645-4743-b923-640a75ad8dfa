package net.tartan.platform.integration.beans.dto;

import lombok.Data;
import net.tartan.platform.integration.entity.*;
import net.tartan.platform.integration.entity.deviceassemble.*;

import java.util.List;

/**
 * ea工单信息
 */
@Data
public class WorkOrderEaDto {

    private Long eaId;

    // 上级ea工单id
    private Long parentEaId;

    // 上级mwd工单id
    private Long parentMwdId;

    /**
     * 工单单号
     */
    private String eaNumber;

    /**
     * 作业编号
     */
    private String jobNumber;

    /**
     * 井号
     */
    private String wellNumber;

    /**
     * kit箱编号
     */
    private String kitNumber;

    /**
     * 开始日期
     */
    private String startDate;

    /**
     * 完成日期
     */
    private String endDate;
    /**
     * 维修状态1：已完成，0：未完成，-1：滞留
     */
    private Integer finish;
    /**
     * 备注
     */
    private String notes;

    private List<WorkOrderEaDetail> workOrderEaDetailList;

    // 组装与拆卸
    private EaComponentAssembleRecord eaComponentAssembleRecord;

    // 维修检查
    private EaRepairCheckData eaRepairCheckData;

    // 人员工时
    private List<WorkOrderEaRepairMember> repairMemberList;

    //测试件
//    private List<ComponentTest> componentTestList;
}
