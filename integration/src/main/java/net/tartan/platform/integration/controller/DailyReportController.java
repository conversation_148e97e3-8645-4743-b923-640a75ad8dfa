package net.tartan.platform.integration.controller;

import lombok.extern.slf4j.Slf4j;
import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.common.beans.ResultCode;
import net.tartan.platform.common.enums.EnumReportType;
import net.tartan.platform.integration.beans.request.reportupdate.PushReportParams;
import net.tartan.platform.integration.beans.request.reportupdate.ReportExportParams;
import net.tartan.platform.integration.beans.vo.dailyreport.*;
import net.tartan.platform.integration.beans.vo.dailyreport.rundevice.ReportRunSummary;
import net.tartan.platform.integration.beans.vo.dailyreport.rundevice.ReportRunVo;
import net.tartan.platform.integration.entity.JobInfo;
import net.tartan.platform.integration.exception.BusinessException;
import net.tartan.platform.integration.service.report.CompleteReportFileService;
import net.tartan.platform.integration.service.report.DailyReportService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * dailyReport 日报接口
 */
@RestController
@RequestMapping("dailyReport")
@Slf4j
public class DailyReportController {

    @Autowired
    private DailyReportService dailyReportService;
    //    @Autowired
//    private JobPermissionService jobPermissionService;
    @Autowired
    private CompleteReportFileService completeReportFileService;

//    //新版暂时不用了
//    @PostMapping("add")
//    public CommonResult add(@RequestBody String request) {
//        JSONObject params = JSON.parseObject(request);
//        //校验报告类型
//        String reportType = params.getString("reportType");
//        if (StringUtils.isBlank(reportType)) {
//            throw new BusinessException(ResultCode.PARAMS_ERROR);
//        }
//        //校验是否授权
//        long jobId = params.getLongValue("jobId");
//        boolean isPermit = jobPermissionService.checkPermission(jobId);
//        if (!isPermit) {
//            throw new BusinessException(ResultCode.NO_OPERATION_PERMISSION);
//        }
//        //添加报告
//        dailyReportService.addReport(request, EnumReportType.valueOf(reportType));
//        return CommonResult.success();
//    }

    @GetMapping("info")
    public CommonResult info(@RequestParam("reportType") EnumReportType reportType,
                             @RequestParam("reportId") long reportId) {
        Object result = dailyReportService.getReport(reportId, reportType);
        return CommonResult.success(result);
    }

//    //新版暂时不用了
//    /**
//     * 根据趟次和序列号查找日报中工具使用时间
//     */
//    @GetMapping("toolRunHours")
//    public CommonResult toolRunHours(@RequestParam("run") Integer run,
//                                     @RequestParam("jobId") Long jobId,
//                                     @RequestParam("serialNumber") String SerialNumber) {
//        BigDecimal hours = dailyReportService.toolRunHours(run, jobId, SerialNumber);
//        return CommonResult.success(hours);
//    }

//    //新版暂时不用了
//    /**
//     * 根据趟次获取工具列表
//     */
//    @GetMapping("runToolList")
//    public CommonResult runToolList(@RequestParam("run") Integer run, @RequestParam("jobId") Long jobId) {
//        List<MwdTool> toolList = dailyReportService.runToolList(run, jobId);
//        return CommonResult.success(toolList);
//    }

//    /**
//     * 修改日报（只有当前报告井下授权的用户能够添加）
//     */
//    //新版暂时不用了
//    @PostMapping("update")
//    public CommonResult update(@RequestBody String request) {
//        JSONObject params = JSON.parseObject(request);
//        //校验报告类型
//        String reportType = params.getString("reportType");
//        if (StringUtils.isBlank(reportType)) {
//            throw new BusinessException(ResultCode.PARAMS_ERROR);
//        }
//        EnumReportType type = EnumReportType.valueOf(reportType);
//        //校验是否授权
//        long reportId = params.getLongValue("id");
//        boolean isPermit = jobPermissionService.checkPermission(type, reportId);
//        if (!isPermit) {
//            throw new BusinessException(ResultCode.NO_OPERATION_PERMISSION);
//        }
//        //更新报告
//        dailyReportService.updateReport(request, type);
//        return CommonResult.success();
//    }
//
//    //新版暂时不用了
//    /**
//     * 删除日报（只有当前报告井下授权的用户能够添加）
//     */
//    @PostMapping("delete")
//    public CommonResult delete(@RequestParam("reportType") EnumReportType reportType,
//                               @RequestParam("reportId") long reportId) {
//        //校验是否授权
//        boolean isPermit = jobPermissionService.checkPermission(reportType, reportId);
//        if (!isPermit) {
//            throw new BusinessException(ResultCode.NO_OPERATION_PERMISSION);
//        }
//        //删除报告
//        dailyReportService.deleteReport(reportId, reportType);
//        return CommonResult.success();
//    }

    /**
     * 根据作业获取该作业下的所有报告的简要信息
     *
     * @param jobId
     * @return
     */
    @GetMapping("baseInfoList")
    public CommonResult getBaseInfoList(@RequestParam("jobId") long jobId,
                                        @RequestParam("reportType") EnumReportType reportType) {
        List<ReportBaseVo> list = dailyReportService.getBaseInfoList(jobId, reportType);
        return CommonResult.success(list);
    }

//    //新版暂时不用了
//    /**
//     * 客户端同步基础信息列表（同步第一步）
//     */
//    @GetMapping("syncBaseInfoList")
//    public CommonResult syncBaseInfoList(@RequestParam(value = "range", required = false) Long days) {
//        List<SyncReportInfoVo> list = dailyReportService.syncBaseInfoList(days);
//        return CommonResult.success(list);
//    }

//    //新版暂时不用了
//    /**
//     * 从服务端拉取报告
//     *
//     * @param list
//     * @return
//     */
//    @PostMapping("pull")
//    public CommonResult pull(@Validated @RequestBody List<PullReportParams> list) {
//        List<PullReportVo> reportVoList = dailyReportService.pull(list);
//        return CommonResult.success(reportVoList);
//    }

    /**
     * 从服务端拉取报告
     *
     * @param jobIdList
     * @return
     */
    @PostMapping("pullV2")
    public CommonResult pull(@RequestParam(value = "jobIdList") List<Long> jobIdList) {
        if (CollectionUtils.isEmpty(jobIdList)) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        List<PullReportVo> reportVoList = dailyReportService.pullV2(jobIdList);
        return CommonResult.success(reportVoList);
    }


    /**
     * 客户端推送报告V2
     * 此版本的ERP厂商为鼎捷
     * 日报客户端只推送数据覆盖服务端，不拉取服务端的数据
     *
     * @date 2022-01-13
     */
    @PostMapping("pushV2")
    public CommonResult pushV2(@RequestBody @Validated PushReportParams reportParams) {
        JobInfo jobInfo = dailyReportService.pushV2(reportParams);
        return CommonResult.success(jobInfo);
    }
//
//    //新版暂时不用了
//    /**
//     * 根据jobId,stockId获取工具相关数据
//     */
//    @PostMapping("toolData")
//    public CommonResult toolData(@RequestParam("jobId") Long jobId,
//                                 @RequestParam("stockId") Long stockId) {
//        RepairDetailVo repairDetailVo = dailyReportService.getToolData(jobId, stockId);
//        return CommonResult.success(repairDetailVo);
//    }

//    //新版暂时不用了
//    /**
//     * 工程日报相关数据
//     */
//    @PostMapping("projectData")
//    public CommonResult projectData(@RequestBody @Validated ReportDto reportDto) {
//        if (reportDto == null || reportDto.getJobId() == null) {
//            throw new BusinessException(ResultCode.PARAMS_ERROR);
//        }
//        SyncProjectDailyReportVo projectDailyReport = dailyReportService.getProjectData(reportDto);
//        return CommonResult.success(projectDailyReport);
//    }

//    //新版暂时不用了
//    /**
//     * 日报相关数据
//     */
//    @PostMapping("baseData")
//    public CommonResult baseData(@RequestBody @Validated ReportDto reportDto) {
//        if (reportDto == null || reportDto.getJobId() == null) {
//            throw new BusinessException(ResultCode.PARAMS_ERROR);
//        }
//        List<MwdDailyActivityVo> dailyActivityVoList = dailyReportService.getBaseData(reportDto);
//        return CommonResult.success(dailyActivityVoList);
//    }

//    /**
//     * 仪器使用统计
//     *
//     * @param instrumentUsageStatisticsDto
//     * @return
//     */
//    @PostMapping("usage")
//    public CommonResult mwdUsage(@RequestBody @Validated InstrumentUsageStatisticsDto instrumentUsageStatisticsDto) {
//        if (instrumentUsageStatisticsDto == null) {
//            return CommonResult.success(new ArrayList());
//        }
//        List<MWDInstrumentUsageStatisticsVo> mwdInstrumentUsageStatisticsVoList = new ArrayList<>();
//        if (instrumentUsageStatisticsDto.getToolType().equals(EnumToolType.MWD)) {
//            mwdInstrumentUsageStatisticsVoList = dailyReportService.getMWDUsageList(instrumentUsageStatisticsDto);
//        }
////        if (instrumentUsageStatisticsDto.getToolType().equals(EnumToolType.UNDER_WELL)) {
////            mwdInstrumentUsageStatisticsVoList = dailyReportService.getUnderWellUsageList(instrumentUsageStatisticsDto);
////        }
//        if (instrumentUsageStatisticsDto.getToolType().equals(EnumToolType.UNDER_WELL) && StringUtils.isEmpty(instrumentUsageStatisticsDto.getType())) {
//            throw new BusinessException(ResultCode.TYPE_NOT_EXIST);
//        }
//        return CommonResult.success(mwdInstrumentUsageStatisticsVoList);
//    }

//    //新版暂时不用了
//    /**
//     * 刷新日报数据
//     */
//    @PostMapping("dataRefresh")
//    public CommonResult dataRefresh(@RequestBody @Validated ReportDto reportDto) {
//        dailyReportService.dataRefresh(reportDto);
//        return CommonResult.success();
//    }

    /**
     * 下载完井报告模板
     */
    @RequestMapping(value = "/complete/download/{reportType}/{jobId}",
            method = RequestMethod.GET)
    public CommonResult completeDownload(@PathVariable("jobId") Long jobId,
                                         @PathVariable("reportType") EnumReportType reportType) {
        if (jobId == null) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        completeReportFileService.generateReport(jobId, reportType);
        return CommonResult.success();
    }

    /**
     * 上传完井报告
     */
    @PostMapping("complete/upload")
    public CommonResult uploadComplete(@RequestParam("jobId") Long jobId,
                                       @RequestParam("reportType") EnumReportType reportType,
                                       @RequestParam(value = "file") MultipartFile file) {
        if (jobId == null || reportType == null) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        dailyReportService.uploadCompleteReport(jobId, reportType, file);
        return CommonResult.success();
    }

    /**
     * 报告导出
     */
    @PostMapping(value = "/export")
    public CommonResult export(@RequestBody ReportExportParams exportParams) {
//        if (jobId == null || reportType == null) {
//            throw new BusinessException(ResultCode.PARAMS_ERROR);
//        }
        dailyReportService.exportReport(exportParams);
        return CommonResult.success();
    }

    /**
     * 根据作业号jobId和仪器获取获取日报中仪器的入井时间，循环时间，最高温度
     */
    @GetMapping("common/data")
    public CommonResult getReportCommonData(@RequestParam(value = "jobNumber", required = false) String jobNumber,
                                            @RequestParam(value = "wellNumber", required = false) String wellNumber,
                                            @RequestParam(value = "serialNumber", required = false) String serialNumber,
                                            @RequestParam(value = "reportType", required = false) String reportType) {
        // 需要让serialNumber可以为空
        ReportCommonDataVo reportCommonData = dailyReportService.getReportCommonData(jobNumber, wellNumber, serialNumber, reportType);
        return CommonResult.success(reportCommonData);
    }

    /**
     * 获取作业下所有趟次的仪器信息与仪器维修信息
     */
    @PostMapping("run/device/info")
    public CommonResult getRunDeviceInfo(@RequestParam("wellNumber") String wellNumber) {
        List<ReportRunVo> reportRunVos = dailyReportService.getRunDeviceInfo(wellNumber);
        return CommonResult.success(reportRunVos);
    }

    /**
     *
     * @param reportRunSummary
     * @return
     */
    @PostMapping("run/summary/save")
    public CommonResult saveReportRunSummary(@RequestBody ReportRunSummary reportRunSummary) {
        dailyReportService.saveReportRunSummary(reportRunSummary);
        return CommonResult.success();
    }
}
