package net.tartan.platform.integration.beans.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.enums.EnumToolType;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InstrumentUsageStatisticsDto {
  /** 存货编码 */
  private String invCode;
  /** 存货名称 */
  private String invName;
  /** 批次号 */
  private String serialNumber;
  /** 仪器类型 */
  private String type;
  /** 开始时间 */
  private String startTime;
  /** 结束时间 */
  private String endTime;
  /** 工具类型 */
  private EnumToolType toolType;
}
