package net.tartan.platform.integration.beans.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.enums.EnumRiskType;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ComponentHierarchyDTO {
    private Long componentId;

    /**
     * 部件名称
     */
    private String invName;

    /**
     * 序列号，erp的批次号
     */
    private String serialNumber;

    private String versionNumber;

    // 旋导部件参数
    // 品号
    private String invCode;
    // 最后修改时间
    private String lastAssemblyDate;
    // 上级部件id
    private Long parentComponentId;
    private String parentComponentSerialNumber;
    // 祖先id（所属于哪个总成中）
    private Long ancestorDeviceId;
    private String ancestorDeviceSerialNumber;
    // 部件可用层级
    private Integer level;
    private Boolean isLeaf;
    private Integer sort;

    /**
     * 风险类型
     */
    private EnumRiskType riskType;
    /**
     * 风险值
     */
    private BigDecimal riskValue;
    /**
     * 最高温度
     */
    private BigDecimal maxBht;
    /**
     * 修正最高温度
     */
    private BigDecimal reviseMaxBht;
    /**
     * 总入井时间
     */
    private BigDecimal totalInWellHrs;
    /**
     * 修正使用时长
     */
    private BigDecimal reviseTotalHours;
    /**
     * 总循环时间
     */
    private BigDecimal totalCirculateHrs;
    /**
     * 制造商
     */
    private String manufacturer;

    /**
     * 备注
     */
    private String note;

    List<ComponentHierarchyDTO> children = new ArrayList<>();
}
