package net.tartan.platform.integration.entity.process;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 工具操作明细
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ProcessFlowOperate implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 工序id
     */
    private Long flowId;

    /**
     * 设备编号
     */
    private String deviceNumber;

//    /**
//     * 数量
//     */
//    private Integer quantity;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 合格数量
     */
    private Integer qualifiedQuantity;
    /**
     * 返修数量
     */
    private Integer repairQuantity;
    /**
     * 废弃数量
     */
    private Integer abandonQuantity;
    /**
     * 废弃原因
     */
    private String abandonReason;
    /**
     * 工作类型（白班，夜班）
     */
    private String workType;
    /**
     * 质检员
     */
    private Long checkUserId;

    /**
     * 操作人员id
     */
    private Long operateId;

    /**
     * 首件调试
     */
    private Integer firstTest;

    /**
     * 总耗时
     */
    private Double totalHours;

    /**
     * 备注
     */
    private String comment;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


}
