package net.tartan.platform.integration.beans.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class DeviceRiskStatisticsVo {
    private Long deviceType;
    private String deviceTypeStr;
    private String specification;  // 规格(仅近钻头区分475/675)
    private Integer produce;      // 生产序列 PRODUCE
    private Integer risk;         // 风险序列 RISK
    private Integer test;         // 试验序列 TEST
    private Integer total;        // 总数
} 