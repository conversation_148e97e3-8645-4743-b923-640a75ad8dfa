package net.tartan.platform.integration.entity.erp;

import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 工单/CHT/工單/ENU/MO
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("MO")
public class Mo implements Serializable {

    private static final long serialVersionUID = 1L;

//    /**
//     * 关联部门
//     */
//    @TableField("Owner_Dept")
//    private String ownerDept;
//
//    /**
//     * 关联员工
//     */
//    @TableField("Owner_Emp")
//    private String ownerEmp;

    /**
     * 单号
     */
    @TableField("DOC_NO")
    private String docNo;

    /**
     * 单据日期
     */
    @TableField("DOC_DATE")
    private Date docDate;

//    /**
//     * 单据类型
//     */
//    @TableField("DOC_ID")
//    private String docId;
//
//    /**
//     * 主键
//     */
//    @TableId("MO_ID")
//    private String moId;

        /**
     * 产品品号
     */
    @TableField("ITEM_ID")
    private String itemId;


    /**
     * 产品品名
     */
    @TableField("ITEM_DESCRIPTION")
    private String itemDescription;

    /**
     * 产品规格
     */
    @TableField("ITEM_SPECIFICATION")
    private String itemSpecification;

//    /**
//     * BOM版次
//     */
//    @TableField("BOM_VERSION_TIMES")
//    private String bomVersionTimes;
//
//    /**
//     * BOM日期
//     */
//    @TableField("BOM_DATE")
//    private LocalDateTime bomDate;
//
//    /**
//     * 紧急
//     */
//    @TableField("URGENT")
//    private Boolean urgent;

    /**
     * 状态码
     */
    @TableField("STATUS")
    private String status;
    private String itemCode;//品号
    private String itemName;//品名
    private String docCode;
    private String docName;
    private String lotCode;//批次号
    private String lotDescription;
    private String projectName;//wellNumber
    private String projectCode;//wellId

//    /**
//     * 批工单
//     */
//    @TableField("LOT_MO_FLAG")
//    private Boolean lotMoFlag;
//
//    /**
//     * 预计产量
//     */
//    @TableField("PLAN_QTY")
//    private Double planQty;
//
//    /**
//     * 申请数量
//     */
//    @TableField("REQ_QTY")
//    private Double reqQty;
//
//    /**
//     * 已生产量
//     */
//    @TableField("COMPLETED_QTY")
//    private Double completedQty;
//
//    /**
//     * 报废数量
//     */
//    @TableField("SCRAP_QTY")
//    private Double scrapQty;
//
//    /**
//     * 破坏数量
//     */
//    @TableField("DESTROYED_QTY")
//    private Double destroyedQty;
//
//    /**
//     * 预计开工日期
//     */
//    @TableField("PLAN_START_DATE")
//    private LocalDateTime planStartDate;
//
//    /**
//     * 预计完工日期
//     */
//    @TableField("PLAN_COMPLETE_DATE")
//    private LocalDateTime planCompleteDate;
//
//    /**
//     * 实际开工日期
//     */
//    @TableField("ACTUAL_START_DATE")
//    private LocalDateTime actualStartDate;
//
//    /**
//     * 实际完工日期
//     */
//    @TableField("ACTUAL_COMPLETE_DATE")
//    private LocalDateTime actualCompleteDate;
//
//    /**
//     * 计划批号
//     */
//    @TableField("PLAN_LOT")
//    private String planLot;
//
//    /**
//     * 备注
//     */
//    @TableField("REMARK")
//    private String remark;
//
//    /**
//     * 工艺管理
//     */
//    @TableField("ROUTING_CONTROL")
//    private Boolean routingControl;
//
//    /**
//     * 批工单数量
//     */
//    @TableField("LOT_MO_QTY")
//    private Double lotMoQty;
//
//    /**
//     * 入库申请
//     */
//    @TableField("RECEIPT_REQ_CONTROL")
//    private Boolean receiptReqControl;
//
//    /**
//     * 已采数量
//     */
//    @TableField("SUPPLY_PURCHASED_QTY")
//    private Double supplyPurchasedQty;
//
//    /**
//     * 分配数量
//     */
//    @TableField("SUPPLY_APPLY_QTY")
//    private Double supplyApplyQty;
//
//    /**
//     * 分配状态
//     */
//    @TableField("SUPPLY_DISPATCH_STATUS")
//    private String supplyDispatchStatus;
//

//    /**
//     * 业务单位
//     */
//    @TableField("BUSINESS_UNIT_ID")
//    private String businessUnitId;
//
//    /**
//     * 特征码
//     */
//    @TableField("ITEM_FEATURE_ID")
//    private String itemFeatureId;
//
//    /**
//     * 工艺路线号
//     */
//    @TableField("ITEM_ROUTING_ID")
//    private String itemRoutingId;
//
//    /**
//     * 源工单单号
//     */
//    @TableField("SOURCE_MO_ID")
//    private String sourceMoId;
//
//    /**
//     * 母工单单号
//     */
//    @TableField("PARA_MO_ID")
//    private String paraMoId;
//
//    /**
//     * 部门
//     */
//    @TableField("ADMIN_UNIT_ID")
//    private String adminUnitId;
//
//    /**
//     * 上阶工单单号
//     */
//    @TableField("UP_MO_ID")
//    private String upMoId;
//
//    /**
//     * 生产批号
//     */
//    @TableField("ITEM_LOT_ID")
//    private String itemLotId;
//
//    /**
//     * 主键
//     */
//    @TableField("ASSIGN_FINISH_PERSON")
//    private String assignFinishPerson;
//
//    /**
//     * 最早开工时间
//     */
//    @TableField("ACTUAL_START_DATETIME")
//    private LocalDateTime actualStartDatetime;
//
//    /**
//     * 预计产量第二数量
//     */
//    @TableField("PLAN_SECOND_QTY")
//    private Double planSecondQty;
//
//    /**
//     * 批工单第二数量
//     */
//    @TableField("LOT_MO_SECOND_QTY")
//    private Double lotMoSecondQty;
//
//    /**
//     * 入库申请第二数量
//     */
//    @TableField("REQ_SECOND_QTY")
//    private Double reqSecondQty;
//
//    /**
//     * 已入库第二数量
//     */
//    @TableField("COMPLETED_SECOND_QTY")
//    private Double completedSecondQty;
//
//    /**
//     * 报废第二数量
//     */
//    @TableField("SCRAP_SECOND_QTY")
//    private Double scrapSecondQty;
//
//    /**
//     * 破坏第二数量
//     */
//    @TableField("DESTROYED_SECOND_QTY")
//    private Double destroyedSecondQty;
//
//    /**
//     * 需求单号
//     */
//    @TableField("DEMAND_NO")
//    private String demandNo;
//
//    /**
//     * 工单图片
//     */
//    @TableField("MO_PIC")
//    private String moPic;
//
//    /**
//     * 工艺管理
//     */
//    @TableField("ITEM_ROUTING_CONTROL")
//    private String itemRoutingControl;
//
//    /**
//     * 审核日期
//     */
//    @TableField("TRANSACTION_DATE")
//    private LocalDateTime transactionDate;
//
//    /**
//     * 入库仓库
//     */
//    @TableField("WAREHOUSE_ID")
//    private String warehouseId;
//
//    /**
//     * 项目
//     */
//    @TableField("PROJECT_ID")
//    private String projectId;
//
//    /**
//     * RMA号
//     */
//    @TableField("RMA")
//    private String rma;
//
//    /**
//     * 协同关系
//     */
//    @TableField("SYNERGY_ID")
//    private String synergyId;
//
//    /**
//     * 不计算成本
//     */
//    @TableField("NOT_COST")
//    private Boolean notCost;
//
//    @TableField("ISSUE_BY_OP_SEQ")
//    private Boolean issueByOpSeq;
//
//    @TableField("MO_CAN_PRODUCT")
//    private Boolean moCanProduct;
//
//    /**
//     * 打印次数
//     */
//    @TableField("PrintCount")
//    private Integer printcount;
//
//    /**
//     * 版本号，不要随意更改
//     */
//    @TableField("Version")
//    private LocalDateTime version;
//
//    /**
//     * EF签核码
//     */
//    @TableField("EFNETStatus")
//    private String efnetstatus;
//
//    /**
//     * 签核业务动作
//     */
//    @TableField("EFNETAction")
//    private String efnetaction;
//
//    /**
//     * EFNET单别
//     */
//    @TableField("EFNETDOCCategory")
//    private String efnetdoccategory;
//
//    /**
//     * EFNET单号
//     */
//    @TableField("EFNETDOCNumber")
//    private String efnetdocnumber;
//
//    /**
//     * 创建日期
//     */
//    @TableField("CreateDate")
//    private LocalDateTime createdate;
//
//    /**
//     * 最后修改日期
//     */
//    @TableField("LastModifiedDate")
//    private LocalDateTime lastmodifieddate;
//
//    /**
//     * 修改日期
//     */
//    @TableField("ModifiedDate")
//    private LocalDateTime modifieddate;
//
//    /**
//     * 创建者
//     */
//    @TableField("CreateBy")
//    private String createby;
//
//    /**
//     * 最后修改者
//     */
//    @TableField("LastModifiedBy")
//    private String lastmodifiedby;
//
//    /**
//     * 修改者
//     */
//    @TableField("ModifiedBy")
//    private String modifiedby;
//
//    /**
//     * 附件
//     */
//    @TableField("Attachments")
//    private String attachments;
//
//    /**
//     * 单据状态属性
//     */
//    @TableField("ApproveStatus")
//    private String approvestatus;
//
//    /**
//     * 修改日期
//     */
//    @TableField("ApproveDate")
//    private LocalDateTime approvedate;
//
//    /**
//     * 修改人
//     */
//    @TableField("ApproveBy")
//    private String approveby;
//
//    /**
//     * 自定义字段0
//     */
//    @TableField("UDF001")
//    private Double udf001;
//
//    /**
//     * 自定义字段1
//     */
//    @TableField("UDF002")
//    private Double udf002;
//
//    /**
//     * 自定义字段2
//     */
//    @TableField("UDF003")
//    private Double udf003;
//
//    /**
//     * 自定义字段3
//     */
//    @TableField("UDF011")
//    private Double udf011;
//
//    /**
//     * 自定义字段4
//     */
//    @TableField("UDF012")
//    private Double udf012;
//
//    /**
//     * 自定义字段5
//     */
//    @TableField("UDF013")
//    private Double udf013;
//
//    /**
//     * 自定义字段6
//     */
//    @TableField("UDF021")
//    private String udf021;
//
//    /**
//     * 自定义字段7
//     */
//    @TableField("UDF022")
//    private String udf022;
//
//    /**
//     * 自定义字段8
//     */
//    @TableField("UDF023")
//    private String udf023;
//
//    /**
//     * 自定义字段9
//     */
//    @TableField("UDF024")
//    private String udf024;
//
//    /**
//     * 自定义字段10
//     */
//    @TableField("UDF025")
//    private String udf025;
//
//    /**
//     * 自定义字段11
//     */
//    @TableField("UDF026")
//    private String udf026;
//
//    /**
//     * 自定义字段12
//     */
//    @TableField("UDF041")
//    private LocalDateTime udf041;
//
//    /**
//     * 自定义字段13
//     */
//    @TableField("UDF042")
//    private LocalDateTime udf042;
//
//    /**
//     * 自定义字段14
//     */
//    @TableField("UDF051")
//    private String udf051;
//
//    /**
//     * 自定义字段15
//     */
//    @TableField("UDF052")
//    private String udf052;
//
//    /**
//     * 自定义字段16
//     */
//    @TableField("UDF053")
//    private String udf053;
//
//    /**
//     * 自定义字段17
//     */
//    @TableField("UDF054")
//    private String udf054;
//
//    /**
//     * 表单所在的流程实例的编号
//     */
//    @TableField("ProcessInstanceId")
//    private String processinstanceid;
//
//    @TableField("Owner_Org_RTK")
//    private String ownerOrgRtk;
//
//    @TableField("Owner_Org_ROid")
//    private String ownerOrgRoid;
//
//    @TableField("SOURCE_ID_RTK")
//    private String sourceIdRtk;
//
//    @TableField("SOURCE_ID_ROid")
//    private String sourceIdRoid;
//
//    @TableField("SOURCE_FROM_RTK")
//    private String sourceFromRtk;
//
//    @TableField("SOURCE_FROM_ROid")
//    private String sourceFromRoid;


}
