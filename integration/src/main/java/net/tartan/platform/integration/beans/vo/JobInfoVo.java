package net.tartan.platform.integration.beans.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.integration.beans.vo.dailyreport.ReportBaseVo;
import net.tartan.platform.integration.entity.JobFileRelation;
import net.tartan.platform.integration.entity.dailyreport.MwdTool;

import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class JobInfoVo {
    private long jobId;
    private String jobNumber;
    private String wellNumber;
    private String wellId;
    private Long jobType;
    private String jobTypeStr;
    private String kitNumber;
    private List<ReportBaseVo> runList;
    private List<MwdTool> toolList;
    private List<JobFileRelation> fileList;
    private int jobStatus;
}
