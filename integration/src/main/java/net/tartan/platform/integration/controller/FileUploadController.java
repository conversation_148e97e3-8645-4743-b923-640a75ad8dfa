package net.tartan.platform.integration.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.common.beans.ResultCode;
import net.tartan.platform.common.enums.EnumFileBusinessType;
import net.tartan.platform.integration.beans.dto.FileDto;
import net.tartan.platform.integration.beans.vo.FileInfoVo;
import net.tartan.platform.integration.entity.massFile.MassFileInfo;
import net.tartan.platform.integration.entity.massFile.MassFileModel;
import net.tartan.platform.integration.exception.BusinessException;
import net.tartan.platform.integration.service.FileInfoService;
import net.tartan.platform.integration.utils.FileUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Objects;

/**
 * file 文件管理
 */
@Controller
@RequestMapping(value = "file")
public class FileUploadController {
    @Autowired
    private FileInfoService fileInfoService;
    @Autowired
    private HttpServletResponse httpServletResponse;

    @ResponseBody
    @RequestMapping("upload")
    public CommonResult upload(@RequestParam(value = "file") MultipartFile file,
                               @RequestParam(value = "businessType") EnumFileBusinessType businessType) {
        fileInfoService.uploadFile(file, businessType);
        return CommonResult.success();
    }

    @ResponseBody
    @GetMapping("list")
    public CommonResult list(@RequestParam(value = "businessType") EnumFileBusinessType businessType) {
        List<FileInfoVo> list = fileInfoService.list(businessType);
        return CommonResult.success(list);
    }

    @GetMapping("view")
    public void view(@RequestParam("fileId") String fileId) throws IOException {
        byte[] file = fileInfoService.findByFileId(fileId);
        httpServletResponse.setContentType("application/octet-stream");
        httpServletResponse.addHeader("Content-Disposition", "attachment; filename=\"" + fileId + ".pdf" + "\"");
        ServletOutputStream outputStream = null;
        try {
            outputStream = httpServletResponse.getOutputStream();
            outputStream.write(file);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (Objects.nonNull(outputStream)) {
                outputStream.close();
            }
        }
    }

    @GetMapping("download")
    public void downLoad(@RequestParam("fileId") String fileId) throws IOException {
        FileDto fileDto = fileInfoService.downloadFile(fileId);
        httpServletResponse.setContentType("application/octet-stream");
        httpServletResponse.addHeader("Content-Disposition", "attachment; filename=\"" + fileDto.getFileName() + "\"");
        ServletOutputStream outputStream = null;
        try {
            outputStream = httpServletResponse.getOutputStream();
            outputStream.write(fileDto.getFile());
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (Objects.nonNull(outputStream)) {
                outputStream.close();
            }
        }
    }

    @ResponseBody
    @RequestMapping("delete")
    public CommonResult delete(@RequestParam("fileId") String fileId) {
        fileInfoService.deleteByFileId(fileId);
        return CommonResult.success();
    }

    @GetMapping("sensorOffsets/stream")
    public void getSensorOffsetsFile() throws IOException {
        byte[] file = fileInfoService.downloadSensorOffsetsFile();
        httpServletResponse.setContentType("application/octet-stream");
        httpServletResponse.addHeader("Content-Disposition", "attachment; filename=SensorOffsets.xlsx" + "\"");
        ServletOutputStream outputStream = null;
        try {
            outputStream = httpServletResponse.getOutputStream();
            outputStream.write(file);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (Objects.nonNull(outputStream)) {
                outputStream.close();
            }
        }
    }

    @ResponseBody
    @RequestMapping("sensorOffsets/save")
    public CommonResult saveSensorOffsetsFile(@RequestParam(value = "file") MultipartFile file) throws IOException {
        fileInfoService.saveSensorOffsetsFile(file);
        return CommonResult.success();
    }

//
    @ResponseBody
    @RequestMapping("uploadFile")
    public CommonResult uploadFile(@RequestParam(value = "file") MultipartFile file,
                                   @RequestParam(value = "type", defaultValue = "img") String type) {
        if(ObjectUtils.isEmpty(file)){
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        String message = fileInfoService.uploadFiles(file, type);
        return CommonResult.success(message);
    }
}
