package net.tartan.platform.integration.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.integration.beans.dto.OutboundOrderDto;
import net.tartan.platform.integration.beans.query.OutboundQuery;
import net.tartan.platform.integration.beans.vo.MwdLoadOutVo;
import net.tartan.platform.integration.beans.vo.OutboundOrderVo;
import net.tartan.platform.integration.beans.vo.OutboundStockVo;
import net.tartan.platform.integration.service.IOutboundOrderService;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 出库单
 */
@RestController
@RequestMapping("/outboundOrder")
public class OutboundOrderController {
    @Autowired
    private IOutboundOrderService outboundOrderService;

    @PostMapping("queryList/{current}/{size}")
    public CommonResult queryList(@PathVariable long current, @PathVariable long size,
                                  @RequestBody OutboundQuery outboundQuery) {
        if(ObjectUtils.isEmpty(outboundQuery)){
            outboundQuery = new OutboundQuery();
        }
        IPage<OutboundStockVo> page = new Page<>(current, size);
        return CommonResult.success(outboundOrderService.getOutboundOrderList(page, outboundQuery));
    }
    @PostMapping("list/{current}/{size}")
    public CommonResult list(@PathVariable long current, @PathVariable long size,
                                  @RequestBody OutboundQuery outboundQuery) {
        if(ObjectUtils.isEmpty(outboundQuery)){
            outboundQuery = new OutboundQuery();
        }
        IPage<OutboundOrderVo> page = new Page<>(current, size);
        return CommonResult.success(outboundOrderService.list(page, outboundQuery));
    }
    @PostMapping("insert")
    public CommonResult insertOrder(@RequestBody OutboundOrderVo outboundOrderVo) {
        if(ObjectUtils.isEmpty(outboundOrderVo)) {
            return CommonResult.success();
        }
        outboundOrderService.insertOrder(outboundOrderVo);
        return CommonResult.success();
    }

    @PostMapping("batchInsertMwdOrder")
    public CommonResult batchInsertMwdOrder(@RequestBody OutboundOrderDto outboundOrderDto) {
        if(ObjectUtils.isEmpty(outboundOrderDto)) {
            return CommonResult.success();
        }
        outboundOrderService.batchInsertMwdOrder(outboundOrderDto);
        return CommonResult.success();
    }

    @PostMapping("delete")
    public CommonResult delete(@RequestBody OutboundQuery outboundQuery) {
        if(ObjectUtils.isEmpty(outboundQuery) || ObjectUtils.isEmpty(outboundQuery.getId())) {
            return CommonResult.success();
        }
        outboundOrderService.deleteOrder(outboundQuery.getId());
        return CommonResult.success();
    }

    @PostMapping("updateStatus")
    @ResponseBody
    public CommonResult updateStatus(@RequestParam("idList") List<Long> idList) {
        if(ObjectUtils.isEmpty(idList)) {
            return CommonResult.success();
        }
        outboundOrderService.updateCheckUser(idList);
        return CommonResult.success();
    }
}
