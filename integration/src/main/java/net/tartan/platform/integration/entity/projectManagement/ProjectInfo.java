package net.tartan.platform.integration.entity.projectManagement;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <p>
 * 项目基础信息
 * </p>
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ProjectInfo {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 项目名
     */
    private String projectName;

    /**
     * 项目编号
     */
    private String projectNumber;

    /**
     * 项目状态，0 -> 进行中， 1 -> 已结项
     */
    private Integer projectStatus;

    /**
     * 项目经理的memberId
     * 与member_info的member_id关联
     */
    private String projectManagerId;


    /**
     * 总监
     * （审批的第二个节点）
     */
    private String projectDirectorId;

    /**
     * 开始时间
     */
    private String startDate;

    /**
     * 结束时间
     */
    private String endDate;

    /**
     * 创建人姓名
     */
    private String createBy;

    /**
     * 最后编辑人姓名
     */
    private String lastUpdateBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
