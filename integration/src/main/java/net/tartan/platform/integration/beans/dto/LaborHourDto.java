package net.tartan.platform.integration.beans.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LaborHourDto {

    private Long id;

    /**
     * 工时考勤日期 （填报当天日期）
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date laborDate;

    /**
     * 星期几
     */
    private String week;

    /**
     * 所属项目编号
     */
    private Long projectId;

    /**
     * 时间区间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 根据时间区间计算出的工时
     * 午休为 12:00-13:00， 需要扣除休息时间
     */
    private BigDecimal laborHours;

    /**
     * 工作内容总结
     */
    private String workSummary;

    // false 表示非测试数据 走正常日期校验
    // true 表示测试数据 跳过日期校验
    private boolean testLine = false;
}
