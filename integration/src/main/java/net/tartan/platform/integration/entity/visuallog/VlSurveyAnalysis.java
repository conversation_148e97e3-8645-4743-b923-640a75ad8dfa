package net.tartan.platform.integration.entity.visuallog;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * <AUTHOR>
 * @since 2023-10-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class VlSurveyAnalysis implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Long svyRecId;

    /**
     * 作业号
     */
    private Long jobId;
    private Long bitRunId;

    /**
     * 测深
     */
    private BigDecimal mDepth;

    /**
     * 井斜角
     */
    private BigDecimal inc;

    /**
     * 方位角
     */
    private BigDecimal azm;

    private BigDecimal x;

    private BigDecimal y;

    private BigDecimal z;

    /**
     * 垂深
     */
    private BigDecimal tvd;

    private BigDecimal vs;

    private Date tDate;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


}
