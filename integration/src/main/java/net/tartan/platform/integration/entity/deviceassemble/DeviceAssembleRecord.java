package net.tartan.platform.integration.entity.deviceassemble;

import lombok.Data;
import java.util.Date;
import java.util.List;

@Data
public class DeviceAssembleRecord {
//    /**
//     * 品号
//     */
//    private String invCode;
    /**
     * 品名
     */
    private String invName;
//    /**
//     * 规格
//     */
//    private String invStd;

    /**
     * 序列号，erp的批次号
     */
    private String serialNumber;
    /**
     * 备注
     */
    private String note;

    private List<AssembleComponent> inComponentList;

    private List<AssembleComponent> outComponentList;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
