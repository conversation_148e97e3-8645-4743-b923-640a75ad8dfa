package net.tartan.platform.integration.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import java.util.concurrent.Executor;

/**
 * 异步方法执行和定时任务 使用的线程池配置
 * 应用范围：
 * - 全局搜@Async注解的异步方法
 * - 全局搜@Scheduled注解的定时任务
 */
@Configuration
@EnableAsync
public class AsyncConfig {

    /**
     * 异步任务执行器
     * 用于处理@Async注解的异步方法
     */
    @Bean(name = "taskExecutor")
    public Executor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(10);
        executor.setQueueCapacity(25);
        executor.setThreadNamePrefix("AsyncExecutor-");
        executor.initialize();
        return executor;
    }

    /**
     * 定时任务执行器
     * 用于处理@Scheduled注解的定时任务
     * 配置独立的线程池，避免定时任务占用异步任务的资源
     */
    @Bean(name = "taskScheduler")
    public ThreadPoolTaskScheduler taskScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(5);
        scheduler.setThreadNamePrefix("ScheduledTask-");
        scheduler.setAwaitTerminationSeconds(60);
        scheduler.setWaitForTasksToCompleteOnShutdown(true);
        scheduler.setErrorHandler(throwable -> {
            // 定时任务异常处理，防止定时任务因异常中断
            System.err.println("定时任务执行异常: " + throwable.getMessage());
            throwable.printStackTrace();
        });
        return scheduler;
    }
}
