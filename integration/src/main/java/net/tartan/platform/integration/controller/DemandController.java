package net.tartan.platform.integration.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.common.beans.ResultCode;
import net.tartan.platform.integration.beans.dto.DemandOrderInfoDto;
import net.tartan.platform.integration.beans.query.DemandOrderQuery;
import net.tartan.platform.integration.beans.vo.DemandOrderInfoVo;
import net.tartan.platform.integration.exception.BusinessException;
import net.tartan.platform.integration.service.IDemandOrderService;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 需求单
 */
@Slf4j
@RestController
@RequestMapping("demandOrder")
public class DemandController {
    @Autowired
    private IDemandOrderService demandOrderService;

    @PostMapping("list/{current}/{size}")
    public CommonResult list(@PathVariable long current,
                                          @PathVariable long size,
                                          @RequestBody DemandOrderQuery query) {
        IPage<DemandOrderInfoVo> page = new Page<>(current, size);
        demandOrderService.list(page, query);
        return CommonResult.success(page);
    }

    @PostMapping("info")
    public CommonResult info(@RequestBody DemandOrderQuery query) {

        return CommonResult.success(demandOrderService.info(query));
    }

    @PostMapping("insert")
    public CommonResult insertDevice(@RequestBody DemandOrderInfoDto dto) {

        return CommonResult.success(demandOrderService.insert(dto));
    }

    @PostMapping("update")
    public CommonResult update(@RequestBody DemandOrderInfoDto dto) {
        demandOrderService.update(dto);
        return CommonResult.success();
    }
    @PostMapping("updateStatus")
    public CommonResult updateStatus(@RequestBody DemandOrderInfoDto dto) {
        demandOrderService.updateStatus(dto);
        return CommonResult.success();
    }

    @PostMapping("exportDetail")
    public CommonResult exportRepair(@RequestBody DemandOrderQuery query) {
        if (ObjectUtils.isEmpty(query)) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        demandOrderService.export(query);
        return CommonResult.success();
    }
    @PostMapping("delete")
    public CommonResult delete(@RequestBody DemandOrderQuery query) {
        if (ObjectUtils.isEmpty(query)) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        demandOrderService.delete(query);
        return CommonResult.success();
    }
}
