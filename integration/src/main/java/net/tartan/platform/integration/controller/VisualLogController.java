package net.tartan.platform.integration.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.common.enums.EnumVisualLogDataType;
import net.tartan.platform.integration.beans.dto.visuallog.VisualLogDataIn;
import net.tartan.platform.integration.beans.dto.visuallog.VisualLogDto;
import net.tartan.platform.integration.beans.vo.visuallog.VisualLogCoordinateVo;
import net.tartan.platform.integration.beans.vo.visuallog.VisualLogDataVo;
import net.tartan.platform.integration.entity.visuallog.VlTagPool;
import net.tartan.platform.integration.factory.VisualLogFactory;
import net.tartan.platform.integration.service.visuallog.VisualLogServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023-10-08
 */
@RestController
@RequestMapping("/visuallog")
public class VisualLogController {
    @Autowired
    private VisualLogServiceImpl visualLogService;

    @PostMapping("data/import")
    public CommonResult visualLogDataImport(@RequestBody List<VisualLogDataIn> dataInList) {
        if (CollectionUtils.isEmpty(dataInList)) {
            return CommonResult.success();
        }
        for (VisualLogDataIn data : dataInList) {
            EnumVisualLogDataType dataType = data.getDataType();
            VisualLogFactory.getInstance(dataType).addDataBatch(data.getDataList().toString());
        }
        return CommonResult.success();
    }

    @GetMapping("tag")
    public CommonResult getTag() {
        List<VlTagPool> tagPoolList = visualLogService.getTag();
        return CommonResult.success(tagPoolList);
    }

    @PostMapping("data/show")
    public CommonResult dataShow(@RequestBody VisualLogDto visualLogDto) {
        List<VisualLogCoordinateVo> data = visualLogService.dataShow(visualLogDto);
        return CommonResult.success(data);
    }

    @PostMapping("data/imaging/{smooth}")
    public CommonResult getImagingData(@PathVariable boolean smooth,
                                       @RequestBody String param) {
        Map<String,List<VisualLogDataVo>> dataMap = JSONObject.parseObject(param,new TypeReference<HashMap<String,List<VisualLogDataVo>>>(){});
        List<VisualLogCoordinateVo> data = visualLogService.getImagingData(dataMap, smooth);
        return CommonResult.success(data);
    }
}
