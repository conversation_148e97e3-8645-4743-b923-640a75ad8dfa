//package net.tartan.platform.integration.beans.request;
//
//import lombok.AllArgsConstructor;
//import lombok.Builder;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//
//import javax.validation.constraints.Min;
//import javax.validation.constraints.NotBlank;
//import javax.validation.constraints.NotNull;
//import java.io.Serializable;
//
//@Builder
//@NoArgsConstructor
//@AllArgsConstructor
//@Data
//public class AddMenuParams implements Serializable {
//    /**
//     * 父级ID
//     */
//    private long parentId;
//
//    /**
//     * 菜单名称
//     */
////    @NotBlank
////    private String title;
//
//    /**
//     * 菜单级数
//     */
//    @NotNull
//    @Min(1)
//    private Integer level;
//
//    /**
//     * 菜单排序
//     */
//    private Integer sort;
//
//    /**
//     * 前端名称
//     */
//    @NotBlank
//    private String name;
//
//    /**
//     * 前端图标
//     */
//    private String icon;
//
//    /**
//     * 前端隐藏
//     */
//    private Integer hidden;
//}
