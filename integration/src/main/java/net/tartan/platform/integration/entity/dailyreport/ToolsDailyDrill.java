package net.tartan.platform.integration.entity.dailyreport;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * tools_daily_drill
 * <AUTHOR>
public class ToolsDailyDrill implements Serializable {
    private Long id;

    /**
     * 工具日报id
     */
    private Long toolsReportId;

    /**
     * 钻压（吨）
     */
    private BigDecimal wob;

    /**
     * 转速（转/分）
     */
    private BigDecimal speed;

    /**
     * 排量（升/秒）
     */
    private BigDecimal displacement;

    /**
     * 泵压Mpa
     */
    private BigDecimal pumpPressure;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getToolsReportId() {
        return toolsReportId;
    }

    public void setToolsReportId(Long toolsReportId) {
        this.toolsReportId = toolsReportId;
    }

    public BigDecimal getWob() {
        return wob;
    }

    public void setWob(BigDecimal wob) {
        this.wob = wob;
    }

    public BigDecimal getSpeed() {
        return speed;
    }

    public void setSpeed(BigDecimal speed) {
        this.speed = speed;
    }

    public BigDecimal getDisplacement() {
        return displacement;
    }

    public void setDisplacement(BigDecimal displacement) {
        this.displacement = displacement;
    }

    public BigDecimal getPumpPressure() {
        return pumpPressure;
    }

    public void setPumpPressure(BigDecimal pumpPressure) {
        this.pumpPressure = pumpPressure;
    }
}
