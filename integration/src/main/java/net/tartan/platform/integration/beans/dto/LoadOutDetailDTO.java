package net.tartan.platform.integration.beans.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 旋导出库记录详情DTO
 */
@Data
public class LoadOutDetailDTO {
    /**
     * 出库记录详情ID
     */
    private Long id;
    
    /**
     * 出库记录ID
     */
    private Long loadOutId;
    
    /**
     * 父ID
     */
    private Integer parentId;
    
    /**
     * 单元编号
     */
    private String unitNumber;
    
    /**
     * 类型名称
     */
    private String typeName;
    
    /**
     * 单元名称
     */
    private String unitName;
    
    /**
     * 设备ID
     */
    private Long deviceId;
    
    /**
     * 序列号
     */
    private String serialNumber;
    
    /**
     * 仪器状态
     */
    private Long status;
    
    /**
     * 需求数量
     */
    private Integer requireAmount;
    
    /**
     * 实际数量
     */
    private Integer actualAmount;
    
    /**
     * 入库数量
     */
    private Integer loadInAmount;
    
    /**
     * 设备类型列表
     */
    private String deviceTypeList;
    
    /**
     * 其他名称
     */
    private String otherName;
    
    /**
     * 备注
     */
    private String note;
    
    /**
     * 显示顺序
     */
    private Integer displayOrder;
    
    /**
     * 是否分类
     */
    private Boolean isCategory;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 子项列表
     */
    private List<LoadOutDetailDTO> units;
} 