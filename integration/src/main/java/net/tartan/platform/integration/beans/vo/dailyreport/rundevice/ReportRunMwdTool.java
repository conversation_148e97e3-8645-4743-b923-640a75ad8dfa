package net.tartan.platform.integration.beans.vo.dailyreport.rundevice;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReportRunMwdTool {


    private Long repairDetailId;

    /**
     * 返修单号
     */
    private Long repairId;

    /**
     * 存货编码
     */
    private String invCode;
    /**
     * 存货名称
     */
    private String invName;

    /**
     * 序列号
     */
    private String serialNumber;

    /**
     * 开始
     */
    private BigDecimal start;

    /**
     * 今天
     */
    private BigDecimal today;

    /**
     * 总计
     */
    private BigDecimal total;

    /**
     * 返回原因
     */
    private String returnReason;

    /**
     * 返修单的备注
     */
    private String note;
    /**
     * 维修工单号
     */
    private Long mwdId;

    /**
     * 维修工单号
     */
    private String mwdNumber;
    /**
     * 车间发现
     */
    private String findings;

    /**
     * 维修方案 -> 方案&致行措施
     */
    private String repairAction;
}
