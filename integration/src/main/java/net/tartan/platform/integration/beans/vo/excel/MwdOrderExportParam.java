package net.tartan.platform.integration.beans.vo.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import net.tartan.platform.common.enums.EnumRiskType;

import java.math.BigDecimal;

@Data
public class MwdOrderExportParam {
    @ExcelProperty("序号")
    private Long mwdId;

    /**
     * 工单单号
     */
    @ExcelProperty("WO No.\n工单号")
    private String mwdNumber;
    /**
     * 仪器类型
     */
    @ExcelProperty("Tool Type\n工具类型")
    private String deviceTypeStr;

    @ExcelIgnore
    private String riskType;
    @ExcelProperty("仪器风险类型")
    private String riskTypeStr;
    /**
     * 品名(主要用于没有序列号的工单，知道修的叫啥名)
     */
    @ExcelProperty("品名")
    private String invName;

    /**
     * 序列号，批次号
     */
    @ExcelProperty("S/N\n序列号")
    private String serialNumber;
    /**
     * kit箱编号
     */
    @ExcelProperty("Kit#\n所属Kit")
    private String kitNumber;
    /**
     * 维修状态1：已完成，0：未完成，-1：滞留
     */
    @ExcelIgnore
    private Integer status;
    @ExcelProperty("Maintenance status\n维修状态")
    private String statusStr;

    @ExcelProperty("Load in Main parts SN\n入厂主要部件序列号")
    private String inSN;
    @ExcelProperty("Load out Main parts SN\n出厂主要部件序列号")
    private String outSN;

    /**
     * 所有者
     */
    @ExcelProperty("Owner\n所有者")
    private String owner;

    /**
     * 作业编号
     */
    @ExcelProperty("Field Job #\n上井工单号")
    private String jobNumber;

    /**
     * 井号
     */
    @ExcelProperty("Well#\n井号")
    private String wellNumber;

    /**
     * 现场联系人
     */
    @ExcelProperty("Field Operator\n现场联系人")
    private String contactUser;
    /**
     * 现场联系人的联系方式
     */
    @ExcelProperty("Number\n联系方式")
    private String contactNumber;

    /**
     * 维修人员
     */
    @ExcelProperty("Sponsor\n维修人员")
    private String repairUser;
    /**
     * 接收日期
     */
    @ExcelProperty("Received date\n接收日期")
    private String receiveDate;

    /**
     * 开始日期
     */
    @ExcelProperty("Repairing Start Date\n维修开始日期")
    private String startDate;

    /**
     * 预计完成日期
     */
    @ExcelProperty("Estimated Completion Date\n预计完成日期")
    private String estimatedEndDate;

    /**
     * 完成日期
     */
    @ExcelProperty("Completion Date\n维修完成日期")
    private String endDate;

    /**
     * 返回原因
     */
    @ExcelProperty("Return Reason\n仪器返回原因")
    private String returnReason;

    /**
     * 车间发现
     */
    @ExcelProperty("Findings\n车间检查发现")
    private String findings;

    /**
     * 根本原因
     */
    @ExcelProperty("Root cause\n根本原因")
    private String rootReason;
    /**
     * 维修方案 -> 方案&致行措施
     */
    @ExcelProperty("Actions\n方案&执行措施")
    private String repairAction;

    /**
     * 入井时间
     */
    @ExcelProperty("Used Hours\n入井时间")
    private BigDecimal inWellHour;

    /**
     * 循环时间
     */
    @ExcelProperty("Circulate Hrs\n循环时间")
    private BigDecimal circulateHrs;

    /**
     * 最高温度
     */
    @ExcelProperty("Max Temp\n最高温度")
    private BigDecimal maxBht;
    /**
     * 入井趟次
     */
    @ExcelProperty("使用多少趟次\nRUN counts")
    private String run;
    /**
     * 故障类型
     */
//    private Long failureType;
    @ExcelProperty("Failure Type\n故障类型")
    private String failureTypeStr;

    /**
     * 失效部件分类
     */
//    private Long failureComponentType;
    @ExcelProperty("failed part label\n失效部件分类")
    private String failureComponentTypeStr;
    /**
     * 主要失效原因分类
     */
//    private Long failureReasonType;
    @ExcelProperty("主要失效原因分类")
    private String failureReasonTypeStr;

    /**
     * 维修耗时 -> 工时
     */
    @ExcelProperty("Labor Hours\n工时")
    private BigDecimal laborHours;

    /**
     * 备注
     */
    @ExcelProperty("Notes\n备注")
    private String notes;

}
