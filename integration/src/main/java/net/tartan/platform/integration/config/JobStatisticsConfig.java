package net.tartan.platform.integration.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "job.statistics")
public class JobStatisticsConfig {

    /**
     * 近钻头大类编码
     */
    private String atBitInvClassCode;
    /**
     * 方位gamma大类编码
     */
    private String azimuthGammaInvClassCode;
    /**
     * 自然gamma大类编码
     */
    private String naturalGammaInvClassCode;
    /**
     * 失效报告脉冲对应名称
     */
    private Long failureReasonPulserComponentId;
    /**
     * 失效报告底部总成对应名称
     */
    private Long failureReasonBnaComponentId;
    /**
     * 失效报告探管对应名称
     */
    private Long failureReasonDmComponentId;
    /**
     * 失效报告接收器对应名称
     */
    private Long failureReasonRecComponentId;
    /**
     * 失效报告电池对应名称
     */
    private Long failureReasonBat1ComponentId;
    private Long failureReasonBat2ComponentId;
    /**
     * 失效报告伽马对应名称
     */
    private Long failureReasonGammaComponentId;
    /**
     * 失效报告近钻头对应名称
     */
    private Long failureReasonAtBitComponentId;
    /**
     * 失效报告其他对应名称
     */
    private Long failureReasonOtherComponentId;
}
