package net.tartan.platform.integration.entity.dailyreport;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.constant.PfConstant;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * mwd_ro_procedures_report
 *
 * <AUTHOR>
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
@Document(collection = PfConstant.MWD_ROP_REPORT)
public class MwdRoProceduresReport implements Serializable {
    @Transient
    private Long id;

    @Id
    @JsonIgnore
    private String reportId;

    /**
     * 作业id
     */
    @NotNull
    private Long jobId;

    /**
     * 操作人
     */
    private String operator;

    /**
     * mwd操作人
     */
    private String mwdOperator;

    /**
     * 签名日期yyyy-MM-dd
     */
    private String signatureDate;

    /**
     * 截屏保存key
     */
    private String screenShoot;

    /**
     * 工具
     */
    private List<MwdRoProceduresAction> actionList;

    private static final long serialVersionUID = 1L;

}
