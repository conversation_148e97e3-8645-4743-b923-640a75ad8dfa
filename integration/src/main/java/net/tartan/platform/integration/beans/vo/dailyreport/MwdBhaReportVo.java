package net.tartan.platform.integration.beans.vo.dailyreport;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.enums.EnumReportType;
import net.tartan.platform.integration.entity.dailyreport.MwdBhaItem;

import java.math.BigDecimal;
import java.util.List;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MwdBhaReportVo {

    private EnumReportType reportType;

    private long lastModified;

    /**
     * 趟次
     */
    private Integer run;

    /**
     * 公司
     */
    private String company;

    /**
     * 承包商
     */
    private String contractor;

    /**
     * 井队号
     */
    private String rigNo;

    /**
     * 开始时间
     */
    private String startDate;

    /**
     * 井号
     */
    private String wellNumber;

    /**
     * 位置
     */
    private String location;

    /**
     * 入井井深
     */
    private BigDecimal depthIn;

    /**
     * 出井井深
     */
    private BigDecimal depthOut;

    private String bha;

    /**
     * 目的
     */
    private String purpose;

    /**
     * 测斜零长
     */
    private BigDecimal surveySensor;

    /**
     * 伽马零长
     */
    private BigDecimal gammaSensor;

    private String bhaResults;

    /**
     * 起钻原因
     */
    private String reasonForPooh;

    /**
     * 定向工程师
     */
    private String directionalDrillers;

    /**
     * 钻具组合
     */
    private List<MwdBhaItem> itemList;
}
