package net.tartan.platform.integration.entity.erp;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 调拨单/CHT/調撥單/ENU/Transfer Application Doc.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("TRANSFER_DOC")
public class ErpTransferDoc implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 单号
     */
    @TableField("DOC_NO")
    private String docNo;

    /**
     * 单据日期
     */
    @TableField("DOC_DATE")
    private Date docDate;

    /**
     * 单据类型
     */
    @TableField("DOC_ID")
    private String docId;

    /**
     * 主键
     */
    @TableId("TRANSFER_DOC_ID")
    private String transferDocId;

    private String transferDocDId;

    /**
     * 交易日期
     */
    @TableField("TRANSACTION_DATE")
    private Date transactionDate;

    /**
     * 转入工厂
     */
    @TableField("TO_PLANT_ID")
    private String toPlantId;

//    /**
//     * 限用转出仓库
//     */
//    @TableField("FROM_WAREHOUSE_ID")
//    private String fromWarehouseId;

//    /**
//     * 限用转入仓库
//     */
//    @TableField("TO_WAREHOUSE_ID")
//    private String toWarehouseId;
//
//    /**
//     * 在途仓
//     */
//    @TableField("IN_WAREHOUSE_ID")
//    private String inWarehouseId;

    /**
     * 预计拨入日期
     */
    @TableField("TRANSFER_IN_DATE")
    private Date transferInDate;

    /**
     * 修改日期
     */
    @TableField("ApproveDate")
    private Date approveDate;
    /**
     * 版本号，不要随意更改
     */
    @TableField("Version")
    private byte[] version;

    private String itemCode;
    private String lotCode;
    private String itemDescription;
    private String itemSpecification;
    private String docName;
    private String plantCode;
    private String plantName;
    private Double inventoryQty;
    private String fromWarehouseId;
    private String fromBinId;
    private String toWarehouseId;
    private String toBinId;

    /**
     * 最后修改日期
     */
    @TableField("LastModifiedDate")
    private Date lastmodifieddate;
//    /**
//     * 关联部门
//     */
//    @TableField("Owner_Dept")
//    private String ownerDept;
//
//    /**
//     * 关联员工
//     */
//    @TableField("Owner_Emp")
//    private String ownerEmp;
//    /**
//     * 备注
//     */
//    @TableField("REMARK")
//    private String remark;
//
//    /**
//     * 单据性质码
//     */
//    @TableField("CATEGORY")
//    private String category;
//
//    @TableField("PIECES")
//    private Integer pieces;
//    @TableField("COMPANY_ID")
//    private String companyId;
//    /**
//     * 会计年度
//     */
//    @TableField("ACCOUNT_YEAR")
//    private String accountYear;
//
//    /**
//     * 会计期间序号
//     */
//    @TableField("ACCOUNT_PERIOD_SEQNO")
//    private Integer accountPeriodSeqno;
//
//    /**
//     * 已生成运营账簿分录
//     */
//    @TableField("GLOB_JE_INDICATOR")
//    private Boolean globJeIndicator;
//
//    /**
//     * 已生成管理账簿分录
//     */
//    @TableField("GLMB_JE_INDICATOR")
//    private Boolean glmbJeIndicator;
//
//    /**
//     * 运营账簿分录
//     */
//    @TableField("GLOB_JE_ID")
//    private String globJeId;
//
//    /**
//     * 管理账簿分录
//     */
//    @TableField("GLMB_JE_ID")
//    private String glmbJeId;
//
//    @TableField("ACCOUNT_PERIOD_CODE")
//    private String accountPeriodCode;
//
//    /**
//     * 内部调拨单
//     */
//    @TableField("INNER_TRANSFER_DOC_ID")
//    private String innerTransferDocId;
//
//    @TableField("GENERATE_SOURCE")
//    private String generateSource;
//
//    /**
//     * EF签核码
//     */
//    @TableField("EFNETStatus")
//    private String efnetstatus;
//
//    /**
//     * 签核业务动作
//     */
//    @TableField("EFNETAction")
//    private String efnetaction;
//
//    /**
//     * EFNET单别
//     */
//    @TableField("EFNETDOCCategory")
//    private String efnetdoccategory;
//
//    /**
//     * EFNET单号
//     */
//    @TableField("EFNETDOCNumber")
//    private String efnetdocnumber;
//
//    /**
//     * 打印次数
//     */
//    @TableField("PrintCount")
//    private Integer printcount;
//
//    /**
//     * 创建日期
//     */
//    @TableField("CreateDate")
//    private LocalDateTime createdate;
//
//    /**
//     * 修改日期
//     */
//    @TableField("ModifiedDate")
//    private LocalDateTime modifieddate;
//
//    /**
//     * 创建者
//     */
//    @TableField("CreateBy")
//    private String createby;
//
//    /**
//     * 最后修改者
//     */
//    @TableField("LastModifiedBy")
//    private String lastmodifiedby;
//
//    /**
//     * 修改者
//     */
//    @TableField("ModifiedBy")
//    private String modifiedby;
//
//    /**
//     * 附件
//     */
//    @TableField("Attachments")
//    private String attachments;
//
//    /**
//     * 单据状态属性
//     */
//    @TableField("ApproveStatus")
//    private String approvestatus;
//    /**
//     * 修改人
//     */
//    @TableField("ApproveBy")
//    private String approveby;
//
//    /**
//     * 表单所在的流程实例的编号
//     */
//    @TableField("ProcessInstanceId")
//    private String processinstanceid;
//    /**
//     * 自定义字段0
//     */
//    @TableField("UDF001")
//    private Double udf001;
//
//    /**
//     * 自定义字段1
//     */
//    @TableField("UDF002")
//    private Double udf002;
//
//    /**
//     * 自定义字段2
//     */
//    @TableField("UDF003")
//    private Double udf003;
//
//    /**
//     * 自定义字段3
//     */
//    @TableField("UDF011")
//    private Double udf011;
//
//    /**
//     * 自定义字段4
//     */
//    @TableField("UDF012")
//    private Double udf012;
//
//    /**
//     * 自定义字段5
//     */
//    @TableField("UDF013")
//    private Double udf013;
//
//    /**
//     * 自定义字段6
//     */
//    @TableField("UDF021")
//    private String udf021;
//
//    /**
//     * 自定义字段7
//     */
//    @TableField("UDF022")
//    private String udf022;
//
//    /**
//     * 自定义字段8
//     */
//    @TableField("UDF023")
//    private String udf023;
//
//    /**
//     * 自定义字段9
//     */
//    @TableField("UDF024")
//    private String udf024;
//
//    /**
//     * 自定义字段10
//     */
//    @TableField("UDF025")
//    private String udf025;
//
//    /**
//     * 自定义字段11
//     */
//    @TableField("UDF026")
//    private String udf026;
//
//    /**
//     * 自定义字段12
//     */
//    @TableField("UDF041")
//    private LocalDateTime udf041;
//
//    /**
//     * 自定义字段13
//     */
//    @TableField("UDF042")
//    private LocalDateTime udf042;
//
//    /**
//     * 自定义字段14
//     */
//    @TableField("UDF051")
//    private String udf051;
//
//    /**
//     * 自定义字段15
//     */
//    @TableField("UDF052")
//    private String udf052;
//
//    /**
//     * 自定义字段16
//     */
//    @TableField("UDF053")
//    private String udf053;
//
//    /**
//     * 自定义字段17
//     */
//    @TableField("UDF054")
//    private String udf054;
//
//    @TableField("Owner_Org_RTK")
//    private String ownerOrgRtk;
//
//    @TableField("Owner_Org_ROid")
//    private String ownerOrgRoid;
//
//    /**
//     * 单据类型
//     */
//    @TableField("XSOURCE_TYPE")
//    private String xsourceType;
//
//    /**
//     * 来源单据
//     */
//    @TableField("XSOURCE_NO")
//    private String xsourceNo;


}
