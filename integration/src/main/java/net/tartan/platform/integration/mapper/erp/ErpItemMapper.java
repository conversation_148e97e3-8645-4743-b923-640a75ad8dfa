package net.tartan.platform.integration.mapper.erp;

import net.tartan.platform.integration.entity.erp.Item;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 通用品号信息/CHT/通用品號資料/ENU/General Item Data Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-23
 */
@Mapper
public interface ErpItemMapper extends BaseMapper<Item> {

    Item getLatestTimestamp();

    List<Item> listByVersion(@Param("startSyncTimestamp") byte[] startSyncTimestamp, @Param("endSyncTimestamp") byte[] endSyncTimestamp);

}
