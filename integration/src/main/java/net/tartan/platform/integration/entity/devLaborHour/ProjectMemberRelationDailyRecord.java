package net.tartan.platform.integration.entity.devLaborHour;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 每日记录下项目的参项人员列表
 * 以便于日后统计这一天工时的已填未填情况
 * mysql table name: project_member_relation_daily_record
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "project_member_relation_daily_record")
public class ProjectMemberRelationDailyRecord {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 记录的日期
     */
    private String laborDate;

    /**
     * 当天是否已填写工时
     */
    private Integer filled;

    private Long projectId;

    private String memberId;
}
