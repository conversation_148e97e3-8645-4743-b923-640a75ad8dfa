package net.tartan.platform.integration.controller;

import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.integration.beans.query.DeviceServiceHistoryQuery;
import net.tartan.platform.integration.beans.vo.DeviceServiceHistoryVo;
import net.tartan.platform.integration.service.IDeviceServiceHistoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * 仪器服役记录 前端控制器
 */
@RestController
@RequestMapping("/deviceServiceHistory")
public class DeviceServiceHistoryController {

    @Autowired
    private IDeviceServiceHistoryService deviceServiceHistoryService;

    @PostMapping("selectHistory")
    public CommonResult selectHistory(@RequestBody DeviceServiceHistoryQuery query) {

        DeviceServiceHistoryVo deviceServiceHistoryVo = deviceServiceHistoryService.selectHistory(query);
        return CommonResult.success(deviceServiceHistoryVo);
    }
}
