package net.tartan.platform.integration.entity.deviceassemble;

import lombok.Data;
import net.tartan.platform.common.constant.PfConstant;
import net.tartan.platform.integration.beans.vo.RotaryTreeVO;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.validation.constraints.NotNull;

@Data
@Document(collection = PfConstant.ROTARY_DEVICE_ASSEMBLE_RECORD)
public class MwdRotaryAssembleRecord extends RotaryAssembleRecord {
    @Id
    private String recordId;
    /**
     * 台账id
     */
    @NotNull
    private Long mwdId;
}
