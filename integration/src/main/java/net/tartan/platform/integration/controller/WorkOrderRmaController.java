package net.tartan.platform.integration.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.common.beans.ResultCode;
import net.tartan.platform.integration.beans.dto.WorkOrderRmaDto;
import net.tartan.platform.integration.beans.query.WorkOrderRmaQuery;
import net.tartan.platform.integration.beans.vo.WorkOrderRmaDetailResponse;
import net.tartan.platform.integration.beans.vo.WorkOrderRmaVo;
import net.tartan.platform.integration.entity.WorkOrderRma;
import net.tartan.platform.integration.exception.BusinessException;
import net.tartan.platform.integration.service.IWorkOrderRmaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * rma工单信息表 前端控制器
 * </p>
 */
@RestController
@RequestMapping("/workOrderRma")
public class WorkOrderRmaController {

    @Autowired
    private IWorkOrderRmaService workOrderRmaService;


    @PostMapping("add")
    public CommonResult add(@RequestBody @Valid WorkOrderRmaDto orderRmaDto) {
        if (orderRmaDto.getRmaNumber() == null) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        Long rmaId = workOrderRmaService.add(orderRmaDto);
        WorkOrderRmaVo workOrderRmaVo = workOrderRmaService.info(rmaId);
        return CommonResult.success(workOrderRmaVo);
    }
    @GetMapping("info")
    public CommonResult info(@RequestParam("rmaId") long rmaId) {
        WorkOrderRmaVo workOrderRmaVo = workOrderRmaService.info(rmaId);
        return CommonResult.success(workOrderRmaVo);
    }

    @PostMapping("delete")
    public CommonResult delete(@RequestBody @Valid WorkOrderRmaVo orderRmaVo) {
        if (orderRmaVo.getRmaId() == null) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        workOrderRmaService.delete(orderRmaVo.getRmaId());
        return CommonResult.success();
    }

    @PostMapping("update")
    public CommonResult update(@RequestBody @Valid WorkOrderRmaVo orderRmaVo) {
        if (orderRmaVo.getRmaId() == null) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        workOrderRmaService.update(orderRmaVo);
        WorkOrderRmaVo workOrderRmaVo = workOrderRmaService.info(orderRmaVo.getRmaId());
        return CommonResult.success(workOrderRmaVo);
    }

    @PostMapping("finish")
    public CommonResult finish(@RequestBody @Valid WorkOrderRma orderRma) {
        if (orderRma.getRmaId() == null) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        workOrderRmaService.updateFinish(orderRma);
        return CommonResult.success();
    }

    @PostMapping("getDepartmentName")
    public CommonResult getDepartmentName(@RequestBody(required = false) WorkOrderRmaQuery query) {
        if (query == null) {
            query = new WorkOrderRmaQuery();
        }
        return CommonResult.success(workOrderRmaService.getDepartmentName(query));
    }


    @PostMapping("list/{current}/{size}")
    public CommonResult list(@PathVariable long current, @PathVariable long size, @RequestBody(required = false) WorkOrderRmaQuery query) {
        IPage<WorkOrderRmaDetailResponse> page = new Page<>(current, size);
        if (query == null) {
            query = new WorkOrderRmaQuery();
        }
        workOrderRmaService.list(page, query);
        return CommonResult.success(page);
    }
}
