package net.tartan.platform.integration.beans.vo.dailyreport;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;


@Data
@NoArgsConstructor
public class ReportCommonDataVo {

    /**
     * id
     */
    private Long id;
    /**
     * jobId
     */
    private Long jobId;
    /**
     * 品名
     */
    private String invName;

    /**
     * 序列号
     */
    private String serialNumber;
    /**
     * kit箱编号
     */
    private String kitNumber;

    /**
     * 趟次
     */
    private List<Integer> runList;
    private String run;

    /**
     * 最高温度
     */
    private BigDecimal maxBht;

    /**
     * 入井时间
     */
    private BigDecimal inWellHour;
    /**
     * 循环时间
     */
    private BigDecimal circulateHrs;


}
