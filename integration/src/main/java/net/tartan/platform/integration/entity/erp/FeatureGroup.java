package net.tartan.platform.integration.entity.erp;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 品号群组/CHT/品號群組/ENU/Item Group
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("FEATURE_GROUP")
public class FeatureGroup implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 关联部门
     */
    @TableField("Owner_Dept")
    private String ownerDept;

    /**
     * 关联员工
     */
    @TableField("Owner_Emp")
    private String ownerEmp;

    /**
     * 主键
     */
    @TableId("FEATURE_GROUP_ID")
    private String featureGroupId;

    /**
     * 群组编号
     */
    @TableField("FEATURE_GROUP_CODE")
    private String featureGroupCode;

    /**
     * 群组名称
     */
    @TableField("FEATURE_GROUP_NAME")
    private String featureGroupName;

    /**
     * 启用特征码
     */
    @TableField("ITEM_FEATURE_CONTROL")
    private Boolean itemFeatureControl;

    /**
     * 备注
     */
    @TableField("REMARK")
    private String remark;

    /**
     * 业务领域
     */
    @TableField("BUSINESS_RANGE")
    private String businessRange;

    /**
     * 品号编码格式
     */
    @TableField("ITEM_FORMAT")
    private String itemFormat;

    /**
     * 品号编码样式
     */
    @TableField("ITEM_FORM")
    private String itemForm;

    /**
     * 特征码编码格式
     */
    @TableField("FEATURE_FORMAT")
    private String featureFormat;

    /**
     * 特征码编码样式
     */
    @TableField("FEATURE_FORM")
    private String featureForm;

    /**
     * 特征码产生模式
     */
    @TableField("FEATURE_GENERATE_MODE")
    private String featureGenerateMode;

    /**
     * 品号群组
     */
    @TableField("PARENT_FEATURE_GROUP_ID")
    private String parentFeatureGroupId;

    /**
     * 来源
     */
    @TableField("SOURCE")
    private String source;

    /**
     * PLM传输批次号
     */
    @TableField("PLM_DATAKEY")
    private String plmDatakey;

    /**
     * 版本号，不要随意更改
     */
    @TableField("Version")
    private byte[]  version;

    /**
     * 单据状态属性
     */
    @TableField("ApproveStatus")
    private String approvestatus;

    /**
     * 修改日期
     */
    @TableField("ApproveDate")
    private Date approvedate;

    /**
     * 修改人
     */
    @TableField("ApproveBy")
    private String approveby;

    /**
     * 创建日期
     */
    @TableField("CreateDate")
    private Date createdate;

    /**
     * 最后修改日期
     */
    @TableField("LastModifiedDate")
    private Date lastmodifieddate;

    /**
     * 修改日期
     */
    @TableField("ModifiedDate")
    private Date modifieddate;

    /**
     * 创建者
     */
    @TableField("CreateBy")
    private String createby;

    /**
     * 最后修改者
     */
    @TableField("LastModifiedBy")
    private String lastmodifiedby;

    /**
     * 修改者
     */
    @TableField("ModifiedBy")
    private String modifiedby;

    /**
     * 附件
     */
    @TableField("Attachments")
    private String attachments;

    /**
     * 表单所在的流程实例的编号
     */
    @TableField("ProcessInstanceId")
    private String processinstanceid;

    @TableField("Owner_Org_RTK")
    private String ownerOrgRtk;

    @TableField("Owner_Org_ROid")
    private String ownerOrgRoid;


}
