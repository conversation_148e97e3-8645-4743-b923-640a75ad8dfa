package net.tartan.platform.integration.controller;

import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.common.enums.EnumSyncTask;
import net.tartan.platform.integration.task.TaskManager;
import net.tartan.platform.integration.utils.CommonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("task")
public class TaskController {

    @Autowired
    private TaskManager taskManager;

    @RequestMapping("data/sync")
    public CommonResult dataSync(@RequestParam("task") EnumSyncTask task) {
        final boolean success = taskManager.execute(task);
        Map<String, Boolean> result = new HashMap<>();
        result.put("success", success);
        return CommonResult.success(result);
    }

    @RequestMapping("data/syncGroup")
    public CommonResult dataSyncGroup(@RequestParam("taskGroup") String task) {
        final boolean success = taskManager.executeGroup(task);
        Map<String, Boolean> result = new HashMap<>();
        result.put("success", success);
        return CommonResult.success(result);
    }

    @RequestMapping("data/syncAll")
    public CommonResult allDataSync() {
        taskManager.executeAllTask();
        return CommonResult.success();
    }
}
