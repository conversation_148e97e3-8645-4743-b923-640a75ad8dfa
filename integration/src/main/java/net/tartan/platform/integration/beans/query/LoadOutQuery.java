package net.tartan.platform.integration.beans.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LoadOutQuery {
    private Long loadOutId;
    private String serialNumber;
    private Long deviceType;
    private String wellNumber;
    private String location;
    private String dateIn;
    private String dateOut;
    private String jobNumber;
}
