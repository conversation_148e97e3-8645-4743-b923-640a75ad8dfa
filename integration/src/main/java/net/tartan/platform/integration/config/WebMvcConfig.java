//package net.tartan.platform.integration.config;
//
//
//import net.tartan.platform.integration.Interceptor.RequestInterceptor;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
//import org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport;
//
//@Configuration
//public class WebMvcConfig extends WebMvcConfigurationSupport {
//
//    @Autowired
//    private RequestInterceptor requestInterceptor;
//
//    @Override
//    protected void addInterceptors(InterceptorRegistry registry) {
//        registry.addInterceptor(requestInterceptor).addPathPatterns("/**");
//    }
//
//}
