package net.tartan.platform.integration.entity.oa;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 *    OA钻具通知单 总表基础信息
 *    OA表：formmain_0412
 *
 * <AUTHOR>
 * @since 2021-06-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("formmain_0412")
public class OaToolInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.NONE)
    private Long id; // 主键 formmain id

    @TableField("field0023")
    private String no;  // NO

    @TableField("field0024")
    private String starterId;  // 发起人的member id

    private String startDate; // 单据的发起日期

    @TableField("starter_name")
    private String starterName; // 发起人姓名

    @TableField("field0001")
    private String jobNumber;  // 工单号

    @TableField("field0002")
    private String owner; //  客户名称/所有者

    @TableField("field0003")
    private String wellName; // 井名

    @TableField("field0004")
    private String date; // 日期

    @TableField("field0005")
    private String kitNumber; // kit号

    @TableField("field0006")
    private String contactId; // 现场联系人的member id

    @TableField("contact_user")
    private String contactUser; // 现场联系人姓名

    private String contactNumber;// 现场联系人手机号

    @TableField("field0026")
    private String checkResult; // 审批意见

    /*
        -5876320492892174459 入库 即 拆卸
        1567872266221668186 出库 即 组装
     */
    @TableField("field0027")
    private String workOrderType;


}
