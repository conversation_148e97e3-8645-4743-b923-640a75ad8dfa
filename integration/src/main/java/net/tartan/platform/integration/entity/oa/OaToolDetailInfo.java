package net.tartan.platform.integration.entity.oa;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 *   OA钻具通知单 详情信息
 *   OA表：formson_0413
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("formson_0413")
public class OaToolDetailInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    private Long rowNumber; // field0007 明细表行序号

    private String invName; // field0008 名称

    private String serialNumber; // field0009 序列号

    private BigDecimal circulateBht; // field0010 循环温度

    private BigDecimal maxBht; // field0011 最高温度

    private BigDecimal inWellHour; // field0012 入井时间

    private BigDecimal circulateHrs; // field0013 循环时间

    private String returnReason; // field0020 oa字典中叫返回原因 对应井下工单的返修原因

    private String notes; // field0021 备注

    // 以下是螺杆专属参数
    private String mudType; // field0014 泥浆类型 读取时会trim掉多余的空格

    private String hasError; // field0029 是否存在故障及故障描述

    private String amount; // field0030 数量

    /**
     * *非OA原生字段
     * 弯度:单弯 1 / 可调 0
     * angle如果为空 则curve也为空
     * angle如果为0 则curve为 单弯 1
     * angle如果大于0 则curve为 可调 0
     */
    private Integer curve;

    private BigDecimal angle; // field0015 弯度

    private BigDecimal endureTemperature; // field0016 耐温

    private String claspType; // field0017 扣型

    private String stbDescribe; // field0018 扶正器尺寸-类型 对应device表中的扶正器描述

    private BigDecimal odMax;// field0019 最大外径mm

}
