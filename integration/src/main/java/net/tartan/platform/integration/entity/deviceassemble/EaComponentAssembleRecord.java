package net.tartan.platform.integration.entity.deviceassemble;

import lombok.Data;
import net.tartan.platform.common.constant.PfConstant;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.validation.constraints.NotNull;

@Data
@Document(collection = PfConstant.EA_COMPONENT_ASSEMBLE_RECORD)
public class EaComponentAssembleRecord extends RotaryAssembleRecord{

    @Id
    private String recordId;
    /**
     * 台账id
     */
    @NotNull
    private Long eaId;
}
