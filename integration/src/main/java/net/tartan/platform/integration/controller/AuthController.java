package net.tartan.platform.integration.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.common.beans.ResultCode;
import net.tartan.platform.integration.beans.vo.AuthInfoVo;
import net.tartan.platform.integration.beans.vo.RouterVo;
import net.tartan.platform.integration.entity.Department;
import net.tartan.platform.integration.entity.MemberInfo;
import net.tartan.platform.integration.entity.Role;
import net.tartan.platform.integration.entity.UserInfo;
import net.tartan.platform.integration.exception.BusinessException;
import net.tartan.platform.integration.service.*;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.security.Principal;
import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping("auth")
public class AuthController {

    @Autowired
    private IUserInfoService userInfoService;
    @Autowired
    private IMemberInfoService memberService;
    @Autowired
    private IDepartmentService departmentService;
    @Autowired
    private RoleService roleService;
    @Autowired
    private RouterService routerService;
    @Autowired
    private PasswordEncoder passwordEncoder;


    @GetMapping(value = "/info")
    public CommonResult<AuthInfoVo> getUserInfo(Principal principal) {
        String username = principal.getName();
        //账户信息
        LambdaQueryWrapper<UserInfo> accountQueryWrapper = new LambdaQueryWrapper<>();
        accountQueryWrapper.eq(UserInfo::getUsername, username);
        UserInfo accountInfo = userInfoService.getOne(accountQueryWrapper);
        if (Objects.isNull(accountInfo)) {
            throw new BusinessException(ResultCode.ACCOUNT_NOT_EXIST);
        }
        accountInfo.setPassword(null);
        final Long accountId = accountInfo.getUserId();
        //员工信息
        LambdaQueryWrapper<MemberInfo> memberQueryWrapper = new LambdaQueryWrapper<>();
        memberQueryWrapper.eq(MemberInfo::getMemberId, accountInfo.getMemberId());
        final MemberInfo memberInfo = memberService.getOne(memberQueryWrapper);
        if (Objects.isNull(memberInfo)) {
            throw new BusinessException(ResultCode.MEMBER_NOT_EXIST);
        }
        //部门信息
        LambdaQueryWrapper<Department> departmentQueryWrapper = new LambdaQueryWrapper<>();
        departmentQueryWrapper.eq(Department::getDepartmentId, memberInfo.getOrgDepartmentId());
        final Department department = departmentService.getOne(departmentQueryWrapper);
        //角色信息
        List<Role> roleList = roleService.findRoleListByUser(accountId);
        //菜单信息
        List<RouterVo> menuList = routerService.getRouterListByUser(accountId);
        //菜单按钮信息
        List<String> btnPermissionList = routerService.getBtnPermissionListByUser(accountId);

        AuthInfoVo response = AuthInfoVo.builder().accountInfo(accountInfo)
                .memberInfo(memberInfo)
                .department(department)
                .roleList(roleList)
                .menuList(menuList)
                .btnPermissionList(btnPermissionList)
                .build();

        return CommonResult.success(response);
    }

    @PostMapping(value = "/update/pass")
    public CommonResult getUserInfo(@RequestParam("userId") Long userId,
                                    @RequestParam("prePassword") String prePassword,
                                    @RequestParam("password") String password) {
        if (userId == null || prePassword == null || password == null) {
            return CommonResult.failed(ResultCode.PARAMS_ERROR);
        }
        LambdaQueryWrapper<UserInfo> accountQueryWrapper = new LambdaQueryWrapper<>();
        accountQueryWrapper.eq(UserInfo::getUserId, userId);
        UserInfo userInfo = userInfoService.getOne(accountQueryWrapper);
        if (ObjectUtils.isEmpty(userInfo)) {
            return CommonResult.failed(ResultCode.ACCOUNT_NOT_EXIST);
        }

        if (!passwordEncoder.matches(prePassword, userInfo.getPassword())) {
            return CommonResult.failed(ResultCode.PASSWORD_ERROR);
        }

        //更新新密码
        userInfo.setPassword(passwordEncoder.encode(password));
        userInfoService.updateById(userInfo);
        return CommonResult.success();
    }
}
