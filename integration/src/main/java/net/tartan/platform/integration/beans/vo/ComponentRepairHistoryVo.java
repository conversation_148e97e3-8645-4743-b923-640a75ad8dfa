//package net.tartan.platform.integration.beans.vo;
//
//
//import com.fasterxml.jackson.annotation.JsonFormat;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//import net.tartan.platform.common.enums.EnumRepairType;
//import net.tartan.platform.common.enums.EnumRiskType;
//
//import java.math.BigDecimal;
//import java.util.Date;
//
//@Data
//@NoArgsConstructor
//public class ComponentRepairHistoryVo {
//    private Long historyId;
//
//    /**
//     * 台账id
//     */
//    private Long mwdId;
//    private Long pitToolsId;
////    /**
////     * 品号
////     */
////    private String invCode;
//    /**
//     * 配件名称
//     */
//    private String invName;
//    /**
//     * 序列号
//     */
//    private String serialNumber;
////    /**
////     * 母件存货编码
////     */
////    private String parentInvCode;
//    /**
//     * 母件存货品名
//     */
//    private String parentInvName;
//    /**
//     * 母件序列号，erp的批次号
//     */
//    private String parentSerialNumber;
//    /**
//     * 维修单号
//     */
//    private String mwdNumber;
//    private String pitToolsNumber;
//
//    /**
//     * 维修类型
//     */
//    private EnumRepairType repairType;
//
//    /**
//     * 维修时间
//     */
//    private String repairDate;
//
//    /**
//     * 维修方案
//     */
//    private String repairAction;
//
//    /**
//     * 失效原因
//     */
//    private String failureReason;
//
//    /**
//     * 备注
//     */
//    private String notes;
//
//    /**
//     * 创建时间
//     */
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date createTime;
//    /**
//     * 更新时间
//     */
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date updateTime;
//
//    /**
//     * 最高温度
//     */
//    private BigDecimal maxBht;
//
//    /**
//     * 修正最高温度
//     */
//    private BigDecimal reviseMaxBht;
//
//    /**
//     * 总入井时间
//     */
//    private BigDecimal totalInWellHrs;
//
//    /**
//     * 修正使用时长
//     */
//    private BigDecimal reviseTotalHours;
//
//    /**
//     * 服役固件版本号
//     */
//    private String serviceVersionNumber;
//    /**
//     * 升级后固件版本号
//     */
//    private String updatedVersionNumber;
//
//    /**
//     * 服役风险类型
//     */
//    private EnumRiskType serviceRiskType;
//    /**
//     * 更新后的服役风险类型
//     */
//    private EnumRiskType updatedRiskType;
//}
