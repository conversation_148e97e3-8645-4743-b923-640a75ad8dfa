package net.tartan.platform.integration.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.common.beans.ResultCode;
import net.tartan.platform.integration.beans.query.HandoverQuery;
import net.tartan.platform.integration.beans.vo.HandoverBatch;
import net.tartan.platform.integration.beans.vo.HandoverVo;
import net.tartan.platform.integration.exception.BusinessException;
import net.tartan.platform.integration.service.HandoverService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * handover 交接单
 */
@RestController
@RequestMapping("/handover")
public class MwdHandoverController {

    @Autowired
    private HandoverService handoverService;

    @ResponseBody
    @RequestMapping("/add")
    public CommonResult addHandover(@RequestParam("idList") List<Long> idList, @RequestParam(name = "type", defaultValue = "MWD") String type) {

        if (idList == null || idList.isEmpty() || StringUtils.isEmpty(type)) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        handoverService.addHandover(idList, type);
        return CommonResult.success();
    }

    @PostMapping("list/{current}/{size}")
    public CommonResult getList(@PathVariable long current,
                                    @PathVariable long size,
                                    @RequestBody(required = false) HandoverQuery query) {
        IPage<HandoverVo> page = new Page<>(current, size);
        if(query == null){
            query = new HandoverQuery();
        }
        handoverService.select(page, query);
        return CommonResult.success(page);
    }

    @PostMapping("/updateStatus")
    public CommonResult updateStatus(@RequestBody HandoverQuery query) {

        if (query == null ) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        handoverService.updateHandoverStatus(query);
        return CommonResult.success();
    }

    /**
     * 根据交接单的主键id进行删除
     * @param id
     * @return
     */
    @PostMapping("/delete")
    public CommonResult delete(@RequestParam("id") Long id) {

        if (id == null || id <= 0) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        handoverService.delete(id);
        return CommonResult.success();
    }

    @PostMapping("/export")
    public CommonResult export(@RequestBody(required = false) HandoverQuery query) {

        if (query == null ) {
            query = new HandoverQuery();
        }
        handoverService.export(query);
        return CommonResult.success();
    }

}
