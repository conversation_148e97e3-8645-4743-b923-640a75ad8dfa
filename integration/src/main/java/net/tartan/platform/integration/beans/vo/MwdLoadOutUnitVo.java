package net.tartan.platform.integration.beans.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
public class MwdLoadOutUnitVo {
    private Long loadOutDetailId;
    private Integer templateId; // 编号
    private String unitNumber; // 零件号
    private String unitName; // 零件名

    private Long deviceId;
    private String serialNumber; // 序列号

    private Integer requireAmount; // 需求数量
    private Integer actualAmount; // 实发数量
    private Integer loadInAmount; // 入库数量

    private String deviceTypeList;//用来记录仪器类型
    private String otherName;//当仪器类型有其他时，用这个字段来区分是其他中的什么仪器。 如 司显, Depth Tracking, Gamma Key
    private Long status;
    private String statusStr;
    private String note;
    private String typeName;
}
