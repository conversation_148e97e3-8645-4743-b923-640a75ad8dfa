package net.tartan.platform.integration.entity.oa;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.constant.PfConstant;
import net.tartan.platform.common.enums.EnumCirculateType;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * 从OA过来的钻具通知单 被保存到mongo中用的实例
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
@Document(collection = PfConstant.OA_TOOL_SAVE_INFO)
public class OaToolSaveInfo {

    @Id
    private Long id; // mongo id

    private String startDate; // 单据的发起日期

    private Integer finishedflag; // 审批状态，1 -> 已通过，0 -> 审批中， 3 -> 已拒绝

    private String no;  // NO

    private String starterId;  // 发起人的member id

    private String starterName; // 发起人姓名

    private String starterDepName; // 发起人部门

    private String jobNumber;  // 工单号

    private String owner; //  客户名称/所有者

    private String wellName; // 井名

    private String date; // 日期

    private String kitNumber; // kit号

    private String contactId; // 现场联系人的member id

    private String contactUser; // 现场联系人姓名

    private String contactNumber;// 现场联系人手机号

    private String checkResult; // 审批意见

    /*
        -5876320492892174459 入库 即 拆卸
        1567872266221668186 出库 即 组装
     */
    private String workOrderType;

    private String note;

    private List<OaToolDetailInfo> oaToolDetailInfoList;

    //
    private Long orderId;
    private EnumCirculateType orderType;

}
