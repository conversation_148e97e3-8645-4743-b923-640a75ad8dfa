package net.tartan.platform.integration.beans.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.enums.EnumMenuType;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class MenuInfoVo implements Serializable {
    private Long id;

    /**
     * 父级ID
     */
    private Long parentId;

    /**
     * 是否不启用缓存
     */
    private Boolean noCache;

    /**
     * 默认跳转
     */
    private String redirect;

    /**
     * 菜单名称
     */
    private String title;

    /**
     * 类型
     */
    private EnumMenuType type;

    /**
     * 授权标识
     */
    private String authFlag;

    /**
     * 菜单排序
     */
    private Integer sort;

    /**
     * 菜单名称
     */
    private String name;

    /**
     * 前端图标
     */
    private String icon;

    /**
     * 是否隐藏
     */
    private Boolean hidden;

    /**
     * 组件名称
     */
    private String component;

    /**
     * 路由地址
     */
    private String path;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 子菜单
     */
    private List<MenuInfoVo> children;

    private static final long serialVersionUID = 1L;


}