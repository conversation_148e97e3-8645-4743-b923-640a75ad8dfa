package net.tartan.platform.integration.beans.vo.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class WarehouseExportParam {
    @ExcelProperty("仪器名称")
    private String invName;

    /**
     * 仪器类型
     */
    @ExcelProperty("仪器类型")
    private String deviceType;

    /**
     * 序列号
     */
    @ExcelProperty("序列号")
    private String serialNumber;

    /**
     * Status状态
     */
    @ExcelProperty("状态")
    private String status;

    /**
     * 接收日期
     */
    @ExcelProperty("接收日期")
    private String receivedDate;

    /**
     * 仪器配置数量 在Kit Boxes仪器箱中使用 单位为 串
     */
    @ExcelProperty("规格/配置")
    private String quantity;

    /**
     * 地点
     */
    @ExcelProperty("地点")
    private String location;

    /**
     * 备注
     */
    @ExcelProperty("备注")
    private String notes;

    /**
     * 所属
     */
    @ExcelProperty("所属")
    private String owner;
}
