package net.tartan.platform.integration.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import net.tartan.platform.integration.context.UserContext;
import net.tartan.platform.integration.entity.Department;
import net.tartan.platform.integration.entity.MemberInfo;
import net.tartan.platform.integration.mapper.DepartmentMapper;
import net.tartan.platform.integration.mapper.MemberInfoMapper;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import lombok.extern.slf4j.Slf4j;
import net.tartan.platform.common.beans.ResultCode;
import net.tartan.platform.integration.entity.DrillingToolNotification;
import net.tartan.platform.integration.exception.BusinessException;
import net.tartan.platform.integration.mongo.DrillingToolNotificationRepository;
import net.tartan.platform.integration.service.DrillingToolNoGeneratorService;
import net.tartan.platform.integration.service.DrillingToolNotificationService;
import net.tartan.platform.integration.service.OAClientService;

/**
 * 钻具通知单服务实现类
 * 
 * <AUTHOR>
 * @since 2025-07-15
 */
@Slf4j
@Service
public class DrillingToolNotificationServiceImpl implements DrillingToolNotificationService {

    @Autowired
    private DrillingToolNotificationRepository drillingToolNotificationRepository;

    @Autowired
    private DrillingToolNoGeneratorService drillingToolNoGeneratorService;

    @Autowired
    private OAClientService oaClientService;

    @Autowired
    private MemberInfoMapper memberInfoMapper;

    @Autowired
    private DepartmentMapper departmentMapper;

    @Value("${oa.drilling-tool.template-code:ZJTZSHD_001}")
    private String templateCode;

    @Override
    public DrillingToolNotification createNotification(DrillingToolNotification notification) {
        log.info("创建钻具通知单");

        // 验证必填字段（移除单据编号验证）
        validateNotification(notification);

        // 生成单据编号NO（按照SCEL+日期+序号的规则）
        String generatedNo = drillingToolNoGeneratorService.generateNo();
        notification.setNo(generatedNo);
        log.info("生成的钻具通知单NO: {}", generatedNo);

        // 设置初始值
        if (notification.getCreatedTime() == null) {
            notification.setCreatedTime(new Date());
        }
        notification.setUpdatedTime(new Date());
        notification.setOaSyncStatus("pending");
        notification.setStatus("active");

        // 保存到MongoDB
        return drillingToolNotificationRepository.save(notification);
    }

    @Override
    public DrillingToolNotification initNotification() {
        log.info("初始化钻具通知单");
        DrillingToolNotification notification = new DrillingToolNotification();
        // 生成单据编号NO（按照SCEL+日期+序号的规则）
        String generatedNo = drillingToolNoGeneratorService.generateNo();
        notification.setNo(generatedNo);
        log.info("生成的钻具通知单NO: {}", generatedNo);

        // 获取发起人信息
        Long userId = UserContext.getUserId();
        MemberInfo memberInfo = memberInfoMapper.getMemberByUserId(userId);
        if(memberInfo != null){
            notification.setCreatedBy(memberInfo.getName());
            notification.setInitiatorName(memberInfo.getName());
            notification.setInitiatorCode(memberInfo.getSortId().toString());
            Department department = departmentMapper.selectByDepartmentId(memberInfo.getOrgDepartmentId());
            if (ObjectUtils.isNotEmpty(department)) {
                notification.setInitiatorDept(department.getName());
            }
        }

        // 设置初始值
        if (notification.getCreatedTime() == null) {
            notification.setCreatedTime(new Date());
        }
        notification.setUpdatedTime(new Date());
        notification.setOaSyncStatus("pending");
        notification.setStatus("active");
        return notification;
    }

    @Override
    public boolean sentNotification(DrillingToolNotification notification) {

        // 验证必填字段（移除单据编号验证）
        validateNotification(notification);

        // 保存到mongo
        createNotification(notification);

        // 格式化向OA发送的数据
        Map<String, Object> oaData = generateOASubmitData(notification);
        try {
            // 调用发送到oa
            oaClientService.pushDataWithRetry(oaData, 3);
            return true;
        } catch (Exception e) {
            log.error("钻具通知单向OA发送失败：{}", oaData);
            return false;
        }
    }

    @Override
    public DrillingToolNotification getNotificationById(String id) {
        return drillingToolNotificationRepository.findById(id)
                .orElseThrow(() -> new BusinessException(ResultCode.VALIDATE_FAILED, "钻具通知单不存在"));
    }

    @Override
    public DrillingToolNotification getNotificationByNo(String no) {
        if (!StringUtils.hasText(no)) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, "单据编号不能为空");
        }
        DrillingToolNotification notification = drillingToolNotificationRepository.findByNo(no);
        if (notification == null) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, "钻具通知单不存在");
        }
        return notification;
    }

    @Override
    public DrillingToolNotification updateNotification(DrillingToolNotification notification) {
        log.info("更新钻具通知单: {}", notification.getId());
        
        // 验证ID
        if (!StringUtils.hasText(notification.getId())) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, "钻具通知单ID不能为空");
        }
        
        // 检查是否存在
        DrillingToolNotification existingNotification = getNotificationById(notification.getId());
        
        // 更新时间
        notification.setUpdatedTime(new Date());
        
        // 保留原始创建时间
        notification.setCreatedTime(existingNotification.getCreatedTime());
        
        // 保留原始状态
        notification.setStatus(existingNotification.getStatus());
        
        // 如果数据有变化，重置同步状态
        if (!notification.equals(existingNotification)) {
            notification.setOaSyncStatus("pending");
            notification.setOaSyncTime(null);
            notification.setOaSyncError(null);
        }
        
        return drillingToolNotificationRepository.save(notification);
    }

    @Override
    public void deleteNotification(String id) {
        log.info("删除钻具通知单: {}", id);
        
        // 软删除
        DrillingToolNotification notification = getNotificationById(id);
        notification.setStatus("deleted");
        notification.setUpdatedTime(new Date());
        
        drillingToolNotificationRepository.save(notification);
    }

    @Override
    public List<DrillingToolNotification> getAllNotifications() {
        return drillingToolNotificationRepository.findAll().stream()
                .filter(n -> "active".equals(n.getStatus()))
                .collect(Collectors.toList());
    }

    @Override
    public List<DrillingToolNotification> getNotificationsByConditions(Map<String, Object> conditions) {
        // 根据条件查询
        if (conditions.containsKey("customerName") && conditions.containsKey("wellName")) {
            return drillingToolNotificationRepository.findByCustomerAndWell(
                    (String) conditions.get("customerName"),
                    (String) conditions.get("wellName")
            );
        } else if (conditions.containsKey("jobNumber")) {
            return drillingToolNotificationRepository.findByJobNumber((String) conditions.get("jobNumber"));
        } else if (conditions.containsKey("kitNo")) {
            return drillingToolNotificationRepository.findByKitNo((String) conditions.get("kitNo"));
        } else if (conditions.containsKey("toolName")) {
            return drillingToolNotificationRepository.findByToolName((String) conditions.get("toolName"));
        } else if (conditions.containsKey("serialNumber")) {
            return drillingToolNotificationRepository.findByToolSerialNumber((String) conditions.get("serialNumber"));
        } else {
            // 默认返回最近的记录
            return drillingToolNotificationRepository.findRecentRecords();
        }
    }

    @Override
    public List<DrillingToolNotification> getNotificationsByDateRange(Date startDate, Date endDate) {
        return drillingToolNotificationRepository.findByDateRange(startDate, endDate);
    }

    /**
     * 获取待同步记录的方法保留，但不再用于自动轮询
     * 仅用于手动查询待同步状态的记录
     */
    @Override
    public List<DrillingToolNotification> getPendingSyncNotifications() {
        return drillingToolNotificationRepository.findPendingSyncRecords();
    }

    @Override
    public boolean syncNotificationToOA(String id) {
        log.info("同步钻具通知单到OA系统: {}", id);
        
        // 获取钻具通知单
        DrillingToolNotification notification = getNotificationById(id);
        
        // 检查状态
        if ("success".equals(notification.getOaSyncStatus())) {
            log.info("钻具通知单已同步成功，无需重新同步: {}", id);
            return true;
        }
        
        // 更新状态为同步中
        updateSyncStatus(id, "syncing", null);
        
        try {
            // 生成OA送签数据
            Map<String, Object> oaData = generateOASubmitData(notification);
            
            // 调用OA接口
            oaClientService.pushDataWithRetry(oaData, 3);
            
            // 更新同步状态为成功
            updateSyncStatus(id, "success", null);
            
            log.info("钻具通知单同步成功: {}", id);
            return true;
            
        } catch (Exception e) {
            log.error("钻具通知单同步失败: {}, 错误: {}", id, e.getMessage(), e);
            
            // 更新同步状态为失败
            updateSyncStatus(id, "failed", e.getMessage());
            
            return false;
        }
    }

    /**
     * 批量同步功能已移除，改为手动触发模式
     * 此方法保留接口兼容性，但不再执行批量同步
     */
    @Override
    public Map<String, Object> batchSyncNotificationsToOA() {
        log.info("批量同步功能已移除，改为手动触发模式");

        // 返回空结果
        Map<String, Object> result = new HashMap<>();
        result.put("totalCount", 0);
        result.put("successCount", 0);
        result.put("failedCount", 0);
        result.put("executeTime", new Date());
        result.put("message", "批量同步功能已移除，请使用手动触发模式");

        return result;
    }

    @Override
    public DrillingToolNotification updateSyncStatus(String id, String syncStatus, String syncError) {
        DrillingToolNotification notification = getNotificationById(id);
        notification.setOaSyncStatus(syncStatus);
        notification.setOaSyncError(syncError);
        
        if ("success".equals(syncStatus) || "failed".equals(syncStatus)) {
            notification.setOaSyncTime(new Date());
        }
        
        notification.setUpdatedTime(new Date());
        
        return drillingToolNotificationRepository.save(notification);
    }

    @Override
    public Map<String, Object> generateOASubmitData(DrillingToolNotification notification) {
        log.info("生成钻具通知单OA送签数据: {}", notification.getNo());
        
        // 构建OA送签数据
        Map<String, Object> oaData = new HashMap<>();
        
        // 设置模板代码
        oaData.put("templateCode", templateCode);
        
        // 设置发起人编码（由前端传递）
        String senderCode = notification.getInitiatorCode();
        if (!StringUtils.hasText(senderCode)) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, "发起人编码不能为空");
        }
        oaData.put("sendMemberCode", senderCode);
        
        // 设置传输类型和参数
        oaData.put("transfertype", "json");
        oaData.put("param", "0");
        
        // 构建业务数据
        Map<String, Object> businessData = new HashMap<>();

        // 主表字段（NO字段由Platform生成并传递给OA）
        businessData.put("NO", notification.getNo());
        businessData.put("INITIATOR", notification.getInitiatorCode());
        businessData.put("WORK_ORDER_NO", notification.getJobNumber());
        businessData.put("INITIATOR_DEPT", notification.getInitiatorDept());
        businessData.put("TRANSACTION_TYPE", notification.getTransactionType());
        businessData.put("TRANSACTION_TYPE_REMARK", notification.getTransactionTypeRemark());
        businessData.put("CUSTOMER_NAME", notification.getCustomerName());
        businessData.put("WELL_NAME", notification.getWellName());

        // 格式化日期为OA系统期望的格式：yyyy-MM-dd HH:mm:ss
        if (notification.getNotificationDate() != null) {
            java.text.SimpleDateFormat dateFormat = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            businessData.put("DATE", dateFormat.format(notification.getNotificationDate()));
        } else {
            businessData.put("DATE", null);
        }

        businessData.put("KIT_NO", notification.getKitNo());
        businessData.put("SITE_CONTACT", notification.getSiteContactCode());
        businessData.put("PROCESS_OPINION", notification.getProcessOpinion());
        
        // 从表数据
        List<Map<String, Object>> subData = new ArrayList<>();
        if (notification.getDrillingTools() != null && !notification.getDrillingTools().isEmpty()) {
            for (DrillingToolNotification.DrillingToolDetail detail : notification.getDrillingTools()) {
                Map<String, Object> subItem = new HashMap<>();
                
                // 基本信息
                subItem.put("SEQUENCE_NUMBER", detail.getSequenceNumber());
                subItem.put("NAME", detail.getName());
                subItem.put("SERIAL_NUMBER", detail.getSerialNumber());
                
                // 技术规格
                if (detail.getSpecifications() != null) {
                    subItem.put("CIRCULATION_TEMPERATURE", detail.getSpecifications().getCirculationTemperature());
                    subItem.put("MAX_TEMPERATURE", detail.getSpecifications().getMaxTemperature());
                    subItem.put("DOWNHOLE_TIME", detail.getSpecifications().getDownholeTime());
                    subItem.put("CIRCULATION_TIME", detail.getSpecifications().getCirculationTime());
                    subItem.put("TEMPERATURE_RESISTANCE", detail.getSpecifications().getTemperatureResistance());
                    subItem.put("BEND_DEGREE", detail.getSpecifications().getBendDegree());
                    subItem.put("MAX_OUTER_DIAMETER", detail.getSpecifications().getMaxOuterDiameter());
                }
                
                // 技术参数
                if (detail.getTechnicalParams() != null) {
                    subItem.put("MUD_TYPE", detail.getTechnicalParams().getMudType());
                    subItem.put("THREAD_TYPE", detail.getTechnicalParams().getThreadType());
                    subItem.put("CENTRALIZER_SIZE_TYPE", detail.getTechnicalParams().getCentralizerSizeType());
                }
                
                // 状态信息
                if (detail.getStatusInfo() != null) {
                    subItem.put("QUANTITY", detail.getStatusInfo().getQuantity());
                    subItem.put("RETURN_REASON", detail.getStatusInfo().getReturnReason());
                    subItem.put("FAULT_DESCRIPTION", detail.getStatusInfo().getFaultDescription());
                    subItem.put("REMARK", detail.getStatusInfo().getRemark());
                }
                // 维修信息
                if (detail.getRepairInfo() != null) {
                    subItem.put("VIBRATION_OOS", detail.getRepairInfo().getVibrationOos());
                    subItem.put("FAILURE_STATUS", detail.getRepairInfo().getFailureStatus());
                    subItem.put("FAILURE_DESCRIPTION", detail.getRepairInfo().getFailureDescription());
                    subItem.put("RUN", detail.getRepairInfo().getRun());
                }
                
                subData.add(subItem);
            }
        }
        
        // 添加从表数据
        businessData.put("sub", subData);
        
        // 添加业务数据
        oaData.put("data", businessData);
        
        return oaData;
    }

    @Override
    public Map<String, Long> countNotifications() {
        Map<String, Long> counts = new HashMap<>();
        
        // 总数
        counts.put("total", drillingToolNotificationRepository.count());
        
        // 各同步状态数量
        counts.put("pending", drillingToolNotificationRepository.countByOaSyncStatus("pending"));
        counts.put("syncing", drillingToolNotificationRepository.countByOaSyncStatus("syncing"));
        counts.put("success", drillingToolNotificationRepository.countByOaSyncStatus("success"));
        counts.put("failed", drillingToolNotificationRepository.countByOaSyncStatus("failed"));
        
        return counts;
    }

    /**
     * 验证钻具通知单必填字段
     * 注意：单据编号(NO)由OA系统自动生成，不需要在Platform系统中预先设置
     */
    private void validateNotification(DrillingToolNotification notification) {
        if (notification == null) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, "钻具通知单不能为空");
        }

        if (!StringUtils.hasText(notification.getJobNumber())) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, "工单号不能为空");
        }

        if (!StringUtils.hasText(notification.getCustomerName())) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, "客户名称不能为空");
        }

        if (!StringUtils.hasText(notification.getWellName())) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, "井名不能为空");
        }

        if (notification.getNotificationDate() == null) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, "日期不能为空");
        }

        if (!StringUtils.hasText(notification.getInitiatorCode())) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, "发起人编码不能为空");
        }
    }
}
