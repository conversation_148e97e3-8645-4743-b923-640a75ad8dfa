package net.tartan.platform.integration.beans.vo.dailyreport;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PullReportVo {
    @NotEmpty
    private String jobNumber;
    private Long jobId;

    private String wellNumber;
    private String wellId;

    /**
     * 作业类型
     */
    private Long jobType;
    private String jobTypeStr;
    private int jobStatus;
    @NotNull
    private List<Object> reportList;
}
