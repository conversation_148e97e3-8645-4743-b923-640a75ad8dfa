package net.tartan.platform.integration.entity.deviceassemble;


import lombok.Data;
import net.tartan.platform.common.constant.PfConstant;
import net.tartan.platform.common.enums.EnumRubberType;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@Document(collection = PfConstant.PIT_TOOLS_DEVICE_ASSEMBLE_RECORD)
public class PitToolsDeviceAssembleRecord extends DeviceAssembleRecord {

    @Id
    private String recordId;
    /**
     * 台账id
     */
    @NotNull
    private Long pitToolsId;

    //螺杆参数信息
    /**
     * 弯度:单弯 1 / 可调 0/2直/3旋导
     */
    private Integer curve;

    /**
     * 度数
     */
    private BigDecimal angle;

    /**
     * 扣型
     */
    private String claspType;

    /**
     * 最大外径
     */
    private BigDecimal odMax;

    /**
     * 耐温
     */
    private BigDecimal endureTemperature;

    /**
     * 泥浆类型: 水基 1  / 油基 0
     */
    private String mudType;

    /**
     * 是否带扶正器：带 1 / 不带 0
     */
    private Integer takeStb;

    /**
     * 扶正器尺寸
     */
    private BigDecimal stbSize;

    /**
     * 压差
     */
    private BigDecimal pressureSub;

    /**
     * 扭矩
     */
    private BigDecimal torque;

    //非核心部件
    /**
     * 上TC静圈
     */
    private String upTcStaticCircle;
    /**
     * 上TC动圈
     */
    private String upTcDynamicCircle;
    /**
     * 下TC静圈
     */
    private String downTcStaticCircle;
    /**
     * 下TC动圈
     */
    private String downTcDynamicCircle;
    /**
     * 水帽
     */
    private String waterCap;

    /**
     * 串轴承
     */
    private String bearing;

    /**
     * 扶正器尺寸-类型
     */
    private String stbDescribe;

    /**
     * 万向轴总成
     */
    private String cardanShaft;

    //定子
    /**
     * 橡胶类型
     */
    private EnumRubberType rubberType;
}
