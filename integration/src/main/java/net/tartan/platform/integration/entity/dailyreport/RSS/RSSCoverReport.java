package net.tartan.platform.integration.entity.dailyreport.RSS;


import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.constant.PfConstant;
import net.tartan.platform.integration.entity.dailyreport.MwdBoreholeInfo;
import net.tartan.platform.integration.entity.dailyreport.MwdCoverPersonnel;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = PfConstant.RSS_COVER_REPORT)
public class RSSCoverReport {

    @Transient
    private Long id;

    /**
     * 最后修改时间戳
     */
    private Long lastModified;

    /**
     * 报告类型
     */
    @Transient
    private String reportType;


    @Id
    @JsonIgnore
    private String reportId;

    /**
     * 作业ID
     */
    @NotNull
    private Long jobId;

    /**
     * 作业编号
     */
    private String jobNumber;

    // 概述
    /**
     * 客户
     */
    private String client;

    /**
     * 井号
     */
    private String wellNumber;

    /**
     * 井位置
     */
    private String location;

    /**
     * 区块
     */
    private String field;

    /**
     * 钻井承包商
     */
    private String drillContractor;

    /**
     * 我方提供服务
     */
    private String serviceType;

    //人员信息Personnel
    /**
     * 人员
     */
    private List<MwdCoverPersonnel> personnelList;

    /**
     * 开钻日期yyyy-MM-dd
     */
    private String dateIn;

    /**
     * 完钻日期yyyy-MM-dd
     */
    private String dateOut;

    /**
     * 工具下入趟数
     */
    private Integer totalMwdRuns;

    /**
     * 本井BPA使用时间
     */
    private Double totalBpaTimeHrs;

    /**
     * 本井最高温度
     */
    private Double maxTemp;

    /**
     * 仪器失效
     */
    private Integer mwdFailures;

    /**
     * 井型
     */
    private String wellType;

    /**
     * 井眼数据
     */
    private MwdBoreholeInfo holeInfo;

    /**
     * 仪器使用时间(Hrs) - 井下
     */
    private Double downHole;

    /**
     * 仪器使用时间(Hrs) - 循环
     */
    private Double circulating;

    /**
     * 仪器使用时间(Hrs) - 纯钻
     */
    private Double drilling;

    /**
     * JOB COMMENTS
     * 作业备注
     */
    private String jobComments;
}
