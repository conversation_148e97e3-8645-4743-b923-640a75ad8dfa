package net.tartan.platform.integration.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.common.beans.ResultCode;
import net.tartan.platform.integration.beans.dto.ComponentHierarchyDTO;
import net.tartan.platform.integration.beans.dto.RotaryBatchUpdateDTO;
import net.tartan.platform.integration.beans.dto.RotaryTreeDTO;
import net.tartan.platform.integration.beans.dto.WorkOrderMwdDto;
import net.tartan.platform.integration.beans.query.RotaryOrderQuery;
import net.tartan.platform.integration.beans.query.ToolQuery;
import net.tartan.platform.integration.beans.vo.ComponentServiceVo;
import net.tartan.platform.integration.beans.vo.RotaryComponentSelectVo;
import net.tartan.platform.integration.beans.vo.RotaryTreeVO;
import net.tartan.platform.integration.beans.vo.WorkOrderMwdVo;
import net.tartan.platform.integration.entity.RotaryTreeTemplate;
import net.tartan.platform.integration.exception.BusinessException;
import net.tartan.platform.integration.service.RotarySystemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/rotary")
public class RotarySystemController {

    @Autowired
    private RotarySystemService rotarySystemService;

    @PostMapping("/tree")
    public CommonResult addFromRepair(@RequestBody RotaryOrderQuery query) {
        RotaryTreeVO tree = rotarySystemService.getFullTree(query);
        return CommonResult.success(tree);
    }

    @GetMapping("/treeByAnyNode")
    public CommonResult addByAnyNode(@RequestParam(value = "componentId") long componentId) {
        ComponentHierarchyDTO hierarchy = rotarySystemService.getFullComponentTree(componentId);
        return CommonResult.success(hierarchy);
    }


    @PostMapping("/add")
    public CommonResult add(@RequestBody @Valid RotaryTreeDTO treeDTO) {
        if (treeDTO == null) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        rotarySystemService.insertComponent(treeDTO);
        return CommonResult.success();
    }
    @PostMapping("/updateComponent")
    public CommonResult updateComponent(@RequestBody @Valid RotaryBatchUpdateDTO componentUpdate) {
        if (componentUpdate == null) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        rotarySystemService.updateComponent(componentUpdate);
        return CommonResult.success();
    }
    @PostMapping("/assemble")
    public CommonResult assemble(@RequestBody @Valid RotaryBatchUpdateDTO componentUpdate) {
        if (componentUpdate == null) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        rotarySystemService.assemble(componentUpdate);
        return CommonResult.success();
    }

//    @PutMapping("/update")
//    public CommonResult update(@RequestBody RotaryTreeDTO treeDTO) {
//        if (treeDTO == null) {
//            throw new BusinessException(ResultCode.PARAMS_ERROR);
//        }
//        rotarySystemService.updateComponent(treeDTO);
//        return CommonResult.success();
//    }

    @PutMapping("/batchUpdate")
    public CommonResult batchUpdate(@RequestBody RotaryTreeDTO treeDTO) {
        if (treeDTO == null) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        rotarySystemService.updateComponent(treeDTO);
        return CommonResult.success();
    }

    @PostMapping("/getTemplate")
    public CommonResult getTemplate(@RequestBody RotaryOrderQuery query) {
        if (query == null || query.getTemplateType() == null) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        RotaryTreeTemplate template = rotarySystemService.getTemplate(query);
        return CommonResult.success(template);
    }

    @PostMapping("/saveTemplate")
    public CommonResult saveTemplate(@RequestBody RotaryTreeTemplate template) {
        if (template == null ) {
            return CommonResult.success();
        }
        rotarySystemService.saveTemplate(template);
        return CommonResult.success();
    }

    @PostMapping("/getAllLeafNodes")
    public CommonResult getAllLeafNodes(@RequestBody RotaryOrderQuery query) {
        List<ComponentHierarchyDTO> leafNodes = rotarySystemService.getAllLeafNodes(query);
        return CommonResult.success(leafNodes);
    }
    @PostMapping("/selectComponentsByPN/{current}/{size}")
    public CommonResult selectComponentsByPN(@PathVariable long current,
                                             @PathVariable long size,
                                             @RequestBody RotaryOrderQuery query) {
        IPage<RotaryComponentSelectVo> page = new Page<>(current, size);
        rotarySystemService.selectRotaryComponentList(page,query);
        return CommonResult.success(page);
    }

}
