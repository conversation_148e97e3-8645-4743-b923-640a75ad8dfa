package net.tartan.platform.integration.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.common.beans.ResultCode;
import net.tartan.platform.integration.entity.WarehouseCount;
import net.tartan.platform.integration.exception.BusinessException;
import net.tartan.platform.integration.service.IWarehouseCountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * warehouse 库房统计
 */
@RestController
@RequestMapping("/warehouse")
@Slf4j
public class WarehouseCountController {

    @Autowired
    private IWarehouseCountService warehouseCountService;

    /**
     * 库房仪器状态统计接口
     *
     * @param warehouseCount
     * @return
     */
    @PostMapping("/queryByDeviceTypeList")
    public CommonResult queryByDeviceTypeList(@RequestBody WarehouseCount warehouseCount) {
        return CommonResult.success(warehouseCountService.getList(warehouseCount.getNameList()));
    }

    /**
     * 库房仪器名称列表查询
     *
     * @return
     */
    @PostMapping("/getAllName")
    public CommonResult getAllName() {
        return CommonResult.success(warehouseCountService.getAllName());
    }

    /**
     * 根据库房内的仪器名 查看使用情况
     *
     * @param current
     * @param size
     * @param wellInfoQuery
     * @return
     */
    @PostMapping("queryByDeviceType/{current}/{size}")
    public CommonResult queryByDeviceType(@PathVariable long current,
                                    @PathVariable long size,
                                    @RequestBody WarehouseCount wellInfoQuery) {
        IPage<WarehouseCount> page = new Page<>(current, size);
        warehouseCountService.queryByDeviceType(page, wellInfoQuery);
        return CommonResult.success(page);
    }

    /**
     * 库房仪器列表查询
     *
     * @param
     * @return
     */
    @PostMapping("list/{current}/{size}")
    public CommonResult add(@PathVariable long current,
                            @PathVariable long size,
                            @RequestBody(required = false) WarehouseCount query) {
        if (query == null) {
            query = new WarehouseCount();
        }
        IPage<WarehouseCount> page = new Page<>(current, size);
        warehouseCountService.getListByPage(page, query);
        return CommonResult.success(page);
    }

    /**
     * 新增库房仪器
     *
     * @param
     * @return
     */
    @PostMapping("add")
    public CommonResult add(@RequestBody WarehouseCount warehouseCount) {
        warehouseCountService.save(warehouseCount);
        return CommonResult.success();
    }

    /**
     * 更新库房仪器
     *
     * @param
     * @return
     */
    @PostMapping("update")
    public CommonResult update(@RequestBody WarehouseCount warehouseCount) {
        if (warehouseCount.getDeviceId() == null) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        warehouseCountService.updateById(warehouseCount);
        return CommonResult.success();
    }

    /**
     * 删除库房仪器
     *
     * @param
     * @return
     */
    @PostMapping("delete")
    public CommonResult delete(@RequestParam("deviceId") Long deviceId) {
        warehouseCountService.removeById(deviceId);
        return CommonResult.success();
    }

    /**
     * 仪器使用情况 导出excel
     */
    @GetMapping("device/export")
    public CommonResult orderExport() {
//        if (startTime == null || endTime==null) {
//            throw new BusinessException(ResultCode.PARAMS_ERROR);
//        }
        warehouseCountService.deviceExportExcel();
        return CommonResult.success();
    }

}
