package net.tartan.platform.integration.mapper.visuallog;

import net.tartan.platform.integration.beans.vo.visuallog.VisualLogDataVo;
import net.tartan.platform.integration.entity.visuallog.VlResultsSet;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-10-08
 */
public interface VlResultsSetMapper extends BaseMapper<VlResultsSet> {
    List<VisualLogDataVo> getVisualLogDataList(@Param("runIdList") List<Long> runIdList, @Param("jobId") long jobId, @Param("tagIdList")List<Integer> tagIdList);
}
