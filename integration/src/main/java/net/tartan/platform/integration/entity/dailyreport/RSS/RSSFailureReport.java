package net.tartan.platform.integration.entity.dailyreport.RSS;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.constant.PfConstant;
import net.tartan.platform.integration.entity.RSSCompentFailedPartsInfo;
import net.tartan.platform.integration.entity.dailyreport.MwdComponent;
import net.tartan.platform.integration.entity.dailyreport.MwdDailyActivity;
import net.tartan.platform.integration.entity.dailyreport.MwdTool;
import net.tartan.platform.integration.entity.dailyreport.MwdWorkSummary;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = PfConstant.RSS_FAILURE_REPORT)
public class RSSFailureReport implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @JsonIgnore
    private String reportId;

    @Transient
    private Long id;

    /**
     * 报告类型
     */
    @Transient
    private String reportType;

    /**
     * 报告井id
     */
    @NotNull
    private Long jobId;
    private String jobNumber;
    private String jobType;

    private String wellNumber;

    /**
     * 失效报告的日期
     */
    @NotEmpty
    private String date;

    /**
     * 失效时间
     * incident date
     * yyyy-mm-dd hh:MM
     */
    private String incidentDate;

    /**
     * 事件描述
     */
    private String incidentDescription;
    /**
     * 事件报告人
     */
    private String incidentReporter;

    /**
     * 失效地点
     */
    private String failureLocation;

    /**
     * 启动模式
     */
    private String activityMode;

    /**
     * 失效类型
     */
    private String failureType;
    /**
     * 建议失效序列号
     */
    private String suggestedFailureSn;
    /**
     * 失效时的循环时间（小时）
     * Circulcation Hrs.at Failure
     */
    private BigDecimal circulateHrsAtFailure;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 失效部件列表
     */
    private List<MwdComponent> componentList;
    /**
     * 地面检查及结果
     */
    private String surfaceInspection;

    /**
     * 车间检查
     */
    private String shopInspectionc;

    /**
     * 改进计划/措施
     */
    private String improvementPlanMeasures;


    /**
     * 失效趟次
     */
    private Integer run;

    // 以下是暂时不用的字段
    private String location; // 地点
    private BigDecimal measuredDepthAtFailure; // 失效位置测深
    private BigDecimal nonProductiveTime;// 非生产时间
    private String suggestedFailurePartNo; // 建议失效部件号
    private String failureSymptom; // 失效症状
    private Integer tripForFailure; // 故障跳闸

    private List<RSSCompentFailedPartsInfo> rssFailedPartsList;

}
