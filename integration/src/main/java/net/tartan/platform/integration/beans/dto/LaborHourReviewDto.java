package net.tartan.platform.integration.beans.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import net.tartan.platform.common.enums.EnumReviewStatus;
import net.tartan.platform.integration.beans.vo.LaborHourVo;
import net.tartan.platform.integration.entity.devLaborHour.LaborHourReviewDetail;
import org.springframework.data.annotation.Id;

import java.util.Date;
import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class LaborHourReviewDto {
    private static final long serialVersionUID = 1L;
    /**
     * 申请人的memberId
     */
    private String memberId;

    /**
     * 所属项目编号
     */
    private Long projectId;

    /**
     * 工时日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date laborDate;

    /**
     * 提交申请的日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date submitDate;

    /**
     * 申请调整工时的详情
     * 里面所有的详情都是属于同一个项目下的
     */
    private List<LaborHourVo> laborHourInfos;

    // false 表示非测试数据 走正常日期校验
    // true 表示测试数据 跳过日期校验
    private boolean testLine = false;
}
