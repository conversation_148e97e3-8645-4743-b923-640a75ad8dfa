package net.tartan.platform.integration.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * rma工单信息表
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WorkOrderRmaRepairMember implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "work_order_rma_repair_member_id", type = IdType.AUTO)
    private Long workOrderRmaRepairMemberId;

    /**
     * 工单单号
     */
    private Long rmaId;

    /**
     * 员工Id
     */
    private Long memberId;

    /**
     * 维修耗时
     */
    private BigDecimal laborHours;

    /**
     * 备注
     */
    private String notes;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
