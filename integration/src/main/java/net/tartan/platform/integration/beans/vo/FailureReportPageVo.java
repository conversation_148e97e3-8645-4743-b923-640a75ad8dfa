package net.tartan.platform.integration.beans.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.integration.beans.vo.dailyreport.ReportBaseVo;

import java.util.Date;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FailureReportPageVo {
    private static final long serialVersionUID = 1L;

    /**
     * 作业编号
     */
    private Long jobId;


    /**
     * 作业号，工单号
     *  - from JobInfo
     */
    private String jobNumber;

    /**
     * 井号
     *  - from JobInfo
     */
    private String wellId;

    /**
     * job 开钻日期
     */
    private String dateIn;
    /**
     * job 完钻日期
     */
    private String dateOut;

    /**
     * 井名
     *  - from WellInfo
     */
    private String wellNumber;

    /**
     * 服务类型
     *  - count EnumJobStatistics
     *      仪器服务 APPARATUS_COUNT
     *      大包服务 PACKAGE_COUNT
     *      工程服务 PROJECT_COUNT
     */
    private Long jobType;

    /**
     * 工程状态（完井/施工中） 0 1
     */
    private Integer jobStatus;

    /**
     * 总趟数
     * totalMwdRuns
     */
    private Integer totalRun;

    /**
     * 失效趟数
     * mwdFailures
     */
    private Integer failureRun;

    /**
     * 失效原因
     */
    private String failureReason;

    /**
     * 区块
     *  - from WellInfo
     *  blocks
     */
    private String blocks;

    private List<ReportBaseVo> failureReportBaseInfo;

    /**
     * 创建时间
     */
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date createTime;

    /**
     * 失效报告提交人所属的部门名称
     */
    private String failureReportDepartmentName;

    /**
     * 失效报告提交人姓名
     */
    private String failureReportUserName;

    /**
     * mwd工单的提交人所属的部门名
     */
    private String mwdDepartmentName;

//    private String date;

    private String updateStatus;

    /**
     * 有没有失效报告
     * true -> 有失效报告
     * false -> 无失效报告
     */
    private Boolean hasFailureReport;
    //改进计划/措施
    private String improvementPlanMeasures;

    /**
     * 有没有维修工单
     * true -> 有
     * false -> 没有
     */
    private Boolean hasMwdWorkOrder;



}
