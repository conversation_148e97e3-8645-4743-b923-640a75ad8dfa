package net.tartan.platform.integration.beans.vo.dailyreport;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.enums.EnumReportType;
import net.tartan.platform.integration.entity.dailyreport.MwdSurveysActivity;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MwdSurveysReportVo {

    private EnumReportType reportType;

    private long lastModified;

    /**
     * 公司
     */
    private String company;

    /**
     * 测斜组编号 （作用同 趟次中的run）
     */
    @Builder.Default
    private Integer surveyNumber = 1;

    /**
     * 井号
     */
    private String wellNumber;

    /**
     * 磁偏角
     */
    private BigDecimal magDec;

    /**
     * 测斜零长
     */
    private BigDecimal bitToSensor;

    /**
     * 钻头类型
     */
    private String bitType;

    /**
     * 人员
     */
    private List<MwdSurveysActivity> activityList;
}
