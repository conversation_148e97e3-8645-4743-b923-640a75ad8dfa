package net.tartan.platform.integration.entity.dailyreport;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.constant.PfConstant;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * mwd_surveys_report
 *
 * <AUTHOR>
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = PfConstant.MWD_SURVEYS_REPORT)
public class MwdSurveysReport{
    @Transient
    private Long id;

    @Id
    @JsonIgnore
    private String reportId;

    /**
     * 作业id
     */
    @NotNull
    private Long jobId;
    private String jobNumber;

    /**
     * 公司
     */
    private String company;
    /**
     * 趟次
     */
    @NotNull
    @Builder.Default
    private Integer surveyNumber = 1;

    /**
     * 井号
     */
    private String wellNumber;

    /**
     * 磁偏角
     */
    private BigDecimal magDec;

    /**
     * 测斜零长
     */
    private BigDecimal bitToSensor;

    /**
     * 钻头类型
     */
    private String bitType;

    /**
     * 人员
     */
    private List<MwdSurveysActivity> activityList;
}
