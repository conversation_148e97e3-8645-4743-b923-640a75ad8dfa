package net.tartan.platform.integration.beans.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import net.tartan.platform.common.enums.EnumRiskType;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 设备库存信息表
 */
@Data
public class ComponentVo {

    private Long componentId;
    /**
     * 品名
     */
    private String invName;

    /**
     * 序列号，erp的批次号
     */
    private String serialNumber;
    /**
     * 固件版本号
     */
    private String versionNumber;

    /**
     * 数量
     */
    private Integer quantity;
    /**
     * 风险类型
     */
    private EnumRiskType riskType;

    /**
     * 额定温度
     */
    private BigDecimal standardBht;

    /**
     * 最高温度
     */
    private BigDecimal maxBht;

    /**
     * 修正最高温度
     */
    private BigDecimal reviseMaxBht;
//    /**
//     * 使用时长
//     */
//    private BigDecimal totalHours;

    /**
     * 修正使用时长
     */
    private BigDecimal reviseTotalHours;
    /**
     * 总循环时间
     */
    private BigDecimal totalCirculateHrs;
    /**
     * 总入井时间
     */
    private BigDecimal totalInWellHrs;

    /**
     * 最大时长
     */
    private BigDecimal maxHours;

    /**
     * 使用时长警戒线
     */
    private BigDecimal targetLine;

    /**
     * 制造商
     */
    private String manufacturer;

    /**
     * 温度类型
     */
    private String tempType;

    /**
     * 风险值
     */
    private BigDecimal riskValue;

    /**
     * 入库时间
     */
    private Date stockInDate;

    /**
     * 备注
     */
    private String note;

    /**
     * 最新修改的台账id
     */
    private Long lastMwdId;

    // 部件的最后更新人
    private String lastUpdateUser;

    // 部件的创建人
    private String createdByUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    // 旋导部件参数
    // 品号
    private String invCode;
    // 最后修改时间
    private String lastAssemblyDate;
    private Long parentComponentId;
    // 部件可用层级
    private Integer level;
    private Boolean isLeaf;
    private Integer sort;

    // 母件仪器信息
    private Long ancestorDeviceId;
    private String ancestorDeviceInvName;
    private String ancestorDeviceSerialNumber;

}
