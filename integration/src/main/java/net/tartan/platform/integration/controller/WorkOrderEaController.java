package net.tartan.platform.integration.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.common.beans.ResultCode;
import net.tartan.platform.integration.beans.dto.WorkOrderEaDto;
import net.tartan.platform.integration.beans.dto.WorkOrderMwdDto;
import net.tartan.platform.integration.beans.query.WorkOrderEaQuery;
import net.tartan.platform.integration.beans.query.WorkOrderMwdQuery;
import net.tartan.platform.integration.beans.vo.*;
import net.tartan.platform.integration.entity.WorkOrderEa;
import net.tartan.platform.integration.entity.WorkOrderMwd;
import net.tartan.platform.integration.entity.deviceassemble.EaComponentAssembleRecord;
import net.tartan.platform.integration.entity.deviceassemble.MwdDeviceAssembleRecord;
import net.tartan.platform.integration.exception.BusinessException;
import net.tartan.platform.integration.service.IWorkOrderEaService;
import net.tartan.platform.integration.service.IWorkOrderMwdService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * ea工单信息表 前端控制器
 *
 */
@RestController
@RequestMapping("/workOrderEa")
public class WorkOrderEaController {
    @Autowired
    private IWorkOrderEaService workOrderEaService;

    @PostMapping("add")
    public CommonResult add(@RequestBody @Valid WorkOrderEaDto orderEaDto) {
        if (orderEaDto.getEaNumber() == null) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        Long eaId = workOrderEaService.add(orderEaDto);
        WorkOrderEaVo workOrderEaVo = workOrderEaService.info(eaId);
        return CommonResult.success(workOrderEaVo);
    }

    @GetMapping("info")
    public CommonResult info(@RequestParam("eaId") long eaId) {
        WorkOrderEaVo workOrderEaVo = workOrderEaService.info(eaId);
        return CommonResult.success(workOrderEaVo);
    }

    @PostMapping("getEaTree")
    public CommonResult getEaTree(@RequestBody @Valid WorkOrderEaQuery query) {
        EaTreeVO eaTreeVO = workOrderEaService.getEaTreeByEaId(query);
        return CommonResult.success(eaTreeVO);
    }

    @PostMapping("list/{current}/{size}")
    public CommonResult list(@PathVariable long current, @PathVariable long size, @RequestBody(required = false) WorkOrderEaQuery query) {
        IPage<WorkOrderEaDetailResponse> page = new Page<>(current, size);
        if (query == null) {
            query = new WorkOrderEaQuery();
        }
        workOrderEaService.list(page, query);
        return CommonResult.success(page);
    }

    @PostMapping("update")
    public CommonResult update(@RequestBody @Valid WorkOrderEaVo orderEaVo) {
        if (orderEaVo.getEaId() == null) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        workOrderEaService.update(orderEaVo);
        WorkOrderEaVo workOrderEaVo = workOrderEaService.info(orderEaVo.getEaId());
        return CommonResult.success(workOrderEaVo);
    }

    /**
     * 仪器的组装与拆卸
     *
     * @param record
     * @return
     */
    @PostMapping("assemble")
    public CommonResult assemble(@RequestBody @Valid EaComponentAssembleRecord record) {
        if (record.getEaId() == null) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        workOrderEaService.assemble(record);
        return CommonResult.success();
    }


    @PostMapping("delete")
    public CommonResult delete(@RequestBody @Valid WorkOrderEaVo orderEaVo) {
        if (orderEaVo.getEaId() == null) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        workOrderEaService.delete(orderEaVo.getEaId());
        return CommonResult.success();
    }

    @PostMapping("finish")
    public CommonResult finish(@RequestBody @Valid WorkOrderEa orderEa) {
        if (orderEa.getEaId() == null) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        workOrderEaService.updateFinish(orderEa);
        return CommonResult.success();
    }

}
