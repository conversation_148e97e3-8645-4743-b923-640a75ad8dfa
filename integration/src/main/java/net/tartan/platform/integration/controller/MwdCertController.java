package net.tartan.platform.integration.controller;

import com.alibaba.fastjson.JSONObject;
import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.common.beans.ResultCode;
import net.tartan.platform.integration.beans.vo.cert.CertVo;
import net.tartan.platform.integration.entity.mwdCert.CertApprovalInfo;
import net.tartan.platform.integration.entity.mwdCert.MwdCertBaseInfo;
import net.tartan.platform.integration.exception.BusinessException;
import net.tartan.platform.integration.service.cert.CertService;
import net.tartan.platform.integration.service.cert.MwdCertService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 生成合格证
 */
@RestController
@RequestMapping("/cert")
public class MwdCertController {

    @Autowired
    private MwdCertService certService;

    @PostMapping("/init")
    public CommonResult getInfo(@RequestParam("mwdId") long mwdId) {

        if (mwdId <= 0) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        CertVo certVo = certService.initCert(mwdId);
        return CommonResult.success(certVo);
    }

    @PostMapping("/select")
    public CommonResult select(@RequestParam("mwdId") long mwdId) {

        if (mwdId <= 0) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        CertVo certVo = certService.selectCertByMwdId(mwdId);
        return CommonResult.success(certVo);
    }

    @PostMapping("/update")
    public CommonResult update(@RequestBody JSONObject certBaseInfo) {

        if (certBaseInfo == null) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        CertVo certVo = certService.update(certBaseInfo);
        return CommonResult.success(certVo);
    }

    @PostMapping("/approval")
    public CommonResult approval(@RequestBody CertApprovalInfo approvalInfo) {

        if (approvalInfo == null) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        certService.approval(approvalInfo);
        return CommonResult.success();
    }

    @PostMapping("/exportCert")
    public CommonResult exportCert(@RequestParam("mwdId") long mwdId) {

        if (mwdId <= 0) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        certService.exportCert(mwdId);
        return CommonResult.success();
    }


}
