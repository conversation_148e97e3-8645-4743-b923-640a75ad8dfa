package net.tartan.platform.integration.controller;


import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.integration.entity.ProduceTemplate;
import net.tartan.platform.integration.service.IProduceTemplateService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 模版配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-06
 */
@RestController
@RequestMapping("/produceTemplate")
public class ProduceTemplateController {
    @Autowired
    private IProduceTemplateService taskTemplateService;

    /**
     * 查询任务模板
     *
     * @param produceTemplate
     * @return
     */
    @PostMapping("list")
    public CommonResult taskTemplateList(@RequestBody(required = false) ProduceTemplate produceTemplate) {
        List<ProduceTemplate> templateList = taskTemplateService.listByCondition(produceTemplate);
        return CommonResult.success(templateList);
    }

    @PostMapping("info")
    public CommonResult taskTemplateInfo(@RequestBody ProduceTemplate produceTemplate) {
        String templateId = produceTemplate.getTemplateId();
        ProduceTemplate template = taskTemplateService.getInfoById(templateId);
        return CommonResult.success(template);
    }

//    @PostMapping("childList")
//    public CommonResult taskTemplateInfo(@RequestBody ProduceTemplate produceTemplate) {
//        //当前列表进查询所有子节点
//        List<ProduceTemplate> templateList = taskTemplateService.listByCondition(produceTemplate);
//        return CommonResult.success(templateList);
//    }

    @PostMapping("add")
    public CommonResult addTaskTemplate(@RequestBody ProduceTemplate produceTemplate) {
        taskTemplateService.addTaskTemplate(produceTemplate);
        return CommonResult.success();
    }

//    @PostMapping("addBatch")
//    public CommonResult addTaskTemplateBatch(@RequestBody List<ProduceTemplate> templateList) {
//        if (CollectionUtils.isNotEmpty(templateList)) {
//            taskTemplateService.addTaskTemplateBatch(templateList);
//        }
//        return CommonResult.success();
//    }

    @PostMapping("update")
    public CommonResult updateTaskTemplate(@RequestBody ProduceTemplate produceTemplate) {
        taskTemplateService.updateTaskTemplate(produceTemplate);
        return CommonResult.success();
    }

    @PostMapping("delete")
    public CommonResult deleteTaskTemplate(@RequestBody ProduceTemplate produceTemplate) {
        String templateId = produceTemplate.getTemplateId();
        taskTemplateService.deleteTaskTemplate(templateId);
        return CommonResult.success();
    }
}
