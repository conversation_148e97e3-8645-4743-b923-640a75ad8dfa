package net.tartan.platform.integration.beans.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.integration.entity.Department;
import net.tartan.platform.integration.entity.MemberInfo;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class MemberTree {
    private Department department;
    private List<MemberInfo> memberList;
    private List<MemberTree> childrenList;
}
