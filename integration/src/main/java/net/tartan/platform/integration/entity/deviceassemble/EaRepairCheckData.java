package net.tartan.platform.integration.entity.deviceassemble;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import net.tartan.platform.common.constant.PfConstant;
import net.tartan.platform.integration.entity.deviceassemble.repaircheck.BaseCheckData;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.validation.constraints.NotNull;

@Data
@Document(collection = PfConstant.EA_REPAIR_CHECK)
public class EaRepairCheckData<T extends BaseCheckData>  {
    @Id
    private String checkId;

    /**
     * 台账id
     */
    @NotNull
    private Long eaId;

    private String deviceTypeStr;

    private EaComponentBaseData componentBaseData;

    private T checkData;

    public <RESULT> RESULT getResult(Class<RESULT> requiredType) {
        return JSON.parseObject(JSON.toJSONString(this.checkData), requiredType);
    }
}
