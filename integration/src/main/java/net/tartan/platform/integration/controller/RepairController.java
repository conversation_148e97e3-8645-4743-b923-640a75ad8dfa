package net.tartan.platform.integration.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.common.beans.ResultCode;
import net.tartan.platform.common.enums.EnumToolType;
import net.tartan.platform.integration.beans.query.RepairQuery;
import net.tartan.platform.integration.beans.vo.RepairVo;
import net.tartan.platform.integration.entity.repair.Repair;
import net.tartan.platform.integration.exception.BusinessException;
import net.tartan.platform.integration.service.IRepairService;
import org.apache.commons.lang3.ObjectUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-10
 */
@RestController
@RequestMapping("/repair")
public class RepairController {
    @Autowired
    private IRepairService repairService;

    /**
     * 返修单列表
     */
    @PostMapping("list/{current}/{size}")
    public CommonResult listRepair(@PathVariable long current,
                                   @PathVariable long size,
                                   @RequestBody RepairQuery query) {
        IPage<Repair> page = new Page<>(current, size);
        repairService.getListByPage(page, query);
        return CommonResult.success(page);
    }

    /**
     * 返修单mwd详情
     */
    @GetMapping("/info")
    public CommonResult infoRepair(@RequestParam("repairId") Long repairId) {
        RepairVo repairVo = repairService.getRepairInfo(repairId);
        return CommonResult.success(repairVo);
    }

    /**
     * 返修单mwd详情
     */
    @GetMapping("/infoByCode")
    public CommonResult infoRepair(@RequestParam("repairCode") String repairCode) {
        RepairVo repairVo = repairService.getRepairInfoByCode(repairCode);
        return CommonResult.success(repairVo);
    }

    /**
     * 新增mwd返修单
     *
     * @param repairVo
     * @return
     */
    @PostMapping("add")
    public CommonResult addRepair(@RequestBody @Valid RepairVo repairVo) {
        DateTime dateTime = new DateTime();
        EnumToolType toolType = repairVo.getToolType();
        if (EnumToolType.MWD.equals(toolType)) {
            repairVo.setRepairCode("RPMWD" + dateTime.toString("yyyyMMddHHmmss"));
        } else {
            repairVo.setRepairCode("RPUNDERWELL" + dateTime.toString("yyyyMMddHHmmss"));
        }
        repairService.addRepair(repairVo);
        return CommonResult.success();
    }

    @PostMapping("receive")
    public CommonResult receive(@RequestParam("repairId") Long repairId) {
        if (repairId == null) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        repairService.receive(repairId);
        return CommonResult.success();
    }

    /**
     * 返修单详情接收
     *
     * @param detailIdList
     * @return
     */
    @PostMapping("receiveDetail")
    public CommonResult receiveDetail(@RequestParam("detailIdList") List<Long> detailIdList) {
        if (detailIdList.isEmpty()) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        repairService.receiveDetail(detailIdList);
        return CommonResult.success();
    }

    /**
     * mwd返修单更新
     *
     * @param repairVo
     * @return
     */
    @PostMapping("update")
    public CommonResult updateRepair(@RequestBody @Valid RepairVo repairVo) {
        if (repairVo.getRepairId() == null) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        repairService.updateRepair(repairVo);
        return CommonResult.success();
    }

    /**
     * 删除mwd返修单
     *
     * @return
     */
    @PostMapping("delete")
    public CommonResult deleteRepair(@RequestParam("repairId") Long repairId) {
        if (repairId == null) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        repairService.deleteRepair(repairId);
        return CommonResult.success();
    }

    @PostMapping("export")
    public CommonResult exportRepair(@RequestParam("repairId") Long repairId) {
        if (repairId == null) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        repairService.exportRepair(repairId);
        return CommonResult.success();
    }

}
