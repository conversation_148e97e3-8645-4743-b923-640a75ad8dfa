package net.tartan.platform.integration.entity.deviceassemble.repaircheck.pitTools;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import net.tartan.platform.integration.entity.deviceassemble.repaircheck.BaseCheckData;

import java.util.Date;

//井下维修通用类
@Data
public class ToolsCommonData {


    //-设备/检具
    private String device;

    //-工时/件
    private String taskTime;

    //-操作者
    private String operator;

    //-日期 timezone="GMT+8"
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8" )
    private Date date;

    //-备注
    private String comments;
}
