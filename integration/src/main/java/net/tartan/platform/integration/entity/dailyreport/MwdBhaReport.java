package net.tartan.platform.integration.entity.dailyreport;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.constant.PfConstant;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * mwd_bha_report
 *
 * <AUTHOR>
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = PfConstant.MWD_BHA_REPORT)
public class MwdBhaReport implements Serializable {

    @Transient
    private Long id;

    @Id
    @JsonIgnore
    private String reportId;

    /**
     * 作业id
     */
    @NotNull
    private Long jobId;
    private String jobNumber;

    /**
     * 趟次
     */
    @NotNull
    private Integer run;

    /**
     * 公司
     */
    private String company;

    /**
     * 承包商
     */
    private String contractor;

    /**
     * 井队号
     */
    private String rigNo;

    /**
     * 开始时间
     */
    private String startDate;

    /**
     * 井号
     */
    private String wellNumber;
    /**
     * 位置
     */
    private String location;

    /**
     * 入井井深
     */
    private BigDecimal depthIn;

    /**
     * 出井井深
     */
    private BigDecimal depthOut;

    private String bha;

    /**
     * 目的
     */
    private String purpose;

    /**
     * 测斜零长
     */
    private BigDecimal surveySensor;

    /**
     * 伽马零长
     */
    private BigDecimal gammaSensor;

    private String bhaResults;

    /**
     * 起钻原因
     */
    private String reasonForPooh;

    /**
     * 定向工程师
     */
    private String directionalDrillers;

    /**
     * 钻具组合
     */
    private List<MwdBhaItem> itemList;
}
