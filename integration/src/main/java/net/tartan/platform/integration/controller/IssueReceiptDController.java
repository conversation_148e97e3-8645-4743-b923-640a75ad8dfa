package net.tartan.platform.integration.controller;

import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.integration.beans.query.IssueReceiptQuery;
import net.tartan.platform.integration.entity.deviceassemble.repaircheck.ReplaceItem;
import net.tartan.platform.integration.service.erp.IErpIssueReceiptDService;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 领/退料单单身/CHT/領//退料單單身/ENU/Material Requisition//Return Note Detail 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-14
 */
@RestController
@RequestMapping("/pick")
public class IssueReceiptDController {

    @Autowired
    private IErpIssueReceiptDService issueReceiptDService;

    @PostMapping("list")
    public CommonResult list(@RequestBody(required = false) IssueReceiptQuery query) {
        if (ObjectUtils.isEmpty(query)) {
            return CommonResult.success(new ArrayList<>());
        }
        List<ReplaceItem> list = issueReceiptDService.getIssueReceiptDList(query);
        return CommonResult.success(list);
    }

    @PostMapping("docNoList")
    public CommonResult docNoList(@RequestParam(value = "docNo") String docNo) {
        List<String> list = issueReceiptDService.selectDocNoList(docNo);
        return CommonResult.success(list);
    }
}
