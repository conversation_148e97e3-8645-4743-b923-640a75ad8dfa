package net.tartan.platform.integration.beans.query;

import lombok.Data;
import net.tartan.platform.common.enums.EnumCirculateType;

import java.util.List;

@Data
public class OaToolQuery {

    private String jobNumber;
    private String no;
    private String kitNumber;
    private String clientName;
    private String wellName;

    // 钻具通知单上的日期
    private String startDate;
    private String endDate;

    // 单据审核状态 1 为正常结束
    private Integer finishedflag;


    private List<String> wellNumberList; // 井号列表（每个井号分别有一个结尾带“井”的名字和不带井的名字）
    private List<String> orderNumberList; // 返修单和需求单的单号列表

    private Long orderId;
    private EnumCirculateType orderType;// DEMAND_ORDER REPAIR

}
