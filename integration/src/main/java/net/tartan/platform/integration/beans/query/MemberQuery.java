package net.tartan.platform.integration.beans.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@AllArgsConstructor
@Builder
public class MemberQuery {
    private long current;
    private long total;
    private long pages;
    private long size;
    /**
     * 姓名
     */
    private String name;
    /**
     * 部门id
     */
    private Long departmentId;
    /**
     * 电话号
     */
    private String phoneNumber;

    /**
     * 邮箱
     */
    private String email;

    public MemberQuery() {
        this.current = 1;
        this.size = 10;
    }
}
