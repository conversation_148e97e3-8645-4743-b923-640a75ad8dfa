package net.tartan.platform.integration.entity.deviceassemble.repaircheck;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class PulserCheckData extends BaseCheckData {
    private List<CheckDataModel> driverInspectionList;
    private List<CheckDataModel> oilfillSectionInspectionList;
    private List<CheckDataModel> driverRingOutList;
    private List<CheckDataModel> motorRingOutList;
    private List<CheckDataModel> heatTestList;
    private List<CheckDataModel> functionTestList;

    public PulserCheckData init() {
        PulserCheckData dmCheckData = new PulserCheckData();

        dmCheckData.setDriverInspectionList(driverInspectionListInit());

        dmCheckData.setOilfillSectionInspectionList(oilfillSectionInspectionListInit());

        dmCheckData.setDriverRingOutList(driverRingOutListInit());

        dmCheckData.setMotorRingOutList(motorRingOutListInit());

        dmCheckData.setHeatTestList(heatTestListInit());

        dmCheckData.setFunctionTestList(functionTestListInit());

        //初始化故障诊断
        dmCheckData.setFaultDiagnosis(new FaultDiagnosis().init());
        return dmCheckData;
    }

    private List<CheckDataModel> driverInspectionListInit() {
        List<CheckDataModel> checkDataModelList = new ArrayList<>();

        CheckDataModel checkDataModel0 = new CheckDataModel();
        checkDataModel0.setName("Accel Min/ Max Value");
        checkDataModel0.setValue("");
        checkDataModel0.setIn("");
        checkDataModel0.setOut("");
        checkDataModelList.add(checkDataModel0);

        CheckDataModel checkDataModel1 = new CheckDataModel();
        checkDataModel1.setName("Flow Sw. Voltage On/ Off");
        checkDataModel1.setValue(">0.3v / 5V");
        checkDataModel1.setIn("");
        checkDataModel1.setOut("");
        checkDataModelList.add(checkDataModel1);

        CheckDataModel checkDataModel2 = new CheckDataModel();
        checkDataModel2.setName("Load Current");
        checkDataModel2.setValue("600mA");
        checkDataModel2.setIn("");
        checkDataModel2.setOut("");
        checkDataModelList.add(checkDataModel2);

        CheckDataModel checkDataModel3 = new CheckDataModel();
        checkDataModel3.setName("Standby Current");
        checkDataModel3.setValue("9mA (Driver Only) 15-0mA(Gamma)");
        checkDataModel3.setIn("");
        checkDataModel3.setOut("");
        checkDataModelList.add(checkDataModel3);

        CheckDataModel checkDataModel4 = new CheckDataModel();
        checkDataModel4.setName("Snubber Shock");
        checkDataModel4.setValue("");
        checkDataModel4.setIn("");
        checkDataModel4.setOut("");
        checkDataModelList.add(checkDataModel4);

        CheckDataModel checkDataModel5 = new CheckDataModel();
        checkDataModel5.setName("MDM Condition");
        checkDataModel5.setValue("");
        checkDataModel5.setIn("");
        checkDataModel5.setOut("");
        checkDataModelList.add(checkDataModel5);

        CheckDataModel checkDataModel6 = new CheckDataModel();
        checkDataModel6.setName("Pin/ Socket Test");
        checkDataModel6.setValue("");
        checkDataModel6.setIn("");
        checkDataModel6.setOut("");
        checkDataModelList.add(checkDataModel6);

        CheckDataModel checkDataModel7 = new CheckDataModel();
        checkDataModel7.setName("Interconnect Housing");
        checkDataModel7.setValue("");
        checkDataModel7.setIn("");
        checkDataModel7.setOut("");
        checkDataModelList.add(checkDataModel7);

        CheckDataModel checkDataModel8 = new CheckDataModel();
        checkDataModel8.setName("Kintec Condition");
        checkDataModel8.setValue("");
        checkDataModel8.setIn("");
        checkDataModel8.setOut("");
        checkDataModelList.add(checkDataModel7);

        CheckDataModel checkDataModel9 = new CheckDataModel();
        checkDataModel9.setName("Gamma Counts (Background)");
        checkDataModel9.setValue("");
        checkDataModel9.setIn("");
        checkDataModel9.setOut("");
        checkDataModelList.add(checkDataModel9);

        CheckDataModel checkDataModel10 = new CheckDataModel();
        checkDataModel10.setName("Gamma Counts (Source)");
        checkDataModel10.setValue("");
        checkDataModel10.setIn("");
        checkDataModel10.setOut("");
        checkDataModelList.add(checkDataModel10);

        return checkDataModelList;
    }

    private List<CheckDataModel> oilfillSectionInspectionListInit() {
        List<CheckDataModel> checkDataModelList = new ArrayList<>();

        CheckDataModel checkDataModel0 = new CheckDataModel();
        checkDataModel0.setName("Compensation Test");
        checkDataModel0.setIn("");
        checkDataModel0.setOut("");
        checkDataModelList.add(checkDataModel0);

        CheckDataModel checkDataModel1 = new CheckDataModel();
        checkDataModel1.setName("Flashlight Test");
        checkDataModel1.setIn("");
        checkDataModel1.setOut("");
        checkDataModelList.add(checkDataModel1);

        CheckDataModel checkDataModel2 = new CheckDataModel();
        checkDataModel2.setName("Bellows Condition");
        checkDataModel2.setIn("");
        checkDataModel2.setOut("");
        checkDataModelList.add(checkDataModel2);

        CheckDataModel checkDataModel3 = new CheckDataModel();
        checkDataModel3.setName("Oil Condition");
        checkDataModel3.setIn("");
        checkDataModel3.setOut("");
        checkDataModelList.add(checkDataModel3);

        CheckDataModel checkDataModel4 = new CheckDataModel();
        checkDataModel4.setName("Membrane Condition");
        checkDataModel4.setIn("");
        checkDataModel4.setOut("");
        checkDataModelList.add(checkDataModel4);

        CheckDataModel checkDataModel5 = new CheckDataModel();
        checkDataModel5.setName("Flex-seal Condition");
        checkDataModel5.setIn("");
        checkDataModel5.setOut("");
        checkDataModelList.add(checkDataModel5);

        CheckDataModel checkDataModel6 = new CheckDataModel();
        checkDataModel6.setName("Servo Poppet (Cond./ Type)");
        checkDataModel6.setIn("");
        checkDataModel6.setOut("");
        checkDataModelList.add(checkDataModel6);

        CheckDataModel checkDataModel7 = new CheckDataModel();
        checkDataModel7.setName("Servo Orifice  (Cond./ Type)");
        checkDataModel7.setIn("");
        checkDataModel7.setOut("");
        checkDataModelList.add(checkDataModel7);

        CheckDataModel checkDataModel8 = new CheckDataModel();
        checkDataModel8.setName("Spline Nut Condition");
        checkDataModel8.setIn("");
        checkDataModel8.setOut("");
        checkDataModelList.add(checkDataModel8);

        CheckDataModel checkDataModel9 = new CheckDataModel();
        checkDataModel9.setName("Servo Poppet Shaft");
        checkDataModel9.setIn("");
        checkDataModel9.setOut("");
        checkDataModelList.add(checkDataModel9);

        CheckDataModel checkDataModel10 = new CheckDataModel();
        checkDataModel10.setName("Ball Screw Assembly");
        checkDataModel10.setIn("");
        checkDataModel10.setOut("");
        checkDataModelList.add(checkDataModel10);

        CheckDataModel checkDataModel11 = new CheckDataModel();
        checkDataModel11.setName("Screen HousingType / Condition");
        checkDataModel11.setIn("");
        checkDataModel11.setOut("");
        checkDataModelList.add(checkDataModel11);

        CheckDataModel checkDataModel12 = new CheckDataModel();
        checkDataModel12.setName("Ball Screw Length");
        checkDataModel12.setIn("");
        checkDataModel12.setOut("");
        checkDataModelList.add(checkDataModel12);

        return checkDataModelList;
    }

    private List<CheckDataModel> driverRingOutListInit() {
        List<CheckDataModel> checkDataModelList = new ArrayList<>();

        CheckDataModel checkDataModel0 = new CheckDataModel();
        checkDataModel0.setName("1-2");
        checkDataModel0.setValue("Open/OL");
        checkDataModel0.setIn("");
        checkDataModel0.setOut("");
        checkDataModelList.add(checkDataModel0);

        CheckDataModel checkDataModel1 = new CheckDataModel();
        checkDataModel1.setName("1-3");
        checkDataModel1.setValue("Open/OL");
        checkDataModel1.setIn("");
        checkDataModel1.setOut("");
        checkDataModelList.add(checkDataModel1);

        CheckDataModel checkDataModel2 = new CheckDataModel();
        checkDataModel2.setName("1-4");
        checkDataModel2.setValue(">1.1mF/3.7~4.5mF");
        checkDataModel2.setIn("");
        checkDataModel2.setOut("");
        checkDataModelList.add(checkDataModel2);

        CheckDataModel checkDataModel3 = new CheckDataModel();
        checkDataModel3.setName("1-5");
        checkDataModel3.setValue("Open/OL");
        checkDataModel3.setIn("");
        checkDataModel3.setOut("");
        checkDataModelList.add(checkDataModel3);

        CheckDataModel checkDataModel4 = new CheckDataModel();
        checkDataModel4.setName("1-6");
        checkDataModel4.setValue("31.5KΩ/100KΩ");
        checkDataModel4.setIn("");
        checkDataModel4.setOut("");
        checkDataModelList.add(checkDataModel4);

        CheckDataModel checkDataModel5 = new CheckDataModel();
        checkDataModel5.setName("1-7");
        checkDataModel5.setValue("20.4KΩ/420~475KΩ");
        checkDataModel5.setIn("");
        checkDataModel5.setOut("");
        checkDataModelList.add(checkDataModel5);

        CheckDataModel checkDataModel6 = new CheckDataModel();
        checkDataModel6.setName("1-8 (Gamma Installed)");
        checkDataModel6.setValue(">100KΩ");
        checkDataModel6.setIn("");
        checkDataModel6.setOut("");
        checkDataModelList.add(checkDataModel6);

        CheckDataModel checkDataModel7 = new CheckDataModel();
        checkDataModel7.setName("1-9");
        checkDataModel7.setValue("OL/OL");
        checkDataModel7.setIn("");
        checkDataModel7.setOut("");
        checkDataModelList.add(checkDataModel7);

        CheckDataModel checkDataModel8 = new CheckDataModel();
        checkDataModel8.setName("1-10");
        checkDataModel8.setValue("OL//420~475KΩ");
        checkDataModel8.setIn("");
        checkDataModel8.setOut("");
        checkDataModelList.add(checkDataModel7);

        CheckDataModel checkDataModel9 = new CheckDataModel();
        checkDataModel9.setName("6-7");
        checkDataModel9.setValue("51.9KΩ");
        checkDataModel9.setIn("");
        checkDataModel9.setOut("");
        checkDataModelList.add(checkDataModel9);

        CheckDataModel checkDataModel10 = new CheckDataModel();
        checkDataModel10.setName("Case to All");
        checkDataModel10.setValue("Open");
        checkDataModel10.setIn("");
        checkDataModel10.setOut("");
        checkDataModelList.add(checkDataModel10);

        return checkDataModelList;
    }

    private List<CheckDataModel> motorRingOutListInit() {
        List<CheckDataModel> checkDataModelList = new ArrayList<>();

        CheckDataModel checkDataModel0 = new CheckDataModel();
        checkDataModel0.setName("B1-R8");
        checkDataModel0.setValue("550K");
        checkDataModel0.setIn("");
        checkDataModel0.setOut("");
        checkDataModelList.add(checkDataModel0);

        CheckDataModel checkDataModel1 = new CheckDataModel();
        checkDataModel1.setName("B2-R3");
        checkDataModel1.setValue("1.7Ω (High Torque)\n3.1Ω (Regular)");
        checkDataModel1.setIn("");
        checkDataModel1.setOut("");
        checkDataModelList.add(checkDataModel1);

        CheckDataModel checkDataModel2 = new CheckDataModel();
        checkDataModel2.setName("B2-R4");
        checkDataModel2.setValue("1.7Ω (High Torque)\n3.1Ω (Regular)");
        checkDataModel2.setIn("");
        checkDataModel2.setOut("");
        checkDataModelList.add(checkDataModel2);

        CheckDataModel checkDataModel3 = new CheckDataModel();
        checkDataModel3.setName("B3-R4");
        checkDataModel3.setValue("1.7Ω (High Torque)\n3.1Ω (Regular)");
        checkDataModel3.setIn("");
        checkDataModel3.setOut("");
        checkDataModelList.add(checkDataModel3);

        CheckDataModel checkDataModel4 = new CheckDataModel();
        checkDataModel4.setName("B5-R8");
        checkDataModel4.setValue("1.07M");
        checkDataModel4.setIn("");
        checkDataModel4.setOut("");
        checkDataModelList.add(checkDataModel4);

        CheckDataModel checkDataModel5 = new CheckDataModel();
        checkDataModel5.setName("B6-R8");
        checkDataModel5.setValue("1.07M");
        checkDataModel5.setIn("");
        checkDataModel5.setOut("");
        checkDataModelList.add(checkDataModel5);

        CheckDataModel checkDataModel6 = new CheckDataModel();
        checkDataModel6.setName("B7-R8");
        checkDataModel6.setValue("1.07M");
        checkDataModel6.setIn("");
        checkDataModel6.setOut("");
        checkDataModelList.add(checkDataModel6);

        CheckDataModel checkDataModel7 = new CheckDataModel();
        checkDataModel7.setName("R1-B5");
        checkDataModel7.setValue("400K-500K");
        checkDataModel7.setIn("");
        checkDataModel7.setOut("");
        checkDataModelList.add(checkDataModel7);

        CheckDataModel checkDataModel8 = new CheckDataModel();
        checkDataModel8.setName("R1-B6");
        checkDataModel8.setValue("400K-500K");
        checkDataModel8.setIn("");
        checkDataModel8.setOut("");
        checkDataModelList.add(checkDataModel7);

        CheckDataModel checkDataModel9 = new CheckDataModel();
        checkDataModel9.setName("R1-B7");
        checkDataModel9.setValue("400K-500K");
        checkDataModel9.setIn("");
        checkDataModel9.setOut("");
        checkDataModelList.add(checkDataModel9);

        CheckDataModel checkDataModel10 = new CheckDataModel();
        checkDataModel10.setName("R1-B8");
        checkDataModel10.setValue("400K-500K");
        checkDataModel10.setIn("");
        checkDataModel10.setOut("");
        checkDataModelList.add(checkDataModel10);

        CheckDataModel checkDataModel11 = new CheckDataModel();
        checkDataModel11.setName("All Else");
        checkDataModel11.setValue("OL");
        checkDataModel11.setIn("");
        checkDataModel11.setOut("");
        checkDataModelList.add(checkDataModel11);

        CheckDataModel checkDataModel12 = new CheckDataModel();
        checkDataModel12.setName("Case to All");
        checkDataModel12.setValue("Open");
        checkDataModel12.setIn("");
        checkDataModel12.setOut("");
        checkDataModelList.add(checkDataModel12);

        return checkDataModelList;
    }

    private List<CheckDataModel> heatTestListInit() {
        List<CheckDataModel> checkDataModelList = new ArrayList<>();

        CheckDataModel checkDataMode0 = new CheckDataModel();
        checkDataMode0.setName("Max temp");
        checkDataMode0.setValue("");
        checkDataModelList.add(checkDataMode0);

        CheckDataModel checkDataMode1 = new CheckDataModel();
        checkDataMode1.setName("Pass(Yes/No)");
        checkDataMode1.setValue("");
        checkDataModelList.add(checkDataMode1);

        return checkDataModelList;
    }

    private List<CheckDataModel> functionTestListInit() {
        List<CheckDataModel> checkDataModelList = new ArrayList<>();

        CheckDataModel checkDataModel0 = new CheckDataModel();
        checkDataModel0.setName("Pass(Yes/No)");
        checkDataModel0.setValue("");
        checkDataModelList.add(checkDataModel0);

        CheckDataModel checkDataModel1 = new CheckDataModel();
        checkDataModel1.setName("Screenshot(Yes/No)");
        checkDataModel1.setValue("");
        checkDataModelList.add(checkDataModel1);

        return checkDataModelList;
    }
}
