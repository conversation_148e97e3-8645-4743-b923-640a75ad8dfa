package net.tartan.platform.integration.entity.mwdCert;

import lombok.*;
import net.tartan.platform.common.constant.PfConstant;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 脉冲器维护报告（合格证）
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = PfConstant.MWD_CERT_REPORT)
public class MwdPulserInfo extends MwdCertBaseInfo {


    /**
     * 出厂序列状态
     * 生产序列显示为Pd,风险序列显示为Ri，试验序列显示为Te
     */
    private String riskType;

    /**
     * 外观检查
     */
    private String surfaceCheck = "合格";

    /**
     * 功能测试
     */
    private String functionCheck = "合格";

    /**
     * 电子部件检查
     */
    private String electronicPartsCheck = "合格";

    /**
     * 充油试压测试
     */
    private String oilPressureTest = "合格";

    /**
     * Ringout测试
     */
    private String ringoutCheck = "合格";

    /**
     * Gamma测试
     */
    private String gammaTest = "合格";

    /**
     * 检测结果
     */
    private String checkResult = "仪器按照达坦脉冲检查测试规范《TSC/JM-MWD-Pulser-SWI-003-1.0》进行出厂检测并通过。";


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
    }
}
