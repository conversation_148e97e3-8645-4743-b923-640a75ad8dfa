package net.tartan.platform.integration.beans.vo;

import lombok.Data;
import net.tartan.platform.common.enums.EnumComponentTestWarnType;
import net.tartan.platform.common.enums.EnumRiskType;
import net.tartan.platform.common.enums.EnumServiceStatus;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-05-09
 */
@Data
public class ComponentTestVo {

    private Long componentTestId;

    /**
     * 品名
     */
    private String invName;

    /**
     * 序列号
     */
    private String serialNumber;
    /**
     * 自带序列号序列号
     */
    private String merSerialNumber;
    /**
     * 引进品牌
     */
    private String importBrand;
    /**
     * 试用件从开始服役-》服役中（继续服役）-》结束服役为一个周期
     * 每个服役周期的母件序列号相同
     * *
     * 服役状态，如果序列号为空，表示新增，此时是开始服役
     * 如果前端给了服役标示，则按照前端给的标志标记服役状态，一般是结束服役
     * 如果前端没有给服役状态，且序列号不为空，则表示继续服役。
     * *
     * 如果是继续服役的状态，那么母件的序列号需要与库中的保持一致，否则需要报错
     */
    private EnumServiceStatus serviceStatus;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 母件品名
     */
    private String parentInvName;

    /**
     * 母件序列号，erp的批次号
     */
    private String parentSerialNumber;

    /**
     * 试用过程及结果
     */
    private String description;
    /**
     * 提醒类型
     */
    private EnumComponentTestWarnType warnType;
    /**
     * 额定时长
     */
    private BigDecimal standardHour;
    /**
     * 额定趟次
     */
    private Integer standardRun;
    /**
     * 使用过程
     */
    private String testProcess;
    /**
     * 总趟次
     */
    private Integer totalRun;
    /**
     * 总工作时长
     */
    private BigDecimal totalHour;
    /**
     * 最高温度
     */
    private BigDecimal maxBht;
    /**
     * 是否达到提醒界限
     */
    private boolean warn;
    /**
     * 测试目的
     */
    private String testPurpose;
    /**
     * 需现场测试验证的点
     */
    private String testContent;
    /**
     * 试用计划安排
     */
    private String testPlan;
    /**
     * 试用结论
     * 0：未通过
     * 1：通过
     */
    private Integer testResult;
    /**
     * 试用结论描述
     */
    private String testResultDesc;

    private List<ComponentTestServiceHistoryVo> componentTestServiceHistoryList;

}
