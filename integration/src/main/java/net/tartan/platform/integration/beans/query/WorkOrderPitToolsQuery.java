package net.tartan.platform.integration.beans.query;

import lombok.Data;
import net.tartan.platform.common.enums.EnumPitToolsWorkOrderType;

/**
 * <p>
 * 井下工具工单信息表
 * </p>
 *
 */
@Data
public class WorkOrderPitToolsQuery {

    private Long pitToolsId;

    /**
     * 工单单号
     */
    private String pitToolsNumber;

    /**
     * 序列号
     */
    private String serialNumber;

    /**
     * 仪器名称
     */
    private String invName;

    /**
     * 井号
     */
    private String wellNumber;

    /**
     * 开始日期
     */
    private String startTime;

    /**
     * 完成日期
     */
    private String endTime;
    /**
     * 接收日期
     */
    private String receiveDate;
    /**
     * 作业号
     */
    private String jobNumber;

    /**
     * 仪器类型（前端按照字典里面的来，传的是字典的id）
     */
    private Long deviceType;

    /**
     * 1：已完成，0：未完成
     */
    private Integer finish;
    /**
     * 维修状态1：已完成，0：未完成，-1：滞留
     */
    private Integer status;

    /**
     * 工单类型： 组装 ASSEMBLE, 拆卸 DISASSEMBLE
     */
    private String workOrderType;

    // 1 - pit_tools_assemble_record
    // 2 - pit_tools_disassemble_record
    // 3 - pit_tools_screw_usage_data_list
//    private Integer pitToolsExportType;
}
