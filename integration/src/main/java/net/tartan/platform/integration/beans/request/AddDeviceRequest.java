//package net.tartan.platform.integration.beans.request;
//
//import lombok.AllArgsConstructor;
//import lombok.Builder;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//
//import javax.validation.constraints.NotEmpty;
//import java.util.List;
//
//@Data
//@Builder
//@NoArgsConstructor
//@AllArgsConstructor
//public class AddDeviceRequest {
//
//    /**
//     * 序列号
//     */
//    @NotEmpty
//    private String serialNumber;
//    /**
//     * 设备类型，1-KITBOXES、2-ATBITSUBS、3-PULSER、4-DM、5-RECEIVER、6-GAMMANAT、7-GAMMAAZI、8-BATTERY、9-GAPSUB、10-NMDP、11-RFD、12-TRANSDUCER、13-PONYSUB、14-FLOWSIMU、15-DEPTRACK
//     */
//    @NotEmpty
//    private String deviceType;
//
//    /**
//     * 商务目标量
//     */
//    private String targetQuantity;
//
//    /**
//     * 状态：1-ATRIG、2-REPAIR、3-OVERSEAS、4-SCRAP、5-JUNKED、6-HANDOVERTOCLIENT、7-READY、8-SENTBACKCANADA、9-NEW
//     */
//    private String status;
//
//    /**
//     * 接受日期
//     */
//    private String receivedDate;
//
//    /**
//     * 预计完成日期
//     */
//    private String completionDate;
//
//    /**
//     * 仪器配置数量
//     */
//    private Integer instrumentQuantity;
//
//    /**
//     * 检查结果
//     */
//    private String inspectionResult;
//
//    /**
//     * 处理进度:维护中、待客户确认是否维修
//     */
//    private String processProgress;
//
//    /**
//     * 地点
//     */
//    private String location;
//
//    /**
//     * 所属
//     */
//    private String owner;
//
//    /**
//     * 备注
//     */
//    private String notes;
//
//    /**
//     * 如果是kit，可能会上传kit下的设备列表
//     */
//    private List<Long> boxDeviceIdList;
//}
