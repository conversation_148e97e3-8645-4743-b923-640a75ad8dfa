package net.tartan.platform.integration.service.impl;

import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import net.tartan.platform.common.enums.EnumConsolidatedFailureType;
import net.tartan.platform.common.enums.EnumMwdDeviceType;
import net.tartan.platform.integration.beans.vo.WellAnalysisInstrumentsVO;
import net.tartan.platform.integration.beans.vo.WellOperationAnalysisDetailBasicVO;
import net.tartan.platform.integration.beans.vo.WellOperationAnalysisDetailRichContentVO;
import net.tartan.platform.integration.beans.vo.WellOperationAnalysisDetailVO;
import net.tartan.platform.integration.beans.vo.WellSummaryVO;
import net.tartan.platform.integration.entity.Component;
import net.tartan.platform.integration.entity.Device;
import net.tartan.platform.integration.entity.JobInfo;
import net.tartan.platform.integration.entity.MemberInfo;
import net.tartan.platform.integration.entity.MwdFailureShopInspection;
import net.tartan.platform.integration.entity.UserInfo;
import net.tartan.platform.integration.entity.WellAnalysisInstruments;
import net.tartan.platform.integration.entity.WellInfo;
import net.tartan.platform.integration.entity.WellOperationAnalysisDetail;
import net.tartan.platform.integration.entity.dailyreport.MwdCoverReport;
import net.tartan.platform.integration.entity.dailyreport.MwdFailureReport;
import net.tartan.platform.integration.entity.dailyreport.MwdRunReport;
import net.tartan.platform.integration.entity.dailyreport.MwdTool;
import net.tartan.platform.integration.entity.dailyreport.RSS.RSSCoverReport;
import net.tartan.platform.integration.entity.dailyreport.RSS.RSSDailyReport;
import net.tartan.platform.integration.entity.dailyreport.RSS.RSSFailureReport;
import net.tartan.platform.integration.entity.dailyreport.RSS.RSSOos;
import net.tartan.platform.integration.entity.dailyreport.RSS.RSSRunReport;
import net.tartan.platform.integration.entity.dailyreport.RSS.RSSTool;
import net.tartan.platform.integration.mapper.ComponentMapper;
import net.tartan.platform.integration.mapper.DeviceComponentRelationMapper;
import net.tartan.platform.integration.mapper.DeviceMapper;
import net.tartan.platform.integration.mapper.WellOperationAnalysisDetailMapper;
import net.tartan.platform.integration.mongo.MwdFailureShopInspectionRepository;
import net.tartan.platform.integration.service.IJobInfoService;
import net.tartan.platform.integration.service.IMemberInfoService;
import net.tartan.platform.integration.service.IUserInfoService;
import net.tartan.platform.integration.service.IWellInfoService;
import net.tartan.platform.integration.service.WellAnalysisInstrumentsService;
import net.tartan.platform.integration.service.WellOperationAnalysisDetailService;

/**
 * 井作业运行及失效分析详情总表服务实现类
 */
@Service
public class WellOperationAnalysisDetailServiceImpl extends ServiceImpl<WellOperationAnalysisDetailMapper, WellOperationAnalysisDetail> implements WellOperationAnalysisDetailService {

    private static final Logger log = LoggerFactory.getLogger(WellOperationAnalysisDetailServiceImpl.class);

    // 井类别常量
    private static final String WELL_CATEGORY_AT_BIT = "近钻井";
    private static final String WELL_CATEGORY_NON_AT_BIT = "非近钻井";
    private static final String WELL_CATEGORY_RSS = "旋转导向";

    // 工具类型关键字
    private static final List<String> AT_BIT_KEYWORDS = Arrays.asList("近钻头");
    private static final List<String> AT_BIT_EC_KEYWORDS = Arrays.asList("At Bit EC");
    private static final List<String> RECEIVER_KEYWORDS = Arrays.asList("接收器");
    private static final List<String> AT_BIT_GAMMA_KEYWORDS = Arrays.asList("At Bit Gamma");
    private static final List<String> INSULATION_SUB_KEYWORDS = Arrays.asList("绝缘短节", "绝缘短接");

    private static final List<String> PULSER_KEYWORDS = Arrays.asList("脉冲");
    private static final List<String> BOTTOM_END_KEYWORDS = Arrays.asList("底部");
    private static final List<String> DM_KEYWORDS = Arrays.asList("探管", "DM");
    private static final List<String> AZ_DM_KEYWORDS = Arrays.asList("方位探管", "AZ_DM");
    private static final List<String> GAMMA_KEYWORDS = Arrays.asList("自然GAMMA", "自然Gamma");
    private static final List<String> AZ_GAMMA_KEYWORDS = Arrays.asList("方位GAMMA", "方位Gamma");

    private static final List<String> RSS_SU_KEYWORDS = Arrays.asList("SU", "LBSU");
    private static final List<String> RSS_MWD_KEYWORDS = Arrays.asList("MWD", "LBMWD");
    private static final List<String> RSS_LCP_KEYWORDS = Arrays.asList("LCP", "LBLCP");

    /**
     * 组件关系Map，key为设备序列号，value为相关组件列表
     */
    private Map<String, List<String>> componentRelationMap = new ConcurrentHashMap<>();

    /**
     * 设备信息Map，key为设备序列号，value为设备对象
     */
    private Map<String, DeviceInfo> deviceInfoMap = new ConcurrentHashMap<>();

    /**
     * 近钻头组件缓存Map，key为近钻头序列号，value为关联组件列表
     */
    private Map<String, List<Component>> atBitComponentCache = new ConcurrentHashMap<>();

    private final IJobInfoService jobInfoService;
    private final IWellInfoService wellInfoService;
    private final MongoTemplate mongoTemplate;
    private final WellAnalysisInstrumentsService wellAnalysisInstrumentsService;
    private final IUserInfoService userInfoService;
    private final IMemberInfoService memberInfoService;
    private final MwdFailureShopInspectionRepository mwdFailureShopInspectionRepository;

    @Autowired
    private DeviceMapper deviceMapper;

    @Autowired
    private DeviceComponentRelationMapper deviceComponentRelationMapper;

    @Autowired
    private ComponentMapper componentMapper;

    @Autowired
    public WellOperationAnalysisDetailServiceImpl(IJobInfoService jobInfoService,
                                                IWellInfoService wellInfoService,
                                                MongoTemplate mongoTemplate,
                                                WellAnalysisInstrumentsService wellAnalysisInstrumentsService,
                                                IUserInfoService userInfoService,
                                                IMemberInfoService memberInfoService,
                                                MwdFailureShopInspectionRepository mwdFailureShopInspectionRepository) {
        this.jobInfoService = jobInfoService;
        this.wellInfoService = wellInfoService;
        this.mongoTemplate = mongoTemplate;
        this.wellAnalysisInstrumentsService = wellAnalysisInstrumentsService;
        this.userInfoService = userInfoService;
        this.memberInfoService = memberInfoService;
        this.mwdFailureShopInspectionRepository = mwdFailureShopInspectionRepository;
    }

    /**
     * 初始化方法，在服务启动时调用
     */
    @PostConstruct
    public void init() {
        // 初始化时不加载所有数据，采用懒加载方式
        componentRelationMap = new ConcurrentHashMap<>();
    }

    /**
     * 根据工具列表判断井类别
     *
     * @param mwdRunReport MWD运行报告，可能为null
     * @param rssRunReport RSS运行报告，可能为null
     * @return 井类别：手动近钻井、非近钻井或旋转导向
     */
    private String getWellCategory(MwdRunReport mwdRunReport, RSSRunReport rssRunReport) {
        // 获取工具列表
        List<?> toolListRaw = (mwdRunReport != null && mwdRunReport.getToolList() != null) ? mwdRunReport.getToolList() :
                              ((rssRunReport != null && rssRunReport.getToolList() != null) ? rssRunReport.getToolList() : Collections.emptyList());

        if (CollectionUtils.isEmpty(toolListRaw)) {
            return WELL_CATEGORY_NON_AT_BIT; // 默认为非近钻井
        }

        // 检查是否包含RSS工具
        boolean hasRssTool = false;
        // 检查是否包含近钻头工具
        boolean hasAtBitTool = false;
        // 检查是否包含非近钻工具
        boolean hasNonAtBitTool = false;

        for (Object toolRaw : toolListRaw) {
            if (toolRaw == null) continue;

            String invName = null;
            String serialNumber = null;

            if (toolRaw instanceof MwdTool) {
                MwdTool mTool = (MwdTool) toolRaw;
                invName = mTool.getInvName();
                serialNumber = mTool.getSerialNumber();
            } else if (toolRaw instanceof RSSTool) {
                RSSTool rTool = (RSSTool) toolRaw;
                invName = rTool.getInvName();
                serialNumber = rTool.getSerialNumber();
            }

            if (!StringUtils.hasText(invName)) continue;

            // 检查是否是RSS工具 - 使用包含匹配但更具体的关键字
            if (containsAnyKeyword(invName, RSS_SU_KEYWORDS) ||
                containsAnyKeyword(invName, RSS_MWD_KEYWORDS) ||
                containsAnyKeyword(invName, RSS_LCP_KEYWORDS)) {
                hasRssTool = true;
            }

            // 检查是否是近钻头工具 - 使用精确匹配
            if (equalsAnyKeyword(invName, AT_BIT_KEYWORDS)) {
                hasAtBitTool = true;

                // 如果有近钻头工具，查询其关联的近钻Gamma和At bit EC
                if (StringUtils.hasText(serialNumber)) {
                    List<String> relatedComponents = getRelatedComponents(serialNumber);

                    // 检查关联组件中是否包含近钻Gamma或At bit EC
                    for (String component : relatedComponents) {
                        if (containsAnyKeyword(component, AT_BIT_GAMMA_KEYWORDS) ||
                            containsAnyKeyword(component, AT_BIT_EC_KEYWORDS)) {
                            hasAtBitTool = true;
                        }
                    }
                }
            }

            // 检查是否是非近钻工具
            if (containsAnyKeyword(invName, PULSER_KEYWORDS) ||
                containsAnyKeyword(invName, BOTTOM_END_KEYWORDS) ||
                containsAnyKeyword(invName, DM_KEYWORDS) ||
                containsAnyKeyword(invName, AZ_DM_KEYWORDS) ||
                containsAnyKeyword(invName, GAMMA_KEYWORDS) ||
                containsAnyKeyword(invName, AZ_GAMMA_KEYWORDS) ||
                containsAnyKeyword(invName, INSULATION_SUB_KEYWORDS)) {
                hasNonAtBitTool = true;
            }
        }

        // 根据工具类型判断井类别
        if (hasRssTool) {
            return WELL_CATEGORY_RSS;
        } else if (hasAtBitTool) {
            return WELL_CATEGORY_AT_BIT;
        } else if (hasNonAtBitTool) {
            return WELL_CATEGORY_NON_AT_BIT;
        } else {
            return WELL_CATEGORY_NON_AT_BIT; // 默认为非近钻井
        }
    }

    /**
     * 检查字符串是否包含任何关键字
     *
     * @param str 要检查的字符串
     * @param keywords 关键字列表
     * @return 如果包含任何关键字，则返回true；否则返回false
     */
    private boolean containsAnyKeyword(String str, List<String> keywords) {
        if (!StringUtils.hasText(str)) return false;

        String upperStr = str.toUpperCase();
        for (String keyword : keywords) {
            if (upperStr.contains(keyword.toUpperCase())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查字符串是否等于任何关键字
     *
     * @param str 要检查的字符串
     * @param keywords 关键字列表
     * @return 如果等于任何关键字，则返回true；否则返回false
     */
    private boolean equalsAnyKeyword(String str, List<String> keywords) {
        if (!StringUtils.hasText(str)) return false;

        for (String keyword : keywords) {
            if (str.equalsIgnoreCase(keyword)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断组件是否是相关组件（At Bit EC、接收器、At Bit Gamma或绝缘短节）
     *
     * @param invName 组件的invName
     * @return 如果是相关组件则返回true，否则返回false
     */
    private boolean isRelevantComponent(String invName) {
        return StringUtils.hasText(invName) && (
            containsAnyKeyword(invName, AT_BIT_EC_KEYWORDS) ||
            containsAnyKeyword(invName, RECEIVER_KEYWORDS) ||
            containsAnyKeyword(invName, AT_BIT_GAMMA_KEYWORDS) ||
            containsAnyKeyword(invName, INSULATION_SUB_KEYWORDS)
        );
    }

    /**
     * 获取与近钻头相关的组件
     *
     * @param serialNumber 近钻头序列号
     * @return 相关组件列表
     */
    private List<String> getRelatedComponents(String serialNumber) {
        // 如果已经查询过，直接返回缓存结果
        if (componentRelationMap.containsKey(serialNumber)) {
            return componentRelationMap.get(serialNumber);
        }

        List<String> relatedComponents = new ArrayList<>();

        try {
            // 1. 查询device表，获取device_id
            Device device = deviceMapper.selectOne(
                Wrappers.lambdaQuery(Device.class)
                    .eq(Device::getSerialNumber, serialNumber)
                    .last("LIMIT 1")
            );

            if (device == null || device.getDeviceId() == null) {
                log.warn("未找到序列号为 {} 的设备", serialNumber);
                return relatedComponents;
            }

            // 2. 查询device_component_relation表，获取与近钻头相关的component_id列表
            List<Long> componentIds = deviceComponentRelationMapper.getComponentIdListByDeviceId(device.getDeviceId());
            if (CollectionUtils.isEmpty(componentIds)) {
                log.warn("设备ID {} 没有关联的组件", device.getDeviceId());
                return relatedComponents;
            }

            // 3. 查询component表，获取对应的名称和序列号
            List<Component> components = componentMapper.selectList(
                Wrappers.lambdaQuery(Component.class)
                    .in(Component::getComponentId, componentIds)
            );

            if (!CollectionUtils.isEmpty(components)) {
                for (Component component : components) {
                    String invName = component.getInvName();
                    if (StringUtils.hasText(invName)) {
                        // 添加组件名称到结果列表
                        relatedComponents.add(invName);

                        // 特别检查是否是近钻Gamma、At bit EC、接收器或绝缘短节
                        if (containsAnyKeyword(invName, AT_BIT_GAMMA_KEYWORDS)) {
                            log.info("找到近钻Gamma组件: {}", invName);
                        } else if (containsAnyKeyword(invName, AT_BIT_EC_KEYWORDS)) {
                            log.info("找到At bit EC组件: {}", invName);
                        } else if (containsAnyKeyword(invName, RECEIVER_KEYWORDS)) {
                            log.info("找到接收器组件: {}", invName);
                        } else if (containsAnyKeyword(invName, INSULATION_SUB_KEYWORDS)) {
                            log.info("找到绝缘短节组件: {}", invName);
                        }
                    }
                }
            } else {
                log.warn("未找到组件ID列表 {} 对应的组件", componentIds);
            }
        } catch (Exception e) {
            log.error("获取与近钻头相关的组件失败：{}", e.getMessage(), e);
        }

        // 缓存结果
        componentRelationMap.put(serialNumber, relatedComponents);

        // 记录找到的组件数量
        log.info("设备 {} 共找到 {} 个关联组件", serialNumber, relatedComponents.size());

        return relatedComponents;
    }

    /**
     * 创建或更新井作业分析详情，并关联仪器信息
     * 此方法仅供内部使用，不再作为公共API暴露
     *
     * @param wellNumberInput 井号
     * @param runInput 趟次
     * @param skipExistingCheck 是否跳过已存在的记录检查。如果为true，则已经同步过的记录将不再被处理。createTime和updateTime相同的数据会更新
     * @return 创建或更新后的井作业分析详情对象，如果处理失败则返回null
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public WellOperationAnalysisDetail createOrUpdateAnalysisDetailWithInstruments(String wellNumberInput, Integer runInput, boolean skipExistingCheck) {
        // 1. 参数校验：井号和趟次不能为空
        // Validates that wellNumberInput and runInput are provided. Logs an error and returns null if validation fails.
        if (!StringUtils.hasText(wellNumberInput) || runInput == null) {
            log.error("创建或更新分析详情失败：井号和趟次不能为空。井号: {}, 趟次: {}", wellNumberInput, runInput);
            return null;
        }

        // 2. 获取井基础信息 (WellInfo) 从MySQL
        // Fetches WellInfo entity from the database using wellNumberInput.
        // Uses a lambda query with a limit of 1 to ensure a unique result.
        // If no WellInfo is found, logs an error and returns null.
        WellInfo wellInfo = wellInfoService.getOne(
                Wrappers.lambdaQuery(WellInfo.class)
                        .eq(WellInfo::getWellNumber, wellNumberInput)
                        .last("LIMIT 1"), // 确保获取唯一或最新的（如果有多条同名井，业务上应避免）
                false // 不抛出异常，如果找不到则返回null，后续处理
        );

        if (wellInfo == null) {
            log.error("创建或更新分析详情失败：通过井号 {} 未找到井基础信息", wellNumberInput);
            return null;
        }

        // 3. 获取最新的作业信息 (JobInfo) 从MySQL，根据井ID关联
        // Fetches the latest JobInfo entity associated with the fetched WellInfo's ID.
        // Orders by creation time descending and limits to 1.
        // If no JobInfo is found, logs an error and returns null.
        List<JobInfo> jobs = jobInfoService.list(
            Wrappers.lambdaQuery(JobInfo.class)
                .eq(JobInfo::getWellId, wellInfo.getWellId())
                .orderByDesc(JobInfo::getCreateTime) // 获取最新的作业信息
                .last("LIMIT 1")
        );
        JobInfo jobInfo = jobs.isEmpty() ? null : jobs.get(0);

        if (jobInfo == null) {
            log.error("创建或更新分析详情失败：井号 {} (Well ID: {}) 未找到对应的作业信息", wellNumberInput, wellInfo.getWellId());
            return null;
        }
        Long jobInfoId = jobInfo.getId(); // 后续查询MongoDB报告使用 // JobInfo ID is extracted for querying MongoDB.

        // 4. 从MongoDB获取运行报告 (MwdRunReport 或 RSSRunReport)
        // Attempts to fetch MwdRunReport from MongoDB based on jobId and run number.
        // If MwdRunReport is not found, it attempts to fetch RSSRunReport.
        // Logs a warning and returns null if neither report is found, as core data would be missing.
        // 添加排序以获取最新的记录（按_id降序，因为ObjectId包含时间戳）
        MwdRunReport mwdRunReport = mongoTemplate.findOne(
            Query.query(Criteria.where("jobId").is(jobInfoId).and("run").is(runInput))
                .with(Sort.by(Sort.Direction.DESC, "_id")),
            MwdRunReport.class);
        MwdCoverReport mwdCoverReport = mongoTemplate.findOne(Query.query(Criteria.where("jobId").is(jobInfoId)), MwdCoverReport.class);
        RSSRunReport rssRunReport = (mwdRunReport == null) ? mongoTemplate.findOne(
            Query.query(Criteria.where("jobId").is(jobInfoId).and("run").is(runInput))
                .with(Sort.by(Sort.Direction.DESC, "_id")),
            RSSRunReport.class) : null;
        RSSCoverReport rssCoverReport = mongoTemplate.findOne(Query.query(Criteria.where("jobId").is(jobInfoId)), RSSCoverReport.class);
        log.info("创建或更新分析详情：井号: {}, 找到RSSCoverReport: {}", wellNumberInput, rssCoverReport != null);

        if (mwdRunReport == null && rssRunReport == null) {
            log.warn("创建或更新分析详情警告：未找到作业ID: {}, 趟次: {} 的运行报告 (MWD或RSS)。无法进行数据填充。", jobInfoId, runInput);
            return null;
        }

        // 5. 从MongoDB获取相关的辅助报告：故障报告 (MwdFailureReport、RSSFailureReport) 和最新的RSS日报 (RSSDailyReport)
        // 获取故障报告和最新的日报，添加排序以获取最新的记录
        MwdFailureReport mwdFailureReport = mongoTemplate.findOne(
            Query.query(Criteria.where("jobId").is(jobInfoId).and("run").is(runInput))
                .with(Sort.by(Sort.Direction.DESC, "_id")),
            MwdFailureReport.class
        );
        // 获取RSS失效报告
        RSSFailureReport rssFailureReport = mongoTemplate.findOne(
            Query.query(Criteria.where("jobId").is(jobInfoId).and("run").is(runInput))
                .with(Sort.by(Sort.Direction.DESC, "_id")),
            RSSFailureReport.class
        );
        RSSDailyReport latestRssDailyReport = mongoTemplate.findOne(
            Query.query(Criteria.where("jobId").is(jobInfoId).and("run").is(runInput))
                .with(Sort.by(Sort.Direction.DESC, "date")),
            RSSDailyReport.class
        );

        // 6. 初始化 WellOperationAnalysisDetail 实体，并调用私有方法从各数据源填充数据
        // Initializes a new WellOperationAnalysisDetail entity.
        // Calls the populateDetailFromSources method to fill the entity with data from the fetched sources.
        WellOperationAnalysisDetail detail = new WellOperationAnalysisDetail();
        populateDetailFromSources(detail, jobInfo, wellInfo, mwdRunReport, mwdCoverReport, rssRunReport, rssCoverReport, mwdFailureReport, rssFailureReport, latestRssDailyReport, runInput);

        // 7. 检查记录是否已存在于MySQL中，以决定是更新还是插入
        // Checks if a WellOperationAnalysisDetail record already exists in MySQL for the given well number and run.
        WellOperationAnalysisDetail existingDetail = baseMapper.findByWellNumberAndRun(wellNumberInput, runInput);

        if (existingDetail != null) {
            // 记录已存在，执行更新操作
            // If the record exists, its ID is set to the new detail entity for update.
            // Updater information and update time should be set here (currently placeholder TODOs).
            // Calls a custom update method from the mapper.
            detail.setId(existingDetail.getId());
            // TODO: 应从当前用户上下文获取并设置更新人信息 (updaterId, updaterName)
            // detail.setUpdaterId(currentUser.getId());
            // detail.setUpdaterName(currentUser.getName());

            // 同时更新createTime和updateTime，保持它们一致，这样下次同步时系统会认为这条记录仍然可以被更新
            detail.setCreateTime(existingDetail.getCreateTime()); // 之前的创建时间就不会变了
            detail.setUpdateTime(existingDetail.getCreateTime());

            baseMapper.updateDetail(detail);

            // 更新关联仪器信息：先删除旧的，再插入新的 (简化处理，也可进行差异比较更新)
            // Associated WellAnalysisInstruments are removed and will be re-inserted later.
            wellAnalysisInstrumentsService.remove(Wrappers.lambdaQuery(WellAnalysisInstruments.class)
                    .eq(WellAnalysisInstruments::getAnalysisDetailIdFk, existingDetail.getId()));
        } else {
            // 记录不存在，执行插入操作
            // If the record does not exist, creator information and creation/update times should be set (currently placeholder TODOs).
            // Calls a custom save method from the mapper (expected to return the generated ID).
            // TODO: 应从当前用户上下文获取并设置创建人信息 (creatorId, creatorName)
            // detail.setCreatorId(currentUser.getId());
            // detail.setCreatorName(currentUser.getName());
            detail.setCreateTime(new Date());
            detail.setUpdateTime(new Date());
            baseMapper.saveDetail(detail);
        }
        Long detailId = detail.getId(); // ID应由saveDetail方法通过keyProperty回填，或在update后保持不变

        // 8. ID回填校验：确保在保存操作后detail对象拥有ID，以便关联仪器
        // Verifies that the detail entity has an ID after save/update, as it's needed for associating instruments.
        // If the ID is null (e.g., saveDetail didn't return it directly or was an update), it re-fetches the record.
        // Logs an error and returns null if the ID cannot be obtained, as instruments cannot be linked.
        if (detailId == null) {
            log.warn("保存或更新 WellOperationAnalysisDetail 后未能直接获取 ID，尝试重新查询。井号: {}, 趟次: {}", wellNumberInput, runInput);
            WellOperationAnalysisDetail savedOrUpdatedDetail = baseMapper.findByWellNumberAndRun(wellNumberInput, runInput);
            if (savedOrUpdatedDetail != null) {
                detailId = savedOrUpdatedDetail.getId();
                detail.setId(detailId);
                 log.info("重新查询后获取到ID: {}，井号: {}, 趟次: {}", detailId, wellNumberInput, runInput);
            } else {
                 log.error("创建或更新分析详情失败：尝试重新获取ID失败。井号: {}, 趟次: {}", wellNumberInput, runInput);
                 return null;
            }
        }

        // 9. 准备并批量保存关联的井分析仪器列表 (WellAnalysisInstruments)
        // Prepares a list of WellAnalysisInstruments based on the tool lists from MwdRunReport or RSSRunReport.
        // Saves the list of instruments in batch if it's not empty.
        // 确保detailId有效，才进行工具保存
        if (detailId != null && detailId > 0) {
            List<WellAnalysisInstruments> instrumentsToSave = prepareInstrumentsList(detailId, mwdRunReport, rssRunReport);
            if (!CollectionUtils.isEmpty(instrumentsToSave)) {
                wellAnalysisInstrumentsService.saveBatch(instrumentsToSave);
            }
        } else {
            log.error("无法保存工具记录：detailId无效。井号: {}, 趟次: {}", wellNumberInput, runInput);
        }

        log.info("成功创建/更新井作业分析详情，ID: {}, 井号: {}, 趟次: {}", detailId, wellNumberInput, runInput);
        return detail;
    }

    /**
     * 从各个数据源填充 {@link WellOperationAnalysisDetail} 实体。
     *
     * @param detail 要填充的实体对象
     * @param jobInfo 作业信息 (MySQL)
     * @param wellInfo 井基础信息 (MySQL)
     * @param mwdRunReport MWD运行报告 (MongoDB), 可能为null
     * @param rssRunReport RSS运行报告 (MongoDB), 可能为null (当mwdRunReport不为null时，此参数通常也为null)
     * @param rssCoverReport RSS封面报告 (MongoDB), 可能为null
     * @param mwdFailureReport MWD故障报告 (MongoDB), 可能为null
     * @param rssFailureReport RSS失效报告 (MongoDB), 可能为null
     * @param latestRssDailyReport 最新的RSS日报 (MongoDB), 可能为null
     * @param runInput 当前处理的趟次号
     */
    private void populateDetailFromSources(WellOperationAnalysisDetail detail, JobInfo jobInfo, WellInfo wellInfo,
                                           MwdRunReport mwdRunReport, MwdCoverReport mwdCoverReport, RSSRunReport rssRunReport, RSSCoverReport rssCoverReport,
                                           MwdFailureReport mwdFailureReport, RSSFailureReport rssFailureReport, RSSDailyReport latestRssDailyReport,
                                           Integer runInput) {
        // --- 基础信息填充 ---
        // Set foreign keys from JobInfo and WellInfo.
        detail.setJobInfoIdFk(jobInfo.getId());
        detail.setJobNumber(jobInfo.getJobNumber());
        detail.setJobStatus(jobInfo.getJobStatus());
        detail.setWellInfoIdFk(wellInfo.getWellId());

        // 根据工具列表判断井类别
        String wellCategory = getWellCategory(mwdRunReport, rssRunReport);
        detail.setServiceWellCategory(wellCategory);
        // Set well number and block (field name in WellInfo is 'blocks').
        detail.setWellNumber(wellInfo.getWellNumber());
        detail.setRun(runInput);

        // --- 根据运行报告类型填充数据 (MWD优先) ---
        // Populate fields based on the type of run report available (MWD takes precedence).
        if (mwdRunReport != null) {
            // --- 从MWD运行报告填充 ---
            // Fields like temperature, bit size, mud properties, flow rate, depths, orifice/poppet config,
            // dates, trip-out reason, signal strength, and circulating hours are populated from MwdRunReport.
            detail.setTemperatureValue(mwdRunReport.getMaxBht() != null ? BigDecimal.valueOf(mwdRunReport.getMaxBht()) : null);
            detail.setBitSize(mwdRunReport.getHoleSize() != null ? mwdRunReport.getHoleSize().toString() : null);
            detail.setMudType(mwdRunReport.getMudType());
            detail.setMudDensity(mwdRunReport.getMudWeight());
            detail.setFlowRate(mwdRunReport.getFlowRate());
            detail.setConstructionStartDepthMd(mwdRunReport.getDepthIn());
            detail.setConstructionEndDepthMd(mwdRunReport.getDepthOut());
            detail.setOrificeConfig(mwdRunReport.getOrificeId() != null ? mwdRunReport.getOrificeId().toString() : null);
            detail.setPoppetConfig(mwdRunReport.getPopTipOd() != null ? mwdRunReport.getPopTipOd().toString() : null);
            detail.setDateIn(parseReportDate(mwdRunReport.getDateIn()));
            detail.setDateOut(parseReportDate(mwdRunReport.getDateOut()));
            detail.setTripOutReasonType(determineTripOutReason(mwdRunReport, null));
            detail.setSignalStrength(mwdRunReport.getBtmData() != null ? mwdRunReport.getBtmData().toString() : null);
            detail.setCirculatingHours(mwdRunReport.getCirculatingData());
            if (mwdRunReport.getElectricalData() != null) {
                detail.setTripInHours(mwdRunReport.getElectricalData());
            }

           

        } else if (rssRunReport != null) {
            // --- 从RSS运行报告填充 ---
            // Similar fields are populated from RSSRunReport if MwdRunReport is null.
            // Signal strength for RSS is taken from the latest RSSDailyReport if available.
            detail.setTemperatureValue(rssRunReport.getMaxBht());
            detail.setBitSize(rssRunReport.getHoleSize() != null ? rssRunReport.getHoleSize().toString() : null);
            detail.setMudType(rssRunReport.getMudType());
            detail.setMudDensity(rssRunReport.getMudDen());
            detail.setFlowRate(rssRunReport.getFlowRate());
            detail.setConstructionStartDepthMd(rssRunReport.getDepthIn());
            detail.setConstructionEndDepthMd(rssRunReport.getDepthOut());
            detail.setDateIn(parseReportDate(rssRunReport.getDateIn()));
            detail.setDateOut(parseReportDate(rssRunReport.getDateOut()));
            detail.setTripOutReasonType(determineTripOutReason(null, rssRunReport));
            // 如果RSS日报存在且有PlsH值，则设置信号强度
            if (latestRssDailyReport != null && latestRssDailyReport.getPlsH() != null) {
                 detail.setSignalStrength(latestRssDailyReport.getPlsH().toString());
                 // 如果有井下时间，则设置井下时间
            }
            // 如果rssRunReport有井下时间，则设置井下时间
            if (rssRunReport.getDhtData() != null) {
                detail.setTripInHours(rssRunReport.getDhtData());
            }
            detail.setCirculatingHours(rssRunReport.getCirculatingData());
        }

        if (mwdCoverReport != null) {
            detail.setBlock(mwdCoverReport.getField());
        }

        if (rssCoverReport != null) {
            detail.setBlock(rssCoverReport.getField());
        }

        // --- 计算衍生字段 ---
        // 计算进尺 (FootageMd)
        // Calculated as the difference between construction end depth and start depth.
        // Ensures start depth is not greater than end depth to prevent negative footage.
        if (detail.getConstructionEndDepthMd() != null && detail.getConstructionStartDepthMd() != null) {
            try {
                if (detail.getConstructionEndDepthMd().compareTo(detail.getConstructionStartDepthMd()) >= 0) {
                    detail.setFootageMd(detail.getConstructionEndDepthMd().subtract(detail.getConstructionStartDepthMd()));
                } else {
                    log.warn("计算进尺警告：结束深度 {} 小于起始深度 {}。井号: {}, 趟次: {}",
                             detail.getConstructionEndDepthMd(), detail.getConstructionStartDepthMd(), detail.getWellNumber(), detail.getRun());
                    detail.setFootageMd(BigDecimal.ZERO);
                }
            } catch (Exception e) {
                log.error("计算进尺失败: endDepth={}, startDepth={}. 井号: {}, 趟次: {}",
                          detail.getConstructionEndDepthMd(), detail.getConstructionStartDepthMd(), detail.getWellNumber(), detail.getRun(), e);
            }
        }

        // 如果没有从两个运行报告中获取到井下时间，则计算趟内总小时数 (TripInHours),
        if (detail.getTripInHours() == null && detail.getDateOut() != null && detail.getDateIn() != null) {
            if (detail.getDateOut().after(detail.getDateIn())) {
                long durationMillis = detail.getDateOut().getTime() - detail.getDateIn().getTime();
                detail.setTripInHours(BigDecimal.valueOf(TimeUnit.MILLISECONDS.toHours(durationMillis)));
                // detail.setCurrentRunUsageHours(new BigDecimal(durationMillis / (1000.0 * 60 * 60)));
            } else {
                log.warn("计算趟内时间警告：出井时间 {} 不晚于入井时间 {}。井号: {}, 趟次: {}",
                         detail.getDateOut(), detail.getDateIn(), detail.getWellNumber(), detail.getRun());
            }
        }

        // --- 处理故障相关信息 ---
        String serviceTypeFromCover = "MWD";
        boolean isTWellLinkService = "T-Well-Link".equalsIgnoreCase(serviceTypeFromCover);
        Integer mwdFailuresInCover = 0;

        boolean currentRunIsFailure = false; // Flag to indicate if a failure occurred in the current run.
        Date failureOccurTime = null; // Time of failure occurrence.

        // 判断故障类型和状态
        if (isTWellLinkService) {
            // --- T-Well-Link 服务类型的故障判断 ---
            // If service type is T-Well-Link and mwdFailuresInCover > 0, it's a T-Well-Link failure.
            if (mwdFailuresInCover > 0) {
                detail.setConsolidatedFailureType(EnumConsolidatedFailureType.RSS_FAILURE.name());
                currentRunIsFailure = true;
            } else {
                detail.setConsolidatedFailureType(EnumConsolidatedFailureType.NO_FAILURE.name());
            }
        } else if (mwdFailureReport != null) {
            // --- MWD/At-Bit 类型故障判断 (基于 MwdFailureReport) ---
            currentRunIsFailure = true;
            failureOccurTime = parseReportDate(mwdFailureReport.getFailureDate());

            boolean hasAtBitToolInRun = checkForAtBitTool(mwdRunReport, rssRunReport);
            if (hasAtBitToolInRun) {
                detail.setConsolidatedFailureType(EnumConsolidatedFailureType.AT_BIT_FAILURE.name());
            } else {
                detail.setConsolidatedFailureType(EnumConsolidatedFailureType.MWD_FAILURE.name());
            }

        } else if (rssFailureReport != null) {
            // --- RSS 类型故障判断 (基于 RSSFailureReport) ---
            currentRunIsFailure = true;
            failureOccurTime = parseReportDate(rssFailureReport.getIncidentDate());

            // RSS失效报告通常对应RSS_FAILURE类型
            detail.setConsolidatedFailureType(EnumConsolidatedFailureType.RSS_FAILURE.name());

        } else {
            // --- 无故障 ---
            // If no T-Well-Link failure and no MwdFailureReport, it's considered NO_FAILURE.
            // detail.setConsolidatedFailureType(EnumConsolidatedFailureType.NO_FAILURE.name());
            detail.setConsolidatedFailureType(null);
        }


        detail.setDownholeInstrumentStatusDesc(currentRunIsFailure ? "异常" : "正常" );
        detail.setIsFailure(currentRunIsFailure ? "是" : "否");
        detail.setFailureTime(failureOccurTime);

        // 计算距失效发生小时数 (HoursToFailure)
        // Calculated if a failure occurred, using the time difference between failureTime and DateIn.
        // Ensures failureTime is not before DateIn. Set to null if no failure or invalid times.
        if (currentRunIsFailure && detail.getFailureTime() != null && detail.getDateIn() != null) {
            if (detail.getFailureTime().getTime() >= detail.getDateIn().getTime()) {
                long durationMillisToFailure = detail.getFailureTime().getTime() - detail.getDateIn().getTime();
                detail.setHoursToFailure(BigDecimal.valueOf(TimeUnit.MILLISECONDS.toHours(durationMillisToFailure)));
            } else {
                 log.warn("计算距失效小时数警告：失效时间 {} 早于入井时间 {}。井号: {}, 趟次: {}",
                          detail.getFailureTime(), detail.getDateIn(), detail.getWellNumber(), detail.getRun());
                 detail.setHoursToFailure(null);
            }
        } else if (!currentRunIsFailure) {
            detail.setHoursToFailure(null);
        }

        // 处理振动超标信息 (VibrationOos) - 优先从RSS Run Report获取，如果没有再从RSS Daily Report获取
        // Checks for vibration out-of-specification (OOS) conditions from the oosList.
        // Priority: 1. RSSRunReport.oosList, 2. RSSDailyReport.oosList
        // Looks for specific vibration types (Lateral, Axial, Tangential) and non-normal/N/A OOS status.
        boolean vibrationIsOos = false;
        List<RSSOos> oosListToCheck = null;

        // 优先检查RSS Run Report中的OOS数据
        if (rssRunReport != null && !CollectionUtils.isEmpty(rssRunReport.getOosList())) {
            // 检查RSS Run Report的OOS列表是否有有效数据（不是空对象）
            boolean hasValidOosData = rssRunReport.getOosList().stream()
                    .anyMatch(oos -> oos != null && StringUtils.hasText(oos.getType()));

            if (hasValidOosData) {
                oosListToCheck = rssRunReport.getOosList();
                log.info("使用RSS Run Report中的OOS数据进行振动超标检查，jobId: {}, run: {}", jobInfo.getId(), runInput);
            }
        }

        // 如果RSS Run Report中没有有效的OOS数据，则使用RSS Daily Report中的数据
        if (oosListToCheck == null && latestRssDailyReport != null && !CollectionUtils.isEmpty(latestRssDailyReport.getOosList())) {
            boolean hasValidOosData = latestRssDailyReport.getOosList().stream()
                    .anyMatch(oos -> oos != null && StringUtils.hasText(oos.getType()));

            if (hasValidOosData) {
                oosListToCheck = latestRssDailyReport.getOosList();
                log.info("使用RSS Daily Report中的OOS数据进行振动超标检查，jobId: {}, run: {}", jobInfo.getId(), runInput);
            }
        }

        // 检查振动相关的OOS状态
        if (oosListToCheck != null) {
            for (RSSOos oos : oosListToCheck) {
                if (oos != null && StringUtils.hasText(oos.getType()) &&
                        (oos.getType().contains("横向振动") ||
                                oos.getType().contains("轴向震动") ||
                                oos.getType().contains("切向震动")) &&
                        "Y".equalsIgnoreCase(oos.getOos())) {
                    vibrationIsOos = true;
                    log.info("检测到振动超标: type={}, oos={}, note={}", oos.getType(), oos.getOos(), oos.getNote());
                    break;
                }
            }
        }

        detail.setVibrationOos(vibrationIsOos);

        // 设置其他字段
        detail.setVibrationLevel(null);
        detail.setExceededConditionDescription(null);

        // 设置故障描述字段，并过滤base64图片内容
        // 优先从MWD失效报告获取，如果没有则从RSS失效报告获取
        String fieldIncidentDesc = null;
        if (mwdFailureReport != null) {
            fieldIncidentDesc = mwdFailureReport.getFailureSymptoms();
        } else if (rssFailureReport != null) {
            // 从RSS失效报告的incidentDescription字段获取
            fieldIncidentDesc = rssFailureReport.getIncidentDescription();
        }
        detail.setFieldIncidentDescription(filterBase64ImagesFromRichText(fieldIncidentDesc));

        // 根本原因分析自己写，并改名为根本原因描述
        detail.setFailureReasonDescription(null);

        // 从 MongoDB 集合 mwd_failure_shop_inspection 中查询车间检查发现数据

        String improvementPlanMeasures = null;
        String workshopShopFinding = null;
        if (mwdFailureReport != null) {
            try {
                // 使用 jobId 和 date 作为查询条件
                Long jobId = jobInfo.getId();
                String date = mwdFailureReport.getDate();

                if (jobId != null && StringUtils.hasText(date)) {
                    MwdFailureShopInspection shopInspection = mwdFailureShopInspectionRepository.findOneByJobIdAndDate(jobId, date);
                    if (shopInspection != null && StringUtils.hasText(shopInspection.getContent())) {
                        workshopShopFinding = shopInspection.getContent();
                        improvementPlanMeasures = shopInspection.getImprovementPlanMeasures();
                        log.info("从 MongoDB 获取 mwdFailureReport 数据，jobId: {}, date: {}, 内容长度: {}",
                                jobId, date, improvementPlanMeasures.length()+workshopShopFinding.length());
                    } else {
                        log.debug("未在 MongoDB 中找到 mwdFailureReport 数据，jobId: {}, date: {}", jobId, date);
                    }
                } else {
                    log.warn("查询 mwdFailureReport 数据失败：jobId 或 date 为空，jobId: {}, date: {}", jobId, date);
                }
            } catch (Exception e) {
                log.error("从 MongoDB 查询 mwdFailureReport 数据失败：{}", e.getMessage(), e);
                // 发生异常时，workshopShopFinding 保持为 null
            }
        }
        detail.setWorkshopShopFinding(filterBase64ImagesFromRichText(workshopShopFinding));
        detail.setImprovementPlanMeasures(filterBase64ImagesFromRichText(improvementPlanMeasures));

        if (rssFailureReport != null) {
            try {
                // 使用 jobId 和 date 作为查询条件
                Long jobId = jobInfo.getId();
                String date = rssFailureReport.getDate();

                if (jobId != null && StringUtils.hasText(date)) {
                    MwdFailureShopInspection shopInspection = mwdFailureShopInspectionRepository.findOneByJobIdAndDate(jobId, date);
                    if (shopInspection != null && StringUtils.hasText(shopInspection.getContent())) {
                        improvementPlanMeasures = shopInspection.getImprovementPlanMeasures();
                        log.info("从 MongoDB 获取到 rssFailureReport 数据，jobId: {}, date: {}, 内容长度: {}",
                                jobId, date, improvementPlanMeasures.length());
                    } else {
                        log.debug("未在 MongoDB 中找到 rssFailureReport 数据，jobId: {}, date: {}", jobId, date);
                    }
                } else {
                    log.warn("查询 rssFailureReport 数据失败：jobId 或 date 为空，jobId: {}, date: {}", jobId, date);
                }
            } catch (Exception e) {
                log.error("从 MongoDB 查询 rssFailureReport 数据失败：{}", e.getMessage(), e);
                // 发生异常时，workshopShopFinding 保持为 null
            }
        }
        detail.setImprovementPlanMeasures(filterBase64ImagesFromRichText(improvementPlanMeasures));

        // Workshop/maintenance related fields, typically filled post-operation.
        detail.setMaintenanceLevel(null);
        detail.setPostUseMaintenanceCompletionDate(null);
        detail.setReplacedMajorComponentsDesc(null);
    }

    /**
     * 检查工具列表中是否存在近钻头类型的工具。
     *
     * @param mwdRunReport MWD运行报告，可能为null
     * @param rssRunReport RSS运行报告，可能为null
     * @return 如果工具列表中包含名称中含有"近钻"或与{@link EnumMwdDeviceType#AT_BIT}匹配的工具，则返回true；否则返回false。
     */
    private boolean checkForAtBitTool(MwdRunReport mwdRunReport, RSSRunReport rssRunReport) {
        // 安全地获取 toolList，避免空指针
        List<?> toolListRaw = (mwdRunReport != null && mwdRunReport.getToolList() != null) ? mwdRunReport.getToolList() :
                              ((rssRunReport != null && rssRunReport.getToolList() != null) ? rssRunReport.getToolList() : Collections.emptyList());

        if (CollectionUtils.isEmpty(toolListRaw)) {
            return false; // 工具列表为空，肯定没有近钻头工具
        }

        for (Object toolRaw : toolListRaw) {
            if (toolRaw == null) continue; // 跳过列表中的null元素

            String toolName = null;
            if (toolRaw instanceof MwdTool) {
                toolName = ((MwdTool) toolRaw).getInvName();
            } else if (toolRaw instanceof RSSTool) {
                toolName = ((RSSTool) toolRaw).getInvName();
            }

            // 检查工具名称是否等于近钻头关键字
            if (StringUtils.hasText(toolName)) {
                if ("近钻头".equals(toolName) ||
                    EnumMwdDeviceType.AT_BIT.name().equalsIgnoreCase(toolName) ||
                    "AT-BIT".equalsIgnoreCase(toolName)) {
                    return true; // 找到近钻头工具
                }
            }
        }
        return false; // 未找到近钻头工具
    }

    /**
     * 根据MWD或RSS运行报告确定起出原因。
     * 将报告中与钻头、螺杆、MWD/工具及其他相关的原因文本拼接起来。
     *
     * @param mwdRunReport MWD运行报告，可能为null
     * @param rssRunReport RSS运行报告，可能为null (通常只有一个报告会有值)
     * @return 拼接后的起出原因字符串，如果无任何原因信息则返回null
     */
    private String determineTripOutReason(MwdRunReport mwdRunReport, RSSRunReport rssRunReport) {
        StringBuilder reasons = new StringBuilder();
        // 优先处理MWD报告中的原因
        if (mwdRunReport != null) {
            if (StringUtils.hasText(mwdRunReport.getBitReason())) reasons.append("钻头原因: ").append(mwdRunReport.getBitReason()).append("; ");
            if (StringUtils.hasText(mwdRunReport.getMotorReason())) reasons.append("螺杆原因: ").append(mwdRunReport.getMotorReason()).append("; ");
            if (StringUtils.hasText(mwdRunReport.getMwdReason())) reasons.append("MWD原因: ").append(mwdRunReport.getMwdReason()).append("; ");
            if (StringUtils.hasText(mwdRunReport.getOtherReason())) reasons.append("其他原因: ").append(mwdRunReport.getOtherReason()).append("; ");
        } else if (rssRunReport != null) { // 如果没有MWD报告，则处理RSS报告中的原因
            if (StringUtils.hasText(rssRunReport.getBitReason())) reasons.append("钻头原因: ").append(rssRunReport.getBitReason()).append("; ");
            if (StringUtils.hasText(rssRunReport.getMotorReason())) reasons.append("螺杆原因: ").append(rssRunReport.getMotorReason()).append("; ");
            if (StringUtils.hasText(rssRunReport.getToolReason())) reasons.append("工具原因: ").append(rssRunReport.getToolReason()).append("; "); // RSS 中是 toolReason
            if (StringUtils.hasText(rssRunReport.getOtherReason())) reasons.append("其他原因: ").append(rssRunReport.getOtherReason()).append("; ");
        }

        // 如果拼接了原因，移除末尾多余的 "; "
        if (reasons.length() > 2) {
            return reasons.substring(0, reasons.length() - 2);
        }
        return null; // 没有原因信息
    }

    /**
     * 根据运行报告中的工具列表，准备用于保存的工具对象列表
     *
     * @param detailId 关联的主记录ID
     * @param mwdRunReport MWD运行报告
     * @param rssRunReport RSS运行报告
     * @return 构建好的工具对象列表
     */
    private List<WellAnalysisInstruments> prepareInstrumentsList(Long detailId, MwdRunReport mwdRunReport, RSSRunReport rssRunReport) {
        List<WellAnalysisInstruments> instrumentsToSave = new ArrayList<>();
        // 安全地获取 toolList
        List<?> toolListRaw = (mwdRunReport != null && mwdRunReport.getToolList() != null) ? mwdRunReport.getToolList() :
                              ((rssRunReport != null && rssRunReport.getToolList() != null) ? rssRunReport.getToolList() : Collections.emptyList());

        if (CollectionUtils.isEmpty(toolListRaw)) {
            return instrumentsToSave; // 如果没有工具信息，返回空列表
        }

        // 用于存储近钻头SN和对应组件的映射，避免重复查询
        Map<String, List<Component>> atBitComponentMap = new HashMap<>();

        // 第一次遍历：查找所有近钻头工具，并预先加载其关联组件
        for (Object toolRaw : toolListRaw) {
            if (toolRaw == null) continue;

            String sn = null;
            String invName = null;

            if (toolRaw instanceof MwdTool) {
                MwdTool mTool = (MwdTool) toolRaw;
                sn = mTool.getSerialNumber();
                invName = mTool.getInvName();
            } else if (toolRaw instanceof RSSTool) {
                RSSTool rTool = (RSSTool) toolRaw;
                sn = rTool.getSerialNumber();
                invName = rTool.getInvName();
            }

            // 如果是近钻头工具，预先加载其关联组件 - 使用精确匹配
            if (StringUtils.hasText(invName) && equalsAnyKeyword(invName, AT_BIT_KEYWORDS) && StringUtils.hasText(sn)) {
                // 获取关联组件的详细信息
                List<Component> componentObjects = getRelatedComponentObjects(sn);
                atBitComponentMap.put(sn, componentObjects);
            }
        }

        // 第二次遍历：根据工具类型过滤并保存符合条件的工具
        for (Object toolRaw : toolListRaw) {
            if (toolRaw == null) continue;

            String sn = null;
            String invName = null;
            String toolType = null;
            boolean isRssTool = false;
            boolean isAtBitTool = false;
            boolean isNonAtBitTool = false;

            if (toolRaw instanceof MwdTool) {
                MwdTool mTool = (MwdTool) toolRaw;
                sn = mTool.getSerialNumber();
                invName = mTool.getInvName();
                toolType = invName;
            } else if (toolRaw instanceof RSSTool) {
                RSSTool rTool = (RSSTool) toolRaw;
                sn = rTool.getSerialNumber();
                invName = rTool.getInvName();
                toolType = invName;
            }

            if (!StringUtils.hasText(invName) || !StringUtils.hasText(sn)) continue;

            // 判断是否是RSS工具（包含"LB"的invName）
            // 前面已经检查过invName不为空，这里再次确认
            boolean isLbRssTool = invName != null && invName.toUpperCase().contains("LB");

            // 预先判断是否是RSS工具（用于决定是否替换invName）
            String originalInvName = invName;
            boolean isOriginalRssTool = containsAnyKeyword(originalInvName, RSS_SU_KEYWORDS) ||
                                       containsAnyKeyword(originalInvName, RSS_MWD_KEYWORDS) ||
                                       containsAnyKeyword(originalInvName, RSS_LCP_KEYWORDS);

            // 通过SN查询设备信息
            DeviceInfo deviceInfo = getDeviceInfo(sn);
            if (deviceInfo != null) {
                // 对于RSS工具，保持原始invName作为仪器角色，将详细描述放到toolType中
                if (isLbRssTool || isOriginalRssTool) {
                    log.info("RSS工具保持原始invName: {}, SN: {}", invName, sn);
                    // toolType设置为详细描述（如果有的话）
                    if (StringUtils.hasText(deviceInfo.getInvName())) {
                        toolType = deviceInfo.getInvName(); // 详细描述，如"6.75脉冲发电机短节"
                    } else if (StringUtils.hasText(deviceInfo.getDeviceType())) {
                        toolType = deviceInfo.getDeviceType(); // 备选：设备类型代码
                    }
                } else {
                    // 非RSS工具，使用标准invName替换
                    if (StringUtils.hasText(deviceInfo.getInvName())) {
                        log.info("使用标准invName: {} 替换原始invName: {}, SN: {}", deviceInfo.getInvName(), invName, sn);
                        invName = deviceInfo.getInvName();
                    }
                    // 设置toolType为deviceType
                    if (StringUtils.hasText(deviceInfo.getDeviceType())) {
                        toolType = deviceInfo.getDeviceType();
                    }
                }
                log.info("设置工具信息: invName={}, toolType={}, SN={}", invName, toolType, sn);
            }

            // 判断工具类型
            // 1. RSS工具 - 使用原始invName进行判断（因为可能已经被device表中的标准名称替换）
            if (isOriginalRssTool ||
                containsAnyKeyword(invName, RSS_SU_KEYWORDS) ||
                containsAnyKeyword(invName, RSS_MWD_KEYWORDS) ||
                containsAnyKeyword(invName, RSS_LCP_KEYWORDS)) {
                isRssTool = true;
            }

            // 2. 近钻头工具 - 使用精确匹配
            if (equalsAnyKeyword(invName, AT_BIT_KEYWORDS)) {
                isAtBitTool = true;
            }

            // 检查是否是接收器 - 接收器不应该被认为是近钻头工具 （实际上用到了在2025/7/31又需要了）
            if (containsAnyKeyword(invName, RECEIVER_KEYWORDS)) {
                isAtBitTool = true;
            }

            // 3. 非近钻工具
            if (containsAnyKeyword(invName, PULSER_KEYWORDS) ||
                containsAnyKeyword(invName, BOTTOM_END_KEYWORDS) ||
                containsAnyKeyword(invName, DM_KEYWORDS) ||
                containsAnyKeyword(invName, AZ_DM_KEYWORDS) ||
                containsAnyKeyword(invName, GAMMA_KEYWORDS) ||
                containsAnyKeyword(invName, AZ_GAMMA_KEYWORDS) ||
                containsAnyKeyword(invName, INSULATION_SUB_KEYWORDS)) {
                isNonAtBitTool = true;
                isAtBitTool = true; // 如果是非近钻工具，也标记为近钻头工具
            }

            // 如果工具不属于任何一种类型，跳过
            if (!isRssTool && !isAtBitTool && !isNonAtBitTool) {
                continue;
            }

            // 创建并保存工具
            WellAnalysisInstruments instrument = new WellAnalysisInstruments();
            instrument.setAnalysisDetailIdFk(detailId);
            // 设置toolType为deviceType
            instrument.setToolType(StringUtils.hasText(toolType) ? toolType : "未知类型");
            instrument.setInvName(StringUtils.hasText(invName) ? invName : "未知仪器");
            instrument.setSerialNumber(sn);

            // 手动设置创建时间和更新时间
            Date now = new Date();
            instrument.setCreateTime(now);
            instrument.setUpdateTime(now);

            if (StringUtils.hasText(sn)) {
                // 通过SN查询设备信息获取风险类型
                DeviceInfo deviceInfoForRisk = getDeviceInfo(sn);
                if (deviceInfoForRisk != null && StringUtils.hasText(deviceInfoForRisk.getRiskType())) {
                    instrument.setRiskType(deviceInfoForRisk.getRiskType());
                } else {
                    instrument.setRiskType(null);
                }
            }

            instrumentsToSave.add(instrument);
        }

        // 添加近钻头关联的发射器、接收器和近钻Gamma组件
        for (Map.Entry<String, List<Component>> entry : atBitComponentMap.entrySet()) {
            String atBitSn = entry.getKey();
            List<Component> components = entry.getValue();

            log.info("处理近钻头 {} 的关联组件，共 {} 个", atBitSn, components.size());

            // 预加载所有组件的设备信息
            for (Component component : components) {
                if (component != null && StringUtils.hasText(component.getSerialNumber())) {
                    getDeviceInfo(component.getSerialNumber()); // 这会将结果缓存到deviceInfoMap中
                }
            }

            for (Component component : components) {
                if (component == null) continue;

                String componentInvName = component.getInvName();
                String componentSn = component.getSerialNumber();

                if (!StringUtils.hasText(componentInvName)) continue;

                // 检查是否是At Bit EC、接收器、At Bit Gamma或绝缘短节
                if (isRelevantComponent(componentInvName)) {

                    WellAnalysisInstruments instrument = new WellAnalysisInstruments();
                    instrument.setAnalysisDetailIdFk(detailId);

                    // 判断是否是RSS工具组件（包含"LB"的invName）
                    boolean isLbRssComponent = componentInvName != null && componentInvName.toUpperCase().contains("LB");

                    // 设置toolType为deviceType
                    DeviceInfo deviceInfo = null;
                    if (StringUtils.hasText(componentSn)) {
                        deviceInfo = getDeviceInfo(componentSn);
                    }

                    if (deviceInfo != null && StringUtils.hasText(deviceInfo.getDeviceType())) {
                        instrument.setToolType(deviceInfo.getDeviceType());
                    } else {
                        instrument.setToolType(componentInvName);
                    }

                    // 所有组件都使用原始invName，对于RSS工具组件记录日志
                    if (isLbRssComponent) {
                        log.info("RSS工具组件保持原始invName: {}, SN: {}", componentInvName, componentSn);
                    }
                    instrument.setInvName(componentInvName);

                    // 如果有组件的序列号，则使用组件的序列号，否则使用"关联自近钻头: + 近钻头SN"
                    instrument.setSerialNumber(StringUtils.hasText(componentSn) ? componentSn : "关联自近钻头: " + atBitSn);

                    // 手动设置创建时间和更新时间
                    Date now = new Date();
                    instrument.setCreateTime(now);
                    instrument.setUpdateTime(now);

                    // 设置风险类型
                    if (deviceInfo != null && StringUtils.hasText(deviceInfo.getRiskType())) {
                        instrument.setRiskType(deviceInfo.getRiskType());
                    } else {
                        instrument.setRiskType(null);
                    }

                    instrumentsToSave.add(instrument);
                    log.info("添加近钻头关联组件: invName={}, toolType={}, SN={}, riskType={}, 关联自近钻头: {}",
                             componentInvName,
                             instrument.getToolType(),
                             StringUtils.hasText(componentSn) ? componentSn : "无序列号",
                             instrument.getRiskType(),
                             atBitSn);
                }
            }
        }

        log.info("共准备保存 {} 个工具", instrumentsToSave.size());
        return instrumentsToSave;
    }

    /**
     * 获取与近钻头相关的组件对象列表
     *
     * @param serialNumber 近钻头序列号
     * @return 相关组件对象列表
     */
    /**
     * 通过序列号查询标准的invName
     *
     * @param serialNumber 工具序列号
     * @return 标准的invName，如果未找到则返回null
     */
    /**
     * 设备信息类，用于返回设备的多个属性
     */
    private static class DeviceInfo {
        private String invName;
        private String deviceType;
        private String riskType;

        public DeviceInfo(String invName, String deviceType, String riskType) {
            this.invName = invName;
            this.deviceType = deviceType;
            this.riskType = riskType;
        }

        public String getInvName() {
            return invName;
        }

        public String getDeviceType() {
            return deviceType;
        }

        public String getRiskType() {
            return riskType;
        }
    }

    /**
     * 通过序列号查询设备信息，包括invName、deviceType和riskType
     *
     * @param serialNumber 工具序列号
     * @return 设备信息对象，如果未找到则返回null
     */
    private DeviceInfo getDeviceInfo(String serialNumber) {
        if (!StringUtils.hasText(serialNumber)) {
            return null;
        }

        // 如果已经查询过，直接返回缓存结果
        if (deviceInfoMap.containsKey(serialNumber)) {
            return deviceInfoMap.get(serialNumber);
        }

        try {
            Device device = deviceMapper.selectOne(
                Wrappers.lambdaQuery(Device.class)
                    .eq(Device::getSerialNumber, serialNumber)
                    .last("LIMIT 1")
            );

            if (device != null) {
                String invName = device.getInvName();
                String deviceType = device.getDeviceType() != null ? device.getDeviceType().toString() : null;
                String riskType = device.getRiskType() != null ? device.getRiskType().name() : null;

                DeviceInfo deviceInfo = new DeviceInfo(invName, deviceType, riskType);

                // 缓存查询结果
                deviceInfoMap.put(serialNumber, deviceInfo);

                return deviceInfo;
            }
        } catch (Exception e) {
            log.error("通过SN查询设备信息失败: {}", e.getMessage(), e);
        }

        return null;
    }

    private List<Component> getRelatedComponentObjects(String serialNumber) {
        // 如果已经查询过，直接返回缓存结果
        if (atBitComponentCache.containsKey(serialNumber)) {
            log.info("从缓存中获取与近钻头 {} 相关的组件对象", serialNumber);
            return atBitComponentCache.get(serialNumber);
        }

        List<Component> components = new ArrayList<>();
        log.info("开始查询与近钻头 {} 相关的组件对象", serialNumber);

        try {
            // 1. 查询device表，获取device_id
            Device device = deviceMapper.selectOne(
                Wrappers.lambdaQuery(Device.class)
                    .eq(Device::getSerialNumber, serialNumber)
                    .last("LIMIT 1")
            );

            if (device == null || device.getDeviceId() == null) {
                log.warn("未找到序列号为 {} 的设备", serialNumber);
                return components;
            }

            // 2. 查询device_component_relation表，获取与近钻头相关的component_id列表
            List<Long> componentIds = deviceComponentRelationMapper.getComponentIdListByDeviceId(device.getDeviceId());
            if (CollectionUtils.isEmpty(componentIds)) {
                log.warn("设备ID {} 没有关联的组件", device.getDeviceId());
                return components;
            }

            // 3. 查询component表，获取对应的组件对象
            components = componentMapper.selectList(
                Wrappers.lambdaQuery(Component.class)
                    .in(Component::getComponentId, componentIds)
            );

            if (CollectionUtils.isEmpty(components)) {
                log.warn("未找到组件ID列表 {} 对应的组件", componentIds);
            } else {
                log.info("共找到 {} 个关联组件", components.size());
                // 记录找到的组件信息
                for (Component component : components) {
                    String invName = component.getInvName();
                    String componentSn = component.getSerialNumber();
                    if (StringUtils.hasText(invName)) {
                        // 特别检查是否是近钻Gamma、At Bit EC、接收器或绝缘短节
                        if (containsAnyKeyword(invName, AT_BIT_GAMMA_KEYWORDS)) {
                            log.info("找到近钻Gamma组件: {}, 序列号: {}", invName, componentSn);
                        } else if (containsAnyKeyword(invName, AT_BIT_EC_KEYWORDS)) {
                            log.info("找到At Bit EC组件: {}, 序列号: {}", invName, componentSn);
                        } else if (containsAnyKeyword(invName, RECEIVER_KEYWORDS)) {
                            log.info("找到接收器组件: {}, 序列号: {}", invName, componentSn);
                        } else if (containsAnyKeyword(invName, INSULATION_SUB_KEYWORDS)) {
                            log.info("找到绝缘短节组件: {}, 序列号: {}", invName, componentSn);
                        }
                    } else {
                        log.warn("组件 ID={}, 序列号={} 的invName为空", component.getComponentId(), componentSn);
                    }
                }
            }

            // 缓存查询结果
            atBitComponentCache.put(serialNumber, components);

        } catch (Exception e) {
            log.error("获取与近钻头相关的组件对象失败：{}", e.getMessage(), e);
        }

        log.info("完成查询与近钻头 {} 相关的组件对象，共找到 {} 个组件", serialNumber, components.size());
        return components;
    }

    /**
     * 解析日期字符串为 Date 对象。
     * 支持的格式包括：
     * - yyyy-MM-dd HH:mm (日期时间格式，如 "2025-05-27 01:00")
     * - yyyy/MM/dd HH:mm (日期时间格式，如 "2025/05/27 01:00")
     * - yyyy-MM-dd (纯日期格式，如 "2021-04-02")
     * - yyyy/MM/dd (纯日期格式)
     *
     * @param dateString 日期字符串
     * @return 解析成功则返回 Date 对象，否则返回 null
     */
    private Date parseReportDate(String dateString) {
        if (!StringUtils.hasText(dateString)) {
            return null;
        }

        // 定义支持的日期时间格式列表 (优先尝试包含时间的格式)
        SimpleDateFormat[] formatters = {
            new SimpleDateFormat("yyyy-MM-dd HH:mm"),  // 标准日期时间格式
            new SimpleDateFormat("yyyy/MM/dd HH:mm"),  // 替代日期时间格式
            new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"), // ISO 8601 格式
            new SimpleDateFormat("yyyy-MM-dd"),        // 纯日期格式（MwdFailureReport.failureDate 使用此格式）
            new SimpleDateFormat("yyyy/MM/dd")         // 替代纯日期格式
        };

        for (SimpleDateFormat formatter : formatters) {
            try {
                return formatter.parse(dateString);
            } catch (ParseException e) {
                // 继续尝试下一个格式
            }
        }

        // 所有格式尝试均失败
        log.warn("无法解析日期字符串: {}", dateString);
        return null;
    }



    @Override
    public List<WellOperationAnalysisDetail> listByJobInfoId(Long jobInfoIdFk) {
        if (jobInfoIdFk == null) {
            return Collections.emptyList();
        }
        return baseMapper.findByJobInfoIdFk(jobInfoIdFk);
    }

    @Override
    public List<WellOperationAnalysisDetail> listByWellInfoId(String wellInfoIdFk) {
        if (!StringUtils.hasText(wellInfoIdFk)) {
            return Collections.emptyList();
        }
        return baseMapper.findByWellInfoIdFk(wellInfoIdFk);
    }

    @Override
    public WellOperationAnalysisDetailVO getDetailWithInstrumentsById(Long id) {
        if (id == null) {
            return null;
        }

        // 1. 获取主表记录
        WellOperationAnalysisDetail detail = this.getById(id);
        if (detail == null) {
            return null;
        }

        // 2. 查询同一个井号+作业号的所有记录，以确定最大run号
        List<WellOperationAnalysisDetail> sameWellJobRecords = this.list(
            Wrappers.lambdaQuery(WellOperationAnalysisDetail.class)
                .eq(WellOperationAnalysisDetail::getWellNumber, detail.getWellNumber())
                .eq(WellOperationAnalysisDetail::getJobNumber, detail.getJobNumber())
        );

        // 3. 计算最大run号
        Integer maxRun = sameWellJobRecords.stream()
            .map(WellOperationAnalysisDetail::getRun)
            .filter(Objects::nonNull)
            .max(Integer::compareTo)
            .orElse(detail.getRun());

        // 4. 计算正确的jobStatus
        Integer correctJobStatus = calculateCorrectJobStatus(detail.getJobStatus(), detail.getRun(), maxRun);

        // 5. 转换为 VO 对象
        WellOperationAnalysisDetailVO detailVO = convertToVO(detail);
        // 设置重新计算的jobStatus
        detailVO.setJobStatus(correctJobStatus);

        // 6. 获取关联的工具记录
        List<WellAnalysisInstruments> instruments = wellAnalysisInstrumentsService.list(
            Wrappers.lambdaQuery(WellAnalysisInstruments.class)
                .eq(WellAnalysisInstruments::getAnalysisDetailIdFk, id)
        );

        // 7. 转换工具记录为 VO 对象并设置到主表 VO 中
        if (!CollectionUtils.isEmpty(instruments)) {
            List<WellAnalysisInstrumentsVO> instrumentVOs = instruments.stream()
                .map(this::convertToInstrumentVO)
                .collect(Collectors.toList());
            detailVO.setInstruments(instrumentVOs);
        }

        return detailVO;
    }

    /**
     * 映射并验证排序字段
     * 将前端字段名映射到数据库字段名，并验证是否在白名单中
     *
     * @param frontendField 前端字段名
     * @return 映射后的数据库字段名，如果不在白名单中则返回默认字段
     */
    private String mapAndValidateSortField(String frontendField) {
        // 定义前端字段名到数据库字段名的映射
        Map<String, String> fieldMapping = new HashMap<>();
        fieldMapping.put("wellNumber", "well_number");
        fieldMapping.put("jobNumber", "job_number");
        fieldMapping.put("run", "run");
        fieldMapping.put("serviceWellCategory", "service_well_category");
        fieldMapping.put("temperatureValue", "temperature_value");
        fieldMapping.put("block", "block");
        fieldMapping.put("constructionEndDepthMd", "construction_end_depth_md");
        fieldMapping.put("footageMd", "footage_md");
        fieldMapping.put("isFailure", "is_failure");
        fieldMapping.put("dateIn", "date_in");
        fieldMapping.put("tripInHours", "trip_in_hours");
        fieldMapping.put("createTime", "create_time");
        fieldMapping.put("updateTime", "update_time");

        // 获取映射后的字段名
        String mappedField = fieldMapping.get(frontendField);

        // 如果字段名不在映射中，返回默认排序字段
        if (mappedField == null) {
            log.warn("尝试使用未知的排序字段: {}", frontendField);
            return "create_time";
        }

        return mappedField;
    }

    @Override
    public IPage<WellOperationAnalysisDetailBasicVO> pageBasicInfo(int current, int size, String wellNumber, Integer runNumber, String serviceWellCategory, String isFailure, String orderBy, String orderType) {
        // 1. 创建分页对象
        Page<WellOperationAnalysisDetailBasicVO> voPage = new Page<>(current, size);

        // 2. 计算offset
        long offset = (long)(current - 1) * size;
        if (offset < 0) offset = 0;

        // 3. 设置排序字段和顺序，并进行安全验证
        String orderByField = "create_time"; // 默认排序字段
        if (StringUtils.hasText(orderBy)) {
            // 将前端字段名映射到数据库字段名，并验证是否在白名单中
            orderByField = mapAndValidateSortField(orderBy);
        }
        boolean isAsc = "asc".equalsIgnoreCase(orderType);

        // 4. 直接查询原始数据
        List<WellOperationAnalysisDetail> records = baseMapper.findPageDetails(offset, (long)size, wellNumber, runNumber, serviceWellCategory, isFailure, orderByField, isAsc);
        long total = baseMapper.countPageDetails(wellNumber, runNumber, serviceWellCategory, isFailure);

        // 5. 设置分页信息
        voPage.setTotal(total);
        voPage.setSize(size);
        voPage.setCurrent(current);
        voPage.setPages((total + size - 1) / size);

        // 6. 如果原始数据为空，直接返回空页
        if (records == null || records.isEmpty()) {
            voPage.setRecords(Collections.emptyList());
            return voPage;
        }

        // 7. 计算每个井号+作业号组合的最大run号，用于确定jobStatus
        Map<String, Integer> maxRunMap = records.stream()
            .collect(Collectors.groupingBy(
                detail -> detail.getWellNumber() + "|" + detail.getJobNumber(),
                Collectors.mapping(WellOperationAnalysisDetail::getRun,
                    Collectors.maxBy(Integer::compareTo))
            ))
            .entrySet().stream()
            .collect(Collectors.toMap(
                Map.Entry::getKey,
                entry -> entry.getValue().orElse(0)
            ));

        // 8. 转换每条记录为BasicVO，并加载关联的工具信息
        List<WellOperationAnalysisDetailBasicVO> voList = new ArrayList<>(records.size());

        // 9. 收集所有详情ID，用于批量查询工具记录
        List<Long> detailIds = records.stream()
            .map(WellOperationAnalysisDetail::getId)
            .collect(Collectors.toList());

        // 10. 批量查询所有工具记录
        List<WellAnalysisInstruments> allInstruments = wellAnalysisInstrumentsService.list(
            Wrappers.lambdaQuery(WellAnalysisInstruments.class)
                .in(WellAnalysisInstruments::getAnalysisDetailIdFk, detailIds)
        );

        // 11. 按照 analysis_detail_id_fk 分组
        Map<Long, List<WellAnalysisInstruments>> instrumentsMap = allInstruments.stream()
            .collect(Collectors.groupingBy(WellAnalysisInstruments::getAnalysisDetailIdFk));

        for (WellOperationAnalysisDetail detail : records) {
            // 计算正确的jobStatus
            String wellJobKey = detail.getWellNumber() + "|" + detail.getJobNumber();
            Integer maxRun = maxRunMap.get(wellJobKey);
            Integer correctJobStatus = calculateCorrectJobStatus(detail.getJobStatus(), detail.getRun(), maxRun);

            // 转换为BasicVO
            WellOperationAnalysisDetailBasicVO basicVO = convertToBasicVO(detail);
            // 设置重新计算的jobStatus
            basicVO.setJobStatus(correctJobStatus);

            // 获取关联的工具记录
            Long detailId = detail.getId();
            List<WellAnalysisInstruments> instruments = instrumentsMap.getOrDefault(detailId, Collections.emptyList());

            // 转换工具记录为VO并设置到BasicVO中
            if (!instruments.isEmpty()) {
                List<WellAnalysisInstrumentsVO> instrumentVOs = instruments.stream()
                    .map(this::convertToInstrumentVO)
                    .collect(Collectors.toList());
                basicVO.setInstruments(instrumentVOs);
            }

            voList.add(basicVO);
        }

        // 11. 设置结果并返回
        voPage.setRecords(voList);
        return voPage;
    }

    @Override
    public WellOperationAnalysisDetailRichContentVO getRichTextContentById(Long id) {
        if (id == null) {
            return null;
        }

        // 1. 获取主表记录
        WellOperationAnalysisDetail detail = this.getById(id);
        if (detail == null) {
            return null;
        }

        // 2. 转换为富文本内容VO对象
        WellOperationAnalysisDetailRichContentVO richContentVO = convertToRichContentVO(detail);

        // 3. 获取关联的工具记录
        List<WellAnalysisInstruments> instruments = wellAnalysisInstrumentsService.list(
            Wrappers.lambdaQuery(WellAnalysisInstruments.class)
                .eq(WellAnalysisInstruments::getAnalysisDetailIdFk, id)
        );

        // 4. 转换工具记录为VO对象并设置到富文本内容VO中
        if (!CollectionUtils.isEmpty(instruments)) {
            List<WellAnalysisInstrumentsVO> instrumentVOs = instruments.stream()
                .map(this::convertToInstrumentVO)
                .collect(Collectors.toList());
            richContentVO.setInstruments(instrumentVOs);
        }

        return richContentVO;
    }

    @Override
    public IPage<WellOperationAnalysisDetailVO> pageDetailsWithInstruments(int current, int size, String wellNumber, Integer runNumber, String serviceWellCategory, String isFailure) {
        // 1. 创建分页对象
        Page<WellOperationAnalysisDetailVO> voPage = new Page<>(current, size);

        // 2. 计算offset
        long offset = (long)(current - 1) * size;
        if (offset < 0) offset = 0;

        // 3. 设置排序字段和顺序
        String orderByField = "date_in";
        boolean isAsc = false;

        // 4. 直接查询原始数据
        List<WellOperationAnalysisDetail> records = baseMapper.findPageDetails(offset, (long)size, wellNumber, runNumber, serviceWellCategory, isFailure, orderByField, isAsc);
        long total = baseMapper.countPageDetails(wellNumber, runNumber, serviceWellCategory, isFailure);

        // 5. 设置分页信息
        voPage.setTotal(total);
        voPage.setSize(size);
        voPage.setCurrent(current);
        voPage.setPages((total + size - 1) / size);

        // 6. 如果原始数据为空，直接返回空页
        if (records == null || records.isEmpty()) {
            voPage.setRecords(Collections.emptyList());
            return voPage;
        }

        // 7. 转换每条记录为VO，并加载关联的工具信息
        List<WellOperationAnalysisDetailVO> voList = new ArrayList<>(records.size());

        // 8. 收集所有详情ID，用于批量查询工具记录
        List<Long> detailIds = records.stream()
            .map(WellOperationAnalysisDetail::getId)
            .collect(Collectors.toList());

        // 9. 批量查询所有工具记录
        List<WellAnalysisInstruments> allInstruments = wellAnalysisInstrumentsService.list(
            Wrappers.lambdaQuery(WellAnalysisInstruments.class)
                .in(WellAnalysisInstruments::getAnalysisDetailIdFk, detailIds)
        );

        // 10. 计算每个井号+作业号组合的最大run号，用于确定jobStatus
        Map<String, Integer> maxRunMap = records.stream()
            .collect(Collectors.groupingBy(
                detail -> detail.getWellNumber() + "|" + detail.getJobNumber(),
                Collectors.mapping(WellOperationAnalysisDetail::getRun,
                    Collectors.maxBy(Integer::compareTo))
            ))
            .entrySet().stream()
            .collect(Collectors.toMap(
                Map.Entry::getKey,
                entry -> entry.getValue().orElse(0)
            ));

        // 11. 按照 analysis_detail_id_fk 分组
        Map<Long, List<WellAnalysisInstruments>> instrumentsMap = allInstruments.stream()
            .collect(Collectors.groupingBy(WellAnalysisInstruments::getAnalysisDetailIdFk));

        for (WellOperationAnalysisDetail detail : records) {
            // 计算正确的jobStatus
            String wellJobKey = detail.getWellNumber() + "|" + detail.getJobNumber();
            Integer maxRun = maxRunMap.get(wellJobKey);
            Integer correctJobStatus = calculateCorrectJobStatus(detail.getJobStatus(), detail.getRun(), maxRun);

            // 转换为VO
            WellOperationAnalysisDetailVO detailVO = convertToVO(detail);
            // 设置重新计算的jobStatus
            detailVO.setJobStatus(correctJobStatus);

            // 获取关联的工具记录
            Long detailId = detail.getId();
            List<WellAnalysisInstruments> instruments = instrumentsMap.getOrDefault(detailId, Collections.emptyList());

            // 转换工具记录为VO并设置到主表VO中
            if (!instruments.isEmpty()) {
                List<WellAnalysisInstrumentsVO> instrumentVOs = instruments.stream()
                    .map(this::convertToInstrumentVO)
                    .collect(Collectors.toList());
                detailVO.setInstruments(instrumentVOs);
            }

            voList.add(detailVO);
        }

        // 11. 设置转换后的记录列表到分页对象
        voPage.setRecords(voList);

        return voPage;
    }

    @Override
    @Transactional
    public WellOperationAnalysisDetailVO updateDetailWithInstruments(WellOperationAnalysisDetailVO detailVO) {
        if (detailVO == null) {
            log.error("更新井作业分析详情失败：参数为null");
            return null;
        }

        Long detailId = detailVO.getId();
        if (detailId == null) {
            log.error("更新井作业分析详情失败：ID为null");
            return null;
        }

        // 1. 检查记录是否存在
        WellOperationAnalysisDetail existingDetail = this.getById(detailId);
        if (existingDetail == null) {
            log.error("更新井作业分析详情失败：ID为 {} 的记录不存在", detailId);
            return null;
        }

        try {
            // 2. 更新主表数据
            WellOperationAnalysisDetail detail = new WellOperationAnalysisDetail();
            BeanUtils.copyProperties(detailVO, detail);

            // 设置更新时间和数据录入人
            detail.setUpdateTime(new Date());

            // 获取当前登录用户名并设置 dataEntryPerson
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.isAuthenticated()) {
                String currentUsername = authentication.getName();
                if (StringUtils.hasText(currentUsername)) {
                    // 根据用户名查找UserInfo
                    UserInfo userInfo = userInfoService.getOne(Wrappers.lambdaQuery(UserInfo.class).eq(UserInfo::getUsername, currentUsername));
                    if (userInfo != null && userInfo.getMemberId() != null) {
                        // 根据memberId查找MemberInfo
                        MemberInfo memberInfo = memberInfoService.getById(userInfo.getMemberId());
                        if (memberInfo != null && StringUtils.hasText(memberInfo.getName())) {
                            detail.setDataEntryPerson(memberInfo.getName());
                            log.info("数据录入人设置为: {}", memberInfo.getName());
                        } else {
                            log.warn("未能获取到 MemberInfo 或 MemberInfo 中姓名为 null, username: {}", currentUsername);
                            detail.setDataEntryPerson(currentUsername); // Fallback to username if member name not found
                        }
                    } else {
                        log.warn("未能获取到 UserInfo 或 UserInfo 中 memberId 为 null, username: {}", currentUsername);
                        detail.setDataEntryPerson(currentUsername); // Fallback to username if user info not found
                    }
                }
            }

            // 使用自定义的更新方法，确保只更新需要的字段
            boolean updateSuccess = baseMapper.updateDetail(detail) > 0;
            if (!updateSuccess) {
                log.error("更新井作业分析详情失败：ID为 {} 的记录更新失败", detailId);
                return null;
            }

            // 3. 处理子表数据（如果有）
            List<WellAnalysisInstrumentsVO> instrumentVOs = detailVO.getInstruments();
            if (instrumentVOs != null && !instrumentVOs.isEmpty()) {
                // 按ID分类：需要更新的和需要新增的
                List<WellAnalysisInstruments> toUpdate = new ArrayList<>();
                List<WellAnalysisInstruments> toInsert = new ArrayList<>();

                for (WellAnalysisInstrumentsVO instrumentVO : instrumentVOs) {
                    if (instrumentVO.getInstrumentRecordId() != null) {
                        // 有ID，执行更新
                        WellAnalysisInstruments instrument = new WellAnalysisInstruments();
                        BeanUtils.copyProperties(instrumentVO, instrument);

                        // 确保关联ID正确
                        instrument.setAnalysisDetailIdFk(detailId);

                        // 设置更新时间
                        instrument.setUpdateTime(new Date());

                        toUpdate.add(instrument);
                        log.debug("准备更新工具记录，ID: {}, 类型: {}", instrument.getInstrumentRecordId(), instrument.getToolType());
                    } else {
                        // 无ID，执行新增
                        WellAnalysisInstruments instrument = new WellAnalysisInstruments();
                        BeanUtils.copyProperties(instrumentVO, instrument);

                        // 确保关联ID正确
                        instrument.setAnalysisDetailIdFk(detailId);

                        // 设置创建和更新时间
                        Date now = new Date();
                        instrument.setCreateTime(now);
                        instrument.setUpdateTime(now);

                        toInsert.add(instrument);
                        log.debug("准备新增工具记录，类型: {}", instrument.getToolType());
                    }
                }

                // 执行更新操作
                if (!toUpdate.isEmpty()) {
                    log.info("更新 {} 条工具记录", toUpdate.size());
                    boolean updateInstrumentsSuccess = wellAnalysisInstrumentsService.updateBatchById(toUpdate);
                    if (!updateInstrumentsSuccess) {
                        log.error("更新井作业分析详情失败：更新子表记录失败");
                        throw new RuntimeException("更新子表记录失败");
                    }
                }

                // 执行新增操作
                if (!toInsert.isEmpty()) {
                    log.info("新增 {} 条工具记录", toInsert.size());
                    boolean insertInstrumentsSuccess = wellAnalysisInstrumentsService.saveBatch(toInsert);
                    if (!insertInstrumentsSuccess) {
                        log.error("更新井作业分析详情失败：新增子表记录失败");
                        throw new RuntimeException("新增子表记录失败");
                    }
                }
            }

            // 4. 处理需要删除的子表记录（如果有）
            List<Long> instrumentIdsToDelete = detailVO.getInstrumentIdsToDelete();
            if (instrumentIdsToDelete != null && !instrumentIdsToDelete.isEmpty()) {
                log.info("处理需要删除的工具记录，ID列表: {}", instrumentIdsToDelete);

                // 验证这些ID确实属于当前主表记录
                List<WellAnalysisInstruments> instrumentsToCheck = wellAnalysisInstrumentsService.list(
                    Wrappers.lambdaQuery(WellAnalysisInstruments.class)
                        .eq(WellAnalysisInstruments::getAnalysisDetailIdFk, detailId)
                        .in(WellAnalysisInstruments::getInstrumentRecordId, instrumentIdsToDelete)
                );

                // 只删除确实属于当前主表记录的子表记录
                List<Long> validIdsToDelete = instrumentsToCheck.stream()
                    .map(WellAnalysisInstruments::getInstrumentRecordId)
                    .collect(Collectors.toList());

                if (!validIdsToDelete.isEmpty()) {
                    log.info("删除 {} 条工具记录", validIdsToDelete.size());
                    boolean deleteSuccess = wellAnalysisInstrumentsService.removeByIds(validIdsToDelete);
                    if (!deleteSuccess) {
                        log.error("更新井作业分析详情失败：删除子表记录失败");
                        throw new RuntimeException("删除子表记录失败");
                    }
                } else {
                    log.warn("没有找到需要删除的有效工具记录，请求的ID列表: {}", instrumentIdsToDelete);
                }
            }

            // 5. 查询更新后的完整数据并返回
            return getDetailWithInstrumentsById(detailId);

        } catch (Exception e) {
            log.error("更新井作业分析详情异常：ID为 {}, 错误信息: {}", detailId, e.getMessage(), e);
            throw new RuntimeException("更新井作业分析详情异常", e);
        }
    }

    /**
     * 将 WellOperationAnalysisDetail 实体转换为 VO 对象
     */
    private WellOperationAnalysisDetailVO convertToVO(WellOperationAnalysisDetail detail) {
        if (detail == null) {
            return null;
        }

        WellOperationAnalysisDetailVO vo = new WellOperationAnalysisDetailVO();
        BeanUtils.copyProperties(detail, vo);
        // 初始化instruments列表，避免前端出现null
        vo.setInstruments(new ArrayList<>());
        return vo;
    }

    /**
     * 将 WellOperationAnalysisDetail 实体转换为基本信息 VO 对象
     */
    private WellOperationAnalysisDetailBasicVO convertToBasicVO(WellOperationAnalysisDetail detail) {
        if (detail == null) {
            return null;
        }

        WellOperationAnalysisDetailBasicVO vo = new WellOperationAnalysisDetailBasicVO();
        BeanUtils.copyProperties(detail, vo);

        // 复制富文本字段
        vo.setFailureReasonDescription(detail.getFailureReasonDescription());
        vo.setImprovementPlanMeasures(detail.getImprovementPlanMeasures());
        vo.setFieldIncidentDescription(detail.getFieldIncidentDescription());
        vo.setWorkshopShopFinding(detail.getWorkshopShopFinding());

        // 复制维护相关字段
        vo.setMaintenanceLevel(detail.getMaintenanceLevel());
        vo.setPostUseMaintenanceCompletionDate(detail.getPostUseMaintenanceCompletionDate());
        vo.setReplacedMajorComponentsDesc(detail.getReplacedMajorComponentsDesc());
        vo.setTripOutReasonType(detail.getTripOutReasonType());

        // 初始化instruments列表，避免前端出现null
        vo.setInstruments(new ArrayList<>());
        return vo;
    }

    /**
     * 将 WellOperationAnalysisDetail 实体转换为富文本内容 VO 对象
     */
    private WellOperationAnalysisDetailRichContentVO convertToRichContentVO(WellOperationAnalysisDetail detail) {
        if (detail == null) {
            return null;
        }

        WellOperationAnalysisDetailRichContentVO vo = new WellOperationAnalysisDetailRichContentVO();
        // 只复制需要的字段
        vo.setId(detail.getId());
        vo.setMudType(detail.getMudType());
        vo.setMudDensity(detail.getMudDensity());
        vo.setFlowRate(detail.getFlowRate());
        vo.setOrificeConfig(detail.getOrificeConfig());
        vo.setPoppetConfig(detail.getPoppetConfig());
        vo.setSignalStrength(detail.getSignalStrength());
        vo.setVibrationLevel(detail.getVibrationLevel());
        vo.setVibrationOos(detail.getVibrationOos());
        vo.setDownholeInstrumentStatusDesc(detail.getDownholeInstrumentStatusDesc());
        vo.setWorkshopShopFinding(detail.getWorkshopShopFinding());
        vo.setFieldIncidentDescription(detail.getFieldIncidentDescription());
        vo.setFailureReasonDescription(detail.getFailureReasonDescription());
        vo.setImprovementPlanMeasures(detail.getImprovementPlanMeasures());

        // 复制维护相关字段
        vo.setMaintenanceLevel(detail.getMaintenanceLevel());
        vo.setPostUseMaintenanceCompletionDate(detail.getPostUseMaintenanceCompletionDate());
        vo.setReplacedMajorComponentsDesc(detail.getReplacedMajorComponentsDesc());

        // 初始化instruments列表，避免前端出现null
        vo.setInstruments(new ArrayList<>());
        return vo;
    }

    /**
     * 将 WellAnalysisInstruments 实体转换为 VO 对象
     */
    private WellAnalysisInstrumentsVO convertToInstrumentVO(WellAnalysisInstruments instrument) {
        if (instrument == null) {
            return null;
        }

        WellAnalysisInstrumentsVO vo = new WellAnalysisInstrumentsVO();
        BeanUtils.copyProperties(instrument, vo);
        return vo;
    }

    /**
     * 根据业务逻辑计算正确的jobStatus
     *
     * @param originalJobStatus 原始的jobStatus（来自JobInfo表）
     * @param currentRun 当前run号
     * @param maxRun 该井号+作业号组合的最大run号
     * @return 计算后的jobStatus
     */
    private Integer calculateCorrectJobStatus(Integer originalJobStatus, Integer currentRun, Integer maxRun) {
        if (originalJobStatus == null || currentRun == null || maxRun == null) {
            return originalJobStatus;
        }

        // 如果原始jobStatus是1（已完井），所有run都是已完趟，保持状态1
        if (originalJobStatus == 1) {
            return 1;
        }

        // 如果原始jobStatus是0（未完井），需要区分
        if (originalJobStatus == 0) {
            // 如果是最后一个run，保持状态0（未完趟）
            if (currentRun.equals(maxRun)) {
                return 0;
            } else {
                // 如果不是最后一个run，设置状态2（完趟）
                return 2;
            }
        }

        // 其他情况保持原状态
        return originalJobStatus;
    }

    /**
     * 更新井作业分析详情的单个富文本字段。
     * 此方法专门用于处理大型富文本内容，只更新指定的字段，避免一次性传输所有富文本内容导致的性能问题。
     *
     * @param id 井作业分析详情ID
     * @param fieldName 要更新的字段名称
     * @param fieldValue 新的字段值
     * @return 更新是否成功
     */
    @Override
    @Transactional
    public boolean updateRichTextField(Long id, String fieldName, String fieldValue) {
        if (id == null || !StringUtils.hasText(fieldName)) {
            log.error("更新富文本字段失败：ID或字段名为空");
            return false;
        }

        // 1. 检查记录是否存在
        WellOperationAnalysisDetail existingDetail = this.getById(id);
        if (existingDetail == null) {
            log.error("更新富文本字段失败：ID为 {} 的记录不存在", id);
            return false;
        }

        try {
            // 2. 创建一个只包含ID、字段名和更新时间的实体
            WellOperationAnalysisDetail detail = new WellOperationAnalysisDetail();
            detail.setId(id);
            detail.setUpdateTime(new Date());

            // 3. 根据字段名设置对应的值，并过滤base64图片内容
            switch (fieldName) {
                case "failureReasonDescription":
                    detail.setFailureReasonDescription(filterBase64ImagesFromRichText(fieldValue));
                    break;
                case "downholeInstrumentStatusDesc":
                    detail.setDownholeInstrumentStatusDesc(filterBase64ImagesFromRichText(fieldValue));
                    break;
                case "fieldIncidentDescription":
                    detail.setFieldIncidentDescription(filterBase64ImagesFromRichText(fieldValue));
                    break;
                case "workshopShopFinding":
                    detail.setWorkshopShopFinding(filterBase64ImagesFromRichText(fieldValue));
                    break;
                case "improvementPlanMeasures":
                    detail.setImprovementPlanMeasures(filterBase64ImagesFromRichText(fieldValue));
                    break;
                case "maintenanceLevel":
                    detail.setMaintenanceLevel(fieldValue);
                    break;
                case "postUseMaintenanceCompletionDate":
                    // 日期字段需要特殊处理
                    try {
                        if (StringUtils.hasText(fieldValue)) {
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                            detail.setPostUseMaintenanceCompletionDate(sdf.parse(fieldValue));
                        } else {
                            detail.setPostUseMaintenanceCompletionDate(null);
                        }
                    } catch (ParseException e) {
                        log.error("更新维护完成日期字段失败：日期格式错误 {}", fieldValue);
                        return false;
                    }
                    break;
                case "replacedMajorComponentsDesc":
                    detail.setReplacedMajorComponentsDesc(filterBase64ImagesFromRichText(fieldValue));
                    break;
                case "tripOutReasonType":
                    detail.setTripOutReasonType(filterBase64ImagesFromRichText(fieldValue));
                    break;
                default:
                    log.error("更新富文本字段失败：不支持的字段名 {}", fieldName);
                    return false;
            }

            // 4. 使用自定义的更新方法，确保只更新需要的字段
            boolean updateSuccess = baseMapper.updateDetail(detail) > 0;
            if (!updateSuccess) {
                log.error("更新富文本字段失败：ID为 {} 的记录更新失败", id);
                return false;
            }

            log.info("成功更新ID为 {} 的记录的 {} 字段", id, fieldName);
            return true;
        } catch (Exception e) {
            log.error("更新富文本字段异常：ID为 {}, 字段名 {}, 错误信息: {}", id, fieldName, e.getMessage(), e);
            throw new RuntimeException("更新富文本字段异常", e);
        }
    }

    /**
     * 查询井作业分析详情数据用于导出
     *
     * @param params 查询参数
     * @return 包含所有字段的井作业分析详情列表
     */
    @Override
    public List<WellOperationAnalysisDetailVO> listForExport(Map<String, Object> params) {
        // 1. 构建查询条件
        LambdaQueryWrapper<WellOperationAnalysisDetail> queryWrapper = new LambdaQueryWrapper<>();

        // 添加查询条件
        if (params != null) {
            // 井号（模糊查询）
            if (params.containsKey("wellNumber") && StringUtils.hasText(params.get("wellNumber").toString())) {
                queryWrapper.like(WellOperationAnalysisDetail::getWellNumber, params.get("wellNumber").toString());
            }

            // 趟次
            if (params.containsKey("runNumber") && params.get("runNumber") != null) {
                try {
                    Integer runNumber = Integer.parseInt(params.get("runNumber").toString());
                    queryWrapper.eq(WellOperationAnalysisDetail::getRun, runNumber);
                } catch (NumberFormatException e) {
                    log.warn("趟次参数格式错误: {}", params.get("runNumber"));
                }
            }

            // 井类别
            if (params.containsKey("serviceWellCategory") && StringUtils.hasText(params.get("serviceWellCategory").toString())) {
                queryWrapper.eq(WellOperationAnalysisDetail::getServiceWellCategory, params.get("serviceWellCategory").toString());
            }

            // 是否故障
            if (params.containsKey("isFailure") && StringUtils.hasText(params.get("isFailure").toString())) {
                queryWrapper.eq(WellOperationAnalysisDetail::getIsFailure, params.get("isFailure").toString());
            }

            // 入井日期范围
            if (params.containsKey("dateInStart") && params.get("dateInStart") != null) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    Date dateInStart = sdf.parse(params.get("dateInStart").toString());
                    queryWrapper.ge(WellOperationAnalysisDetail::getDateIn, dateInStart);
                } catch (Exception e) {
                    log.warn("入井开始日期参数格式错误: {}", params.get("dateInStart"));
                }
            }

            if (params.containsKey("dateInEnd") && params.get("dateInEnd") != null) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    Date dateInEnd = sdf.parse(params.get("dateInEnd").toString());
                    // 将结束日期设置为当天的23:59:59
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(dateInEnd);
                    calendar.set(Calendar.HOUR_OF_DAY, 23);
                    calendar.set(Calendar.MINUTE, 59);
                    calendar.set(Calendar.SECOND, 59);
                    queryWrapper.le(WellOperationAnalysisDetail::getDateIn, calendar.getTime());
                } catch (Exception e) {
                    log.warn("入井结束日期参数格式错误: {}", params.get("dateInEnd"));
                }
            }
        }

        // 按入井日期降序排序
        queryWrapper.orderByDesc(WellOperationAnalysisDetail::getDateIn);

        // 2. 查询主表数据
        List<WellOperationAnalysisDetail> detailList = this.list(queryWrapper);
        if (detailList == null || detailList.isEmpty()) {
            return new ArrayList<>();
        }

        // 3. 查询关联的工具信息
        List<Long> detailIds = detailList.stream().map(WellOperationAnalysisDetail::getId).collect(Collectors.toList());
        List<WellAnalysisInstruments> allInstruments = wellAnalysisInstrumentsService.listByDetailIds(detailIds);

        // 按detailId分组工具信息
        Map<Long, List<WellAnalysisInstruments>> instrumentsMap = allInstruments.stream()
                .collect(Collectors.groupingBy(WellAnalysisInstruments::getAnalysisDetailIdFk));

        // 4. 计算每个井号+作业号组合的最大run号，用于确定jobStatus
        Map<String, Integer> maxRunMap = detailList.stream()
            .collect(Collectors.groupingBy(
                detail -> detail.getWellNumber() + "|" + detail.getJobNumber(),
                Collectors.mapping(WellOperationAnalysisDetail::getRun,
                    Collectors.maxBy(Integer::compareTo))
            ))
            .entrySet().stream()
            .collect(Collectors.toMap(
                Map.Entry::getKey,
                entry -> entry.getValue().orElse(0)
            ));

        // 5. 构建VO对象
        List<WellOperationAnalysisDetailVO> resultList = new ArrayList<>(detailList.size());
        for (WellOperationAnalysisDetail detail : detailList) {
            // 计算正确的jobStatus
            String wellJobKey = detail.getWellNumber() + "|" + detail.getJobNumber();
            Integer maxRun = maxRunMap.get(wellJobKey);
            Integer correctJobStatus = calculateCorrectJobStatus(detail.getJobStatus(), detail.getRun(), maxRun);

            WellOperationAnalysisDetailVO vo = new WellOperationAnalysisDetailVO();
            BeanUtils.copyProperties(detail, vo);
            // 设置重新计算的jobStatus
            vo.setJobStatus(correctJobStatus);

            // 设置工具信息
            List<WellAnalysisInstruments> instruments = instrumentsMap.getOrDefault(detail.getId(), new ArrayList<>());
            List<WellAnalysisInstrumentsVO> instrumentVOs = instruments.stream()
                    .map(this::convertToInstrumentVO)
                    .collect(Collectors.toList());
            vo.setInstruments(instrumentVOs);

            resultList.add(vo);
        }

        return resultList;
    }

    /**
     * 导出井作业分析详情数据到Excel
     *
     * @param response HTTP响应对象，用于写入Excel文件
     * @param params 导出参数，包含查询条件
     * @throws IOException 如果导出过程中发生IO异常
     */
    @Override
    public void exportWellAnalysisDetails(HttpServletResponse response, Map<String, Object> params) throws IOException {
        // 1. 查询数据
        List<WellOperationAnalysisDetailVO> dataList = this.listForExport(params);
        if (dataList == null || dataList.isEmpty()) {
            // 如果没有数据，返回空的Excel
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("井作业分析详情_" + new SimpleDateFormat("yyyy-MM-dd").format(new Date()), "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream()).sheet("无数据").doWrite(new ArrayList<>());
            return;
        }

        // 2. 按入井日期的年份分组
        Map<String, List<WellOperationAnalysisDetailVO>> groupedData = new LinkedHashMap<>();
        SimpleDateFormat yearFormat = new SimpleDateFormat("yyyy");

        for (WellOperationAnalysisDetailVO vo : dataList) {
            String yearKey = "未知年份";
            if (vo.getDateIn() != null) {
                yearKey = yearFormat.format(vo.getDateIn()) + "年";
            }

            if (!groupedData.containsKey(yearKey)) {
                groupedData.put(yearKey, new ArrayList<>());
            }
            groupedData.get(yearKey).add(vo);
        }

        // 3. 设置响应头
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("井作业分析详情_" + new SimpleDateFormat("yyyy-MM-dd").format(new Date()), "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

        // 4. 创建Excel工作簿
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            // 创建各种单元格样式
            // 标题样式（第一行）- 英文Times New Roman，中文默认，粗体，字号20，无背景颜色
            XSSFCellStyle titleStyle = workbook.createCellStyle();
            titleStyle.setBorderBottom(BorderStyle.THIN);
            titleStyle.setBorderLeft(BorderStyle.THIN);
            titleStyle.setBorderRight(BorderStyle.THIN);
            titleStyle.setBorderTop(BorderStyle.THIN);
            titleStyle.setAlignment(org.apache.poi.ss.usermodel.HorizontalAlignment.CENTER);
            titleStyle.setVerticalAlignment(org.apache.poi.ss.usermodel.VerticalAlignment.CENTER);
            
            XSSFFont titleFont = workbook.createFont();
            titleFont.setFontName("Times New Roman"); // 英文使用Times New Roman
            titleFont.setBold(true);
            titleFont.setFontHeightInPoints((short) 20);
            titleStyle.setFont(titleFont);

            // 分组标题样式（第二行）- 等线字体，字号12，无背景颜色
            XSSFCellStyle groupHeaderStyle = workbook.createCellStyle();
            groupHeaderStyle.setBorderBottom(BorderStyle.THIN);
            groupHeaderStyle.setBorderLeft(BorderStyle.THIN);
            groupHeaderStyle.setBorderRight(BorderStyle.THIN);
            groupHeaderStyle.setBorderTop(BorderStyle.THIN);
            groupHeaderStyle.setAlignment(org.apache.poi.ss.usermodel.HorizontalAlignment.CENTER);
            groupHeaderStyle.setVerticalAlignment(org.apache.poi.ss.usermodel.VerticalAlignment.CENTER);

            XSSFFont groupHeaderFont = workbook.createFont();
            groupHeaderFont.setFontName("等线"); // 等线字体
            groupHeaderFont.setBold(true);
            groupHeaderFont.setFontHeightInPoints((short) 12);
            groupHeaderStyle.setFont(groupHeaderFont);

            // 字段标题样式（第三行）- 橙色背景(#de8344)，白色字体，等线11号粗体
            XSSFCellStyle headerStyle = workbook.createCellStyle();
            XSSFColor orangeColor = new XSSFColor(new java.awt.Color(0xde, 0x83, 0x44), null); // #de8344
            headerStyle.setFillForegroundColor(orangeColor);
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            headerStyle.setBorderBottom(BorderStyle.THIN);
            headerStyle.setBorderLeft(BorderStyle.THIN);
            headerStyle.setBorderRight(BorderStyle.THIN);
            headerStyle.setBorderTop(BorderStyle.THIN);
            headerStyle.setAlignment(org.apache.poi.ss.usermodel.HorizontalAlignment.CENTER);
            headerStyle.setVerticalAlignment(org.apache.poi.ss.usermodel.VerticalAlignment.CENTER);
            headerStyle.setWrapText(true);

            XSSFFont headerFont = workbook.createFont();
            headerFont.setFontName("等线"); // 等线字体
            headerFont.setBold(true);
            headerFont.setFontHeightInPoints((short) 11); // 11号字体
            headerFont.setColor(IndexedColors.WHITE.getIndex()); // 白色字体
            headerStyle.setFont(headerFont);

            // 绿色列名样式 - 使用自定义RGB颜色 (#7eab55)
            XSSFCellStyle greenHeaderStyle = workbook.createCellStyle();
            
            // 创建自定义绿色 #7eab55
            XSSFColor greenColor = new XSSFColor(new java.awt.Color(0x7e, 0xab, 0x55), null); // #7eab55
            greenHeaderStyle.setFillForegroundColor(greenColor);
            greenHeaderStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            greenHeaderStyle.setBorderBottom(BorderStyle.THIN);
            greenHeaderStyle.setBorderLeft(BorderStyle.THIN);
            greenHeaderStyle.setBorderRight(BorderStyle.THIN);
            greenHeaderStyle.setBorderTop(BorderStyle.THIN);
            greenHeaderStyle.setAlignment(org.apache.poi.ss.usermodel.HorizontalAlignment.CENTER);
            greenHeaderStyle.setVerticalAlignment(org.apache.poi.ss.usermodel.VerticalAlignment.CENTER);
            greenHeaderStyle.setWrapText(true);

            XSSFFont greenHeaderFont = workbook.createFont();
            greenHeaderFont.setFontName("等线");
            greenHeaderFont.setBold(true);
            greenHeaderFont.setFontHeightInPoints((short) 11);
            greenHeaderFont.setColor(IndexedColors.WHITE.getIndex());
            greenHeaderStyle.setFont(greenHeaderFont);

            // 天蓝色列名样式 - 使用自定义RGB颜色 (#6a99d0)
            XSSFCellStyle lightBlueHeaderStyle = workbook.createCellStyle();
            
            // 创建自定义天蓝色 #6a99d0
            XSSFColor lightBlueColor = new XSSFColor(new java.awt.Color(0x6a, 0x99, 0xd0), null); // #6a99d0
            lightBlueHeaderStyle.setFillForegroundColor(lightBlueColor);
            lightBlueHeaderStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            lightBlueHeaderStyle.setBorderBottom(BorderStyle.THIN);
            lightBlueHeaderStyle.setBorderLeft(BorderStyle.THIN);
            lightBlueHeaderStyle.setBorderRight(BorderStyle.THIN);
            lightBlueHeaderStyle.setBorderTop(BorderStyle.THIN);
            lightBlueHeaderStyle.setAlignment(org.apache.poi.ss.usermodel.HorizontalAlignment.CENTER);
            lightBlueHeaderStyle.setVerticalAlignment(org.apache.poi.ss.usermodel.VerticalAlignment.CENTER);
            lightBlueHeaderStyle.setWrapText(true);

            XSSFFont lightBlueHeaderFont = workbook.createFont();
            lightBlueHeaderFont.setFontName("等线");
            lightBlueHeaderFont.setBold(true);
            lightBlueHeaderFont.setFontHeightInPoints((short) 11);
            lightBlueHeaderFont.setColor(IndexedColors.WHITE.getIndex());
            lightBlueHeaderStyle.setFont(lightBlueHeaderFont);

            // 深蓝色列名样式 - 用于MWD失效、At Bit近钻失效、T-Well Link失效 (#2f6eba)
            XSSFCellStyle darkBlueHeaderStyle = workbook.createCellStyle();
            
            // 创建自定义深蓝色 #2f6eba
            XSSFColor darkBlueColor = new XSSFColor(new java.awt.Color(0x2f, 0x6e, 0xba), null); // #2f6eba
            darkBlueHeaderStyle.setFillForegroundColor(darkBlueColor);
            darkBlueHeaderStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            darkBlueHeaderStyle.setBorderBottom(BorderStyle.THIN);
            darkBlueHeaderStyle.setBorderLeft(BorderStyle.THIN);
            darkBlueHeaderStyle.setBorderRight(BorderStyle.THIN);
            darkBlueHeaderStyle.setBorderTop(BorderStyle.THIN);
            darkBlueHeaderStyle.setAlignment(org.apache.poi.ss.usermodel.HorizontalAlignment.CENTER);
            darkBlueHeaderStyle.setVerticalAlignment(org.apache.poi.ss.usermodel.VerticalAlignment.CENTER);
            darkBlueHeaderStyle.setWrapText(true);

            XSSFFont darkBlueHeaderFont = workbook.createFont();
            darkBlueHeaderFont.setFontName("等线");
            darkBlueHeaderFont.setBold(true);
            darkBlueHeaderFont.setFontHeightInPoints((short) 11);
            darkBlueHeaderFont.setColor(IndexedColors.WHITE.getIndex());
            darkBlueHeaderStyle.setFont(darkBlueHeaderFont);

            // 数据单元格样式 - 居中对齐
            XSSFCellStyle cellStyle = workbook.createCellStyle();
            cellStyle.setBorderBottom(BorderStyle.THIN);
            cellStyle.setBorderLeft(BorderStyle.THIN);
            cellStyle.setBorderRight(BorderStyle.THIN);
            cellStyle.setBorderTop(BorderStyle.THIN);
            cellStyle.setAlignment(org.apache.poi.ss.usermodel.HorizontalAlignment.CENTER); // 水平居中
            cellStyle.setVerticalAlignment(org.apache.poi.ss.usermodel.VerticalAlignment.CENTER);
            cellStyle.setWrapText(true);

            // 井名列特殊样式（宋体、粗体）
            XSSFCellStyle wellNameStyle = workbook.createCellStyle();
            wellNameStyle.cloneStyleFrom(cellStyle);
            XSSFFont wellNameFont = workbook.createFont();
            wellNameFont.setFontName("宋体");
            wellNameFont.setBold(true);
            wellNameStyle.setFont(wellNameFont);

            // 服务井类别样式（宋体11号）
            XSSFCellStyle wellCategoryTextStyle = workbook.createCellStyle();
            wellCategoryTextStyle.cloneStyleFrom(cellStyle);
            XSSFFont wellCategoryFont = workbook.createFont();
            wellCategoryFont.setFontName("宋体");
            wellCategoryFont.setFontHeightInPoints((short) 11);
            wellCategoryTextStyle.setFont(wellCategoryFont);

            // 温度条件样式
            // 正常温度样式 (<=125)
            XSSFCellStyle normalTempStyle = workbook.createCellStyle();
            normalTempStyle.cloneStyleFrom(cellStyle);
            
            // 高温度样式 (>125 && <=150)
            XSSFCellStyle highTempStyle = workbook.createCellStyle();
            highTempStyle.cloneStyleFrom(cellStyle);
            XSSFFont highTempFont = workbook.createFont();
            highTempFont.setColor(IndexedColors.RED.getIndex());
            highTempFont.setBold(true);
            highTempStyle.setFont(highTempFont);
            
            // 极高温度样式 (>150)
            XSSFCellStyle veryHighTempStyle = workbook.createCellStyle();
            veryHighTempStyle.cloneStyleFrom(cellStyle);
            veryHighTempStyle.setFillForegroundColor(IndexedColors.RED.getIndex());
            veryHighTempStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            XSSFFont veryHighTempFont = workbook.createFont();
            veryHighTempFont.setColor(IndexedColors.WHITE.getIndex());
            veryHighTempFont.setBold(true);
            veryHighTempStyle.setFont(veryHighTempFont);

            // 振动级别条件样式 - Times New Roman 11号字体，背景颜色
            // Low - 绿色背景 (#4fad5b)
            XSSFCellStyle lowVibrationStyle = workbook.createCellStyle();
            lowVibrationStyle.cloneStyleFrom(cellStyle);
            XSSFColor lowVibrationColor = new XSSFColor(new java.awt.Color(0x4f, 0xad, 0x5b), null); // #4fad5b
            lowVibrationStyle.setFillForegroundColor(lowVibrationColor);
            lowVibrationStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            XSSFFont lowVibrationFont = workbook.createFont();
            lowVibrationFont.setFontName("Times New Roman");
            lowVibrationFont.setFontHeightInPoints((short) 11);
            lowVibrationStyle.setFont(lowVibrationFont);
            
            // Medium - 黄色背景 (#ffff54)
            XSSFCellStyle mediumVibrationStyle = workbook.createCellStyle();
            mediumVibrationStyle.cloneStyleFrom(cellStyle);
            XSSFColor mediumVibrationColor = new XSSFColor(new java.awt.Color(0xff, 0xff, 0x54), null); // #ffff54
            mediumVibrationStyle.setFillForegroundColor(mediumVibrationColor);
            mediumVibrationStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            XSSFFont mediumVibrationFont = workbook.createFont();
            mediumVibrationFont.setFontName("Times New Roman");
            mediumVibrationFont.setFontHeightInPoints((short) 11);
            mediumVibrationStyle.setFont(mediumVibrationFont);
            
            // High - 红色背景 (#ea3323)
            XSSFCellStyle highVibrationStyle = workbook.createCellStyle();
            highVibrationStyle.cloneStyleFrom(cellStyle);
            XSSFColor highVibrationColor = new XSSFColor(new java.awt.Color(0xea, 0x33, 0x23), null); // #ea3323
            highVibrationStyle.setFillForegroundColor(highVibrationColor);
            highVibrationStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            XSSFFont highVibrationFont = workbook.createFont();
            highVibrationFont.setFontName("Times New Roman");
            highVibrationFont.setFontHeightInPoints((short) 11);
            highVibrationFont.setColor(IndexedColors.WHITE.getIndex()); // 红色背景用白色字体
            highVibrationStyle.setFont(highVibrationFont);
            
            // Very High - 深红色背景 (#8B0000)
            XSSFCellStyle veryHighVibrationStyle = workbook.createCellStyle();
            veryHighVibrationStyle.cloneStyleFrom(cellStyle);
            XSSFColor veryHighVibrationColor = new XSSFColor(new java.awt.Color(0x8B, 0x00, 0x00), null); // #8B0000
            veryHighVibrationStyle.setFillForegroundColor(veryHighVibrationColor);
            veryHighVibrationStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            XSSFFont veryHighVibrationFont = workbook.createFont();
            veryHighVibrationFont.setFontName("Times New Roman");
            veryHighVibrationFont.setFontHeightInPoints((short) 11);
            veryHighVibrationFont.setColor(IndexedColors.WHITE.getIndex()); // 深红色背景用白色字体
            veryHighVibrationFont.setBold(true); // 粗体以增强视觉效果
            veryHighVibrationStyle.setFont(veryHighVibrationFont);

            // 井下仪器情况条件样式
            // 正常 - 绿色字体(#4fad5b)，浅绿背景(#e4efdc)
            XSSFCellStyle normalInstrumentStyle = workbook.createCellStyle();
            normalInstrumentStyle.cloneStyleFrom(cellStyle);
            XSSFColor normalInstrumentBgColor = new XSSFColor(new java.awt.Color(0xe4, 0xef, 0xdc), null); // #e4efdc
            normalInstrumentStyle.setFillForegroundColor(normalInstrumentBgColor);
            normalInstrumentStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            
            XSSFFont normalInstrumentFont = workbook.createFont();
            normalInstrumentFont.setColor(new XSSFColor(new java.awt.Color(0x4f, 0xad, 0x5b), null)); // #4fad5b
            normalInstrumentStyle.setFont(normalInstrumentFont);
            
            // 异常 - 红色字体(#ea3323)
            XSSFCellStyle abnormalInstrumentStyle = workbook.createCellStyle();
            abnormalInstrumentStyle.cloneStyleFrom(cellStyle);
            
            XSSFFont abnormalInstrumentFont = workbook.createFont();
            abnormalInstrumentFont.setColor(new XSSFColor(new java.awt.Color(0xea, 0x33, 0x23), null)); // #ea3323
            abnormalInstrumentStyle.setFont(abnormalInstrumentFont);

            // 服务井类别条件样式
            // 旋转导向 - 默认样式（无颜色）
            XSSFCellStyle rotarySteerableStyle = workbook.createCellStyle();
            rotarySteerableStyle.cloneStyleFrom(cellStyle);
            
            // 非近钻井 - 绿色背景 (#9fce63)
            XSSFCellStyle nonAtBitStyle = workbook.createCellStyle();
            nonAtBitStyle.cloneStyleFrom(cellStyle);
            XSSFColor nonAtBitColor = new XSSFColor(new java.awt.Color(0x9f, 0xce, 0x63), null); // #9fce63
            nonAtBitStyle.setFillForegroundColor(nonAtBitColor);
            nonAtBitStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            
            // 近钻井 - 橙色背景 (#f5c242)
            XSSFCellStyle atBitStyle = workbook.createCellStyle();
            atBitStyle.cloneStyleFrom(cellStyle);
            XSSFColor atBitColor = new XSSFColor(new java.awt.Color(0xf5, 0xc2, 0x42), null); // #f5c242
            atBitStyle.setFillForegroundColor(atBitColor);
            atBitStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

            // 为每个日期组创建一个sheet
            for (Map.Entry<String, List<WellOperationAnalysisDetailVO>> entry : groupedData.entrySet()) {
                String sheetName = entry.getKey();
                List<WellOperationAnalysisDetailVO> sheetData = entry.getValue();

                // 确保sheet名称不超过31个字符
                if (sheetName.length() > 31) {
                    sheetName = sheetName.substring(0, 31);
                }

                XSSFSheet sheet = workbook.createSheet(sheetName);

                // 创建三层表头
                createMultiLevelHeaders(sheet, titleStyle, groupHeaderStyle, headerStyle, greenHeaderStyle, lightBlueHeaderStyle, darkBlueHeaderStyle);

                // 设置列宽
                setupColumnWidths(sheet);

                // 创建样式数组，便于传递给数据填充方法
                XSSFCellStyle[] conditionalStyles = {
                    cellStyle, normalTempStyle, highTempStyle, veryHighTempStyle,
                    lowVibrationStyle, mediumVibrationStyle, highVibrationStyle, veryHighVibrationStyle,
                    normalInstrumentStyle, abnormalInstrumentStyle,
                    rotarySteerableStyle, nonAtBitStyle, atBitStyle
                };

                // 填充数据
                int rowIndex = 3; // 数据从第4行开始（索引为3）
                int sequenceNumber = 1; // 序号从1开始
                for (WellOperationAnalysisDetailVO vo : sheetData) {
                    XSSFRow dataRow = sheet.createRow(rowIndex++);
                    // 设置统一行高
                    dataRow.setHeight((short) 400);

                    // 获取工具信息，用于后续填充
                    Map<String, String> toolSerialNumbers = new HashMap<>();
                    if (vo.getInstruments() != null && !vo.getInstruments().isEmpty()) {
                        for (WellAnalysisInstrumentsVO instrument : vo.getInstruments()) {
                            String invName = instrument.getInvName();
                            String serialNumber = instrument.getSerialNumber();

                            if (invName != null && serialNumber != null) {
                                // 将invName按照实际数据库值进行分类映射
                                String mappedKey = mapInvNameToCategory(invName);
                                
                                // 如果已经有该类型的工具，则追加序列号
                                if (toolSerialNumbers.containsKey(mappedKey)) {
                                    String existingSerial = toolSerialNumbers.get(mappedKey);
                                    toolSerialNumbers.put(mappedKey, existingSerial + ", " + serialNumber);
                                } else {
                                    toolSerialNumbers.put(mappedKey, serialNumber);
                                }
                            }
                        }
                    }

                    int colIndex = 0;

                    // 按照模板顺序填充数据
                    
                    // 基本信息 (A-J)
                    setCellValue(dataRow.createCell(colIndex++), sequenceNumber++, cellStyle); // 序号
                    
                    // 服务井类别 - 使用条件样式和宋体字体
                    XSSFCellStyle wellCategoryStyle = getServiceWellCategoryStyleWithFont(vo.getServiceWellCategory(), conditionalStyles, wellCategoryTextStyle, workbook);
                    setCellValue(dataRow.createCell(colIndex++), vo.getServiceWellCategory(), wellCategoryStyle);
                    
                    setCellValue(dataRow.createCell(colIndex++), vo.getWellNumber(), wellNameStyle); // 井名 - 使用特殊样式
                    setCellValue(dataRow.createCell(colIndex++), vo.getBlock(), cellStyle); // 区块
                    
                    // 温度 - 使用条件样式
                    XSSFCellStyle tempStyle = getTemperatureStyle(vo.getTemperatureValue(), conditionalStyles);
                    setCellValue(dataRow.createCell(colIndex++), vo.getTemperatureValue(), tempStyle); // 温度
                    
                    // 振动级别 - 使用条件样式
                    XSSFCellStyle vibrationStyle = getVibrationStyle(vo.getVibrationLevel(), conditionalStyles);
                    setCellValue(dataRow.createCell(colIndex++), vo.getVibrationLevel(), vibrationStyle); // 振动级别
                    
                    setCellValue(dataRow.createCell(colIndex++), vo.getVibrationOos(), cellStyle); // 是否振动超标OOS
                    setCellValue(dataRow.createCell(colIndex++), vo.getExceededConditionDescription(), cellStyle); // 超标工况描述
                    setCellValue(dataRow.createCell(colIndex++), vo.getBitSize(), cellStyle); // 钻头尺寸
                    setCellValue(dataRow.createCell(colIndex++), vo.getRun(), cellStyle); // 趟次

                    // 泥浆信息/信号衰减计算 (K-S)
                    setCellValue(dataRow.createCell(colIndex++), vo.getMudType(), cellStyle); // 泥浆类型
                    setCellValue(dataRow.createCell(colIndex++), vo.getMudDensity(), cellStyle); // 密度
                    setCellValue(dataRow.createCell(colIndex++), vo.getFlowRate(), cellStyle); // 排量
                    setCellValue(dataRow.createCell(colIndex++), vo.getConstructionStartDepthMd(), cellStyle); // 施工开始井深
                    setCellValue(dataRow.createCell(colIndex++), vo.getConstructionEndDepthMd(), cellStyle); // 施工结束井深
                    setCellValue(dataRow.createCell(colIndex++), vo.getFootageMd(), cellStyle); // 进尺
                    setCellValue(dataRow.createCell(colIndex++), vo.getOrificeConfig(), cellStyle); // Orifice
                    setCellValue(dataRow.createCell(colIndex++), vo.getPoppetConfig(), cellStyle); // Poppet
                    setCellValue(dataRow.createCell(colIndex++), vo.getSignalStrength(), cellStyle); // 信号强度

                    // 仪器编号 (T-AE)
                    setCellValue(dataRow.createCell(colIndex++), toolSerialNumbers.getOrDefault("近钻", "-"), cellStyle); // 近钻序列号
                    setCellValue(dataRow.createCell(colIndex++), toolSerialNumbers.getOrDefault("发射器", "-"), cellStyle); // 发射器序列号
                    setCellValue(dataRow.createCell(colIndex++), toolSerialNumbers.getOrDefault("接收器", "-"), cellStyle); // 接收器序列号
                    setCellValue(dataRow.createCell(colIndex++), toolSerialNumbers.getOrDefault("近钻Gamma", "-"), cellStyle); // 近钻Gamma序列号
                    setCellValue(dataRow.createCell(colIndex++), toolSerialNumbers.getOrDefault("绝缘短节", "-"), cellStyle); // 绝缘短节序列号
                    setCellValue(dataRow.createCell(colIndex++), toolSerialNumbers.getOrDefault("脉冲", "-"), cellStyle); // 脉冲序列号
                    setCellValue(dataRow.createCell(colIndex++), toolSerialNumbers.getOrDefault("底部", "-"), cellStyle); // 底部序列号
                    setCellValue(dataRow.createCell(colIndex++), toolSerialNumbers.getOrDefault("探管/方位探管", "-"), cellStyle); // 探管/方位探管序列号
                    setCellValue(dataRow.createCell(colIndex++), toolSerialNumbers.getOrDefault("自然/方位Gamma", "-"), cellStyle); // 自然/方位Gamma序列号
                    setCellValue(dataRow.createCell(colIndex++), toolSerialNumbers.getOrDefault("SU", "-"), cellStyle); // SU
                    setCellValue(dataRow.createCell(colIndex++), toolSerialNumbers.getOrDefault("MWD", "-"), cellStyle); // MWD
                    setCellValue(dataRow.createCell(colIndex++), toolSerialNumbers.getOrDefault("LCP", "-"), cellStyle); // LCP

                    // 时间信息 (AE-AG)
                    setCellValue(dataRow.createCell(colIndex++), vo.getDateIn(), cellStyle); // 入井时间
                    setCellValue(dataRow.createCell(colIndex++), vo.getFailureTime(), cellStyle); // 失效时间
                    setCellValue(dataRow.createCell(colIndex++), vo.getDateOut(), cellStyle); // 出井时间

                    // 井下情况 (AH-AI)
                    // 井下仪器情况 - 使用条件样式
                    XSSFCellStyle instrumentStatusStyle = getInstrumentStatusStyle(vo.getDownholeInstrumentStatusDesc(), conditionalStyles);
                    setCellValue(dataRow.createCell(colIndex++), stripHtml(vo.getDownholeInstrumentStatusDesc()), instrumentStatusStyle);
                    setCellValue(dataRow.createCell(colIndex++), vo.getTripOutReasonType(), cellStyle); // 起钻原因

                    // 失效统计分析 (AJ-AP)
                    // 是否故障及趟数
                    String failureAndRun = "";
                    String isFailure = vo.getIsFailure();
                    if ("是".equals(isFailure) || "1".equals(isFailure) || "true".equalsIgnoreCase(isFailure)) {
                        failureAndRun = "是，趟次" + vo.getRun();
                    } else {
                        failureAndRun = "否";
                    }
                    setCellValue(dataRow.createCell(colIndex++), failureAndRun, cellStyle);
                    
                    // 根据consolidatedFailureType判断失效类型
                    String consolidatedFailureType = vo.getConsolidatedFailureType();
                    boolean isMwdFailure = consolidatedFailureType != null && consolidatedFailureType.contains("MWD");
                    boolean isAtBitFailure = consolidatedFailureType != null && consolidatedFailureType.contains("AT_BIT");
                    boolean isRssFailure = consolidatedFailureType != null && 
                                          (consolidatedFailureType.contains("RSS") || consolidatedFailureType.contains("Lucida"));

                    setCellValue(dataRow.createCell(colIndex++), isMwdFailure ? "是" : "否", cellStyle); // MWD失效
                    setCellValue(dataRow.createCell(colIndex++), isAtBitFailure ? "是" : "否", cellStyle); // At Bit 近钻失效
                    setCellValue(dataRow.createCell(colIndex++), isRssFailure ? "是" : "否", cellStyle); // T-Well Link失效
                    setCellValue(dataRow.createCell(colIndex++), vo.getTripInHours(), cellStyle); // 入井小时
                    setCellValue(dataRow.createCell(colIndex++), vo.getCirculatingHours(), cellStyle); // 循环小时
                    setCellValue(dataRow.createCell(colIndex++), vo.getHoursToFailure(), cellStyle); // 多少时间失效

                    // 详细备注 (AQ-AZ)
                    setCellValue(dataRow.createCell(colIndex++), stripHtml(vo.getFieldIncidentDescription()), cellStyle); // 现场端主要情况
                    setCellValue(dataRow.createCell(colIndex++), stripHtml(vo.getWorkshopShopFinding()), cellStyle); // 维护/研发端检查发现
                    setCellValue(dataRow.createCell(colIndex++), vo.getMaintenanceLevel(), cellStyle); // 维护级别
                    setCellValue(dataRow.createCell(colIndex++), vo.getPostUseMaintenanceCompletionDate(), cellStyle); // 使用后维护完成日期
                    setCellValue(dataRow.createCell(colIndex++), vo.getReplacedMajorComponentsDesc(), cellStyle); // 更换主要部件
                    setCellValue(dataRow.createCell(colIndex++), vo.getSpecificFailedAssemblyCategory(), cellStyle); // 具体失效总成分类
                    setCellValue(dataRow.createCell(colIndex++), vo.getFailedComponentInAssemblyCategory(), cellStyle); // 失效总成的部件分类
                    setCellValue(dataRow.createCell(colIndex++), vo.getPrimaryFailureReasonTag(), cellStyle); // 失效主要原因标签
                    setCellValue(dataRow.createCell(colIndex++), stripHtml(vo.getFailureReasonDescription()), cellStyle); // 根本原因分析
                    setCellValue(dataRow.createCell(colIndex++), stripHtml(vo.getImprovementPlanMeasures()), cellStyle); // 改进计划/措施
                }
            }

            // 5. 写入响应流
            workbook.write(response.getOutputStream());
        } catch (Exception e) {
            log.error("导出井作业分析详情数据失败", e);
            throw new IOException("导出井作业分析详情数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 设置单元格值
     */
    private void setCellValue(XSSFCell cell, Object value, XSSFCellStyle style) {
        cell.setCellStyle(style);
        if (value == null) {
            cell.setCellValue("");
            return;
        }

        if (value instanceof String) {
            cell.setCellValue((String) value);
        } else if (value instanceof Integer) {
            cell.setCellValue((Integer) value);
        } else if (value instanceof Long) {
            cell.setCellValue((Long) value);
        } else if (value instanceof Double) {
            cell.setCellValue((Double) value);
        } else if (value instanceof Float) {
            cell.setCellValue((Float) value);
        } else if (value instanceof BigDecimal) {
            cell.setCellValue(((BigDecimal) value).doubleValue());
        } else if (value instanceof Date) {
            cell.setCellValue(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format((Date) value));
        } else if (value instanceof Boolean) {
            cell.setCellValue((Boolean) value ? "是" : "否");
        } else {
            cell.setCellValue(value.toString());
        }
    }

    /**
     * 去除HTML标签
     */
    private String stripHtml(String html) {
        if (html == null || html.isEmpty()) {
            return "";
        }

        // 使用简单的正则表达式去除HTML标签
        return html.replaceAll("<[^>]*>", "");
    }

    /**
     * 将数据库中的 invName 映射到Excel导出中使用的分类标准名称
     * 根据数据库中实际的字段值进行精确映射
     * 
     * @param invName 数据库中的实际 invName 值
     * @return 标准化的分类名称
     */
    private String mapInvNameToCategory(String invName) {
        if (invName == null || invName.trim().isEmpty()) {
            return "未知类型";
        }
        
        // 根据数据库中的实际值进行精确映射
        switch (invName.trim()) {
            // 近钻头类工具
            case "近钻头":
                return "近钻";
            
            // 发射器类工具 (数据库中暂无，保留)
            case "发射器":
                return "发射器";
            
            // 接收器类工具
            case "接收器":
                return "接收器";
            
            // At Bit Gamma类工具
            case "At Bit Gamma":
                return "近钻Gamma";
            
            // At Bit EC类工具 - 映射到发射器列
            case "At Bit EC":
                return "发射器";

            // 绝缘短节类工具
            case "绝缘短节":
            case "绝缘短接":
                return "绝缘短节";

            // 脉冲类工具
            case "脉冲器":
            case "脉冲":
                return "脉冲";
            
            // 底部总成类工具
            case "底部总成":
            case "底部总成Bottom End Assembly":
                return "底部";
            
            // 探管/方位探管类工具
            case "探管/方位探管":
            case "定向探管":
            case "方位探管":
            case "探管":
            case "探管ELECTRONICS":
            case "DM":
                return "探管/方位探管";
            
            // 自然/方位Gamma类工具
            case "自然/方位Gamma":
                return "自然/方位Gamma";
            
            // RSS相关工具
            case "LBSU":
            case "SU":
                return "SU";
            
            case "LBMWD":
            case "MWD":
                return "MWD";
            
            case "LBLCP":
            case "LCP":
                return "LCP";
            
            // 如果都不匹配，返回原始名称
            default:
                return invName;
        }
    }

    /**
     * 创建多层表头（三层：标题行、分组行、字段行）
     *
     * @param sheet Excel工作表
     * @param titleStyle 标题样式
     * @param groupHeaderStyle 分组标题样式
     * @param headerStyle 字段标题样式
     */
    private void createMultiLevelHeaders(XSSFSheet sheet, XSSFCellStyle titleStyle, 
                                       XSSFCellStyle groupHeaderStyle, XSSFCellStyle headerStyle,
                                       XSSFCellStyle greenHeaderStyle, XSSFCellStyle lightBlueHeaderStyle,
                                       XSSFCellStyle darkBlueHeaderStyle) {
        // 第一行：主标题
        XSSFRow titleRow = sheet.createRow(0);
        titleRow.setHeight((short) 400); // 统一行高
        XSSFCell titleCell = titleRow.createCell(0);
        titleCell.setCellValue("Tartan仪器作业可靠性追踪");
        titleCell.setCellStyle(titleStyle);
        
        // 合并标题行单元格 (A1:BA1)
        sheet.addMergedRegion(new org.apache.poi.ss.util.CellRangeAddress(0, 0, 0, 52));

        // 第二行：分组标题
        XSSFRow groupRow = sheet.createRow(1);
        groupRow.setHeight((short) 400); // 统一行高
        
        // 分组信息定义：[起始列, 结束列, 标题文本]
        Object[][] groupInfo = {
            {0, 9, "基本信息"},           // A2:J2 (10列)
            {10, 18, "泥浆信息/信号衰减计算"}, // K2:S2 (9列)
            {19, 30, "仪器编号"},         // T2:AE2 (12列)
            {31, 33, "时间信息"},         // AF2:AH2 (3列)
            {34, 35, "井下情况"},         // AI2:AJ2 (2列)
            {36, 42, "失效统计分析"},       // AK2:AQ2 (7列)
            {43, 52, "详细备注"}          // AR2:BA2 (10列)
        };
        
        for (Object[] group : groupInfo) {
            int startCol = (Integer) group[0];
            int endCol = (Integer) group[1];
            String title = (String) group[2];
            
            XSSFCell groupCell = groupRow.createCell(startCol);
            groupCell.setCellValue(title);
            groupCell.setCellStyle(groupHeaderStyle);
            
            // 合并单元格
            if (startCol != endCol) {
                sheet.addMergedRegion(new org.apache.poi.ss.util.CellRangeAddress(1, 1, startCol, endCol));
            }
        }

        // 第三行：字段标题
        XSSFRow fieldRow = sheet.createRow(2);
        fieldRow.setHeight((short) 600); // 第三行较高的行高
        
        // 字段标题数组（按模板顺序）
        String[] fieldHeaders = {
            // 基本信息 (A-J)
            "序号", "服务井类别", "井名", "区块", "温度", "振动级别", "是否振动超标OOS", "超标工况描述", "钻头尺寸", "趟次",
            
            // 泥浆信息/信号衰减计算 (K-S)
            "泥浆类型", "密度", "排量", "施工开始井深", "施工结束井深", "进尺", "Orifice", "Poppet", "信号强度",
            
            // 仪器编号 (T-AE)
            "近钻序列号", "发射器序列号", "接收器序列号", "近钻Gamma序列号", "绝缘短节序列号", "脉冲序列号", "底部序列号",
            "探管/方位探管序列号", "自然/方位Gamma序列号", "SU", "MWD", "LCP",
            
            // 时间信息 (AE-AG)
            "入井时间", "失效时间", "出井时间",
            
            // 井下情况 (AH-AI)
            "井下仪器情况", "起钻原因",
            
            // 失效统计分析 (AJ-AP)
            "是否\n故障及趟数", "MWD失效", "At Bit 近钻失效", "T-Well Link失效", "入井小时", "循环小时", "多少时间失效",
            
            // 详细备注 (AQ-AZ)
            "现场端主要情况Incident Description\n（1、现场主要表现；2、关键异常点；3、起钻后地面检查结果）",
            "维护/研发端检查发现Shop Finding\n（注意检查发现是否与现场问题对应）",
            "维护级别", "使用后维护完成日期", "更换主要部件", "具体失效总成分类", 
            "失效总成的部件分类", "失效主要原因标签", "根本原因分析", "改进计划/措施"
        };
        
        // 定义绿色列名的索引（振动级别、井下仪器情况、维护/研发端检查发现、维护级别、使用后维护完成日期、更换主要部件）
        Set<Integer> greenHeaderIndexes = new HashSet<>();
        greenHeaderIndexes.add(5);  // 振动级别
        greenHeaderIndexes.add(33); // 井下仪器情况
        greenHeaderIndexes.add(43); // 维护/研发端检查发现Shop Finding
        greenHeaderIndexes.add(44); // 维护级别
        greenHeaderIndexes.add(45); // 使用后维护完成日期
        greenHeaderIndexes.add(46); // 更换主要部件
        
        // 定义天蓝色列名的索引（具体失效总成分类、失效总成的部件分类、失效主要原因标签、根本原因分析、改进计划/措施）
        Set<Integer> lightBlueHeaderIndexes = new HashSet<>();
        lightBlueHeaderIndexes.add(47); // 具体失效总成分类
        lightBlueHeaderIndexes.add(48); // 失效总成的部件分类
        lightBlueHeaderIndexes.add(49); // 失效主要原因标签
        lightBlueHeaderIndexes.add(50); // 根本原因分析
        lightBlueHeaderIndexes.add(51); // 改进计划/措施

        // 定义深蓝色列名的索引（MWD失效、At Bit近钻失效、T-Well Link失效）
        Set<Integer> darkBlueHeaderIndexes = new HashSet<>();
        darkBlueHeaderIndexes.add(36); // MWD失效
        darkBlueHeaderIndexes.add(37); // At Bit近钻失效  
        darkBlueHeaderIndexes.add(38); // T-Well Link失效
        
        for (int i = 0; i < fieldHeaders.length; i++) {
            XSSFCell fieldCell = fieldRow.createCell(i);
            fieldCell.setCellValue(fieldHeaders[i]);
            
            // 根据列索引选择样式
            if (greenHeaderIndexes.contains(i)) {
                fieldCell.setCellStyle(greenHeaderStyle);
            } else if (lightBlueHeaderIndexes.contains(i)) {
                fieldCell.setCellStyle(lightBlueHeaderStyle);
            } else if (darkBlueHeaderIndexes.contains(i)) {
                fieldCell.setCellStyle(darkBlueHeaderStyle);
            } else {
                fieldCell.setCellStyle(headerStyle); // 默认橙色
            }
        }
    }

    /**
     * 根据温度值获取对应的样式
     *
     * @param temperatureValue 温度值
     * @param styles 样式数组 [cellStyle, normalTempStyle, highTempStyle, veryHighTempStyle, lowVibrationStyle, mediumVibrationStyle, highVibrationStyle]
     * @return 对应的样式
     */
    private XSSFCellStyle getTemperatureStyle(BigDecimal temperatureValue, XSSFCellStyle[] styles) {
        if (temperatureValue == null) {
            return styles[0]; // 默认样式
        }
        
        double temp = temperatureValue.doubleValue();
        if (temp <= 125) {
            return styles[1]; // normalTempStyle
        } else if (temp <= 150) {
            return styles[2]; // highTempStyle 
        } else {
            return styles[3]; // veryHighTempStyle
        }
    }

    /**
     * 根据振动级别获取对应的样式
     *
     * @param vibrationLevel 振动级别
     * @param styles 样式数组 [cellStyle, normalTempStyle, highTempStyle, veryHighTempStyle, lowVibrationStyle, mediumVibrationStyle, highVibrationStyle, veryHighVibrationStyle]
     * @return 对应的样式
     */
    private XSSFCellStyle getVibrationStyle(String vibrationLevel, XSSFCellStyle[] styles) {
        if (vibrationLevel == null || vibrationLevel.trim().isEmpty()) {
            return styles[0]; // 默认样式
        }
        
        String level = vibrationLevel.trim().toLowerCase();
        switch (level) {
            case "low":
                return styles[4]; // lowVibrationStyle
            case "medium":
                return styles[5]; // mediumVibrationStyle
            case "high":
                return styles[6]; // highVibrationStyle
            case "very high":
                return styles[7]; // veryHighVibrationStyle
            default:
                return styles[0]; // 默认样式
        }
    }

    /**
     * 根据井下仪器情况获取对应的样式
     *
     * @param instrumentStatus 井下仪器情况
     * @param styles 样式数组 [cellStyle, normalTempStyle, highTempStyle, veryHighTempStyle, lowVibrationStyle, mediumVibrationStyle, highVibrationStyle, veryHighVibrationStyle, normalInstrumentStyle, abnormalInstrumentStyle, rotarySteerableStyle, nonAtBitStyle, atBitStyle]
     * @return 对应的样式
     */
    private XSSFCellStyle getInstrumentStatusStyle(String instrumentStatus, XSSFCellStyle[] styles) {
        if (instrumentStatus == null || instrumentStatus.trim().isEmpty()) {
            return styles[0]; // 默认样式
        }
        
        String status = instrumentStatus.trim();
        if ("正常".equals(status)) {
            return styles[8]; // normalInstrumentStyle
        } else if ("异常".equals(status)) {
            return styles[9]; // abnormalInstrumentStyle
        } else {
            return styles[0]; // 默认样式
        }
    }

    /**
     * 根据服务井类别获取对应的样式
     *
     * @param wellCategory 服务井类别
     * @param styles 样式数组 [cellStyle, normalTempStyle, highTempStyle, veryHighTempStyle, lowVibrationStyle, mediumVibrationStyle, highVibrationStyle, veryHighVibrationStyle, normalInstrumentStyle, abnormalInstrumentStyle, rotarySteerableStyle, nonAtBitStyle, atBitStyle]
     * @return 对应的样式
     */
    private XSSFCellStyle getServiceWellCategoryStyle(String wellCategory, XSSFCellStyle[] styles) {
        if (wellCategory == null || wellCategory.trim().isEmpty()) {
            return styles[0]; // 默认样式
        }
        
        String category = wellCategory.trim();
        if ("旋转导向".equals(category)) {
            return styles[10]; // rotarySteerableStyle - 无颜色
        } else if ("非近钻井".equals(category)) {
            return styles[11]; // nonAtBitStyle - 绿色背景
        } else if ("近钻井".equals(category)) {
            return styles[12]; // atBitStyle - 橙色背景
        } else {
            return styles[0]; // 默认样式
        }
    }

    /**
     * 根据服务井类别获取对应的样式，并应用宋体字体
     *
     * @param wellCategory 服务井类别
     * @param styles 样式数组
     * @param baseTextStyle 基础文本样式
     * @param workbook Excel工作簿
     * @return 对应的样式
     */
    private XSSFCellStyle getServiceWellCategoryStyleWithFont(String wellCategory, XSSFCellStyle[] styles, 
                                                             XSSFCellStyle baseTextStyle, XSSFWorkbook workbook) {
        // 创建新样式
        XSSFCellStyle newStyle = workbook.createCellStyle();
        
        if (wellCategory == null || wellCategory.trim().isEmpty()) {
            newStyle.cloneStyleFrom(baseTextStyle);
            return newStyle;
        }
        
        String category = wellCategory.trim();
        if ("旋转导向".equals(category)) {
            newStyle.cloneStyleFrom(baseTextStyle); // 无特殊背景颜色
        } else if ("非近钻井".equals(category)) {
            newStyle.cloneStyleFrom(baseTextStyle);
            XSSFColor nonAtBitColor = new XSSFColor(new java.awt.Color(0x9f, 0xce, 0x63), null); // #9fce63
            newStyle.setFillForegroundColor(nonAtBitColor);
            newStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        } else if ("近钻井".equals(category)) {
            newStyle.cloneStyleFrom(baseTextStyle);
            XSSFColor atBitColor = new XSSFColor(new java.awt.Color(0xf5, 0xc2, 0x42), null); // #f5c242
            newStyle.setFillForegroundColor(atBitColor);
            newStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        } else {
            newStyle.cloneStyleFrom(baseTextStyle);
        }
        
        return newStyle;
    }

    /**
     * 设置列宽
     *
     * @param sheet Excel工作表
     */
    private void setupColumnWidths(XSSFSheet sheet) {
        // 设置各列的宽度（单位：字符宽度 * 256）
        int[] columnWidths = {
            // 基本信息
            8*256,   // 序号
            12*256,  // 服务井类别
            15*256,  // 井名
            10*256,  // 区块
            8*256,   // 温度
            10*256,  // 振动级别
            12*256,  // 是否振动超标OOS
            15*256,  // 超标工况描述
            10*256,  // 钻头尺寸
            8*256,   // 趟次
            
            // 泥浆信息/信号衰减计算
            10*256,  // 泥浆类型
            8*256,   // 密度
            8*256,   // 排量
            15*256,  // 施工开始井深
            15*256,  // 施工结束井深
            8*256,   // 进尺
            10*256,  // Orifice
            10*256,  // Poppet
            10*256,  // 信号强度
            
            // 仪器编号
            12*256,  // 近钻序列号
            12*256,  // 发射器序列号
            12*256,  // 接收器序列号
            15*256,  // 近钻Gamma序列号
            15*256,  // 绝缘短节序列号
            12*256,  // 脉冲序列号
            12*256,  // 底部序列号
            18*256,  // 探管/方位探管序列号
            18*256,  // 自然/方位Gamma序列号
            12*256,  // SU
            12*256,  // MWD
            12*256,  // LCP
            
            // 时间信息
            18*256,  // 入井时间
            18*256,  // 失效时间
            18*256,  // 出井时间
            
            // 井下情况
            15*256,  // 井下仪器情况
            20*256,  // 起钻原因
            
            // 失效统计分析
            12*256,  // 是否故障及趟数
            10*256,  // MWD失效
            15*256,  // At Bit 近钻失效
            15*256,  // T-Well Link失效
            10*256,  // 入井小时
            10*256,  // 循环小时
            12*256,  // 多少时间失效
            
            // 详细备注
            30*256,  // 现场端主要情况
            30*256,  // 维护/研发端检查发现
            12*256,  // 维护级别
            18*256,  // 使用后维护完成日期
            15*256,  // 更换主要部件
            18*256,  // 具体失效总成分类
            18*256,  // 失效总成的部件分类
            18*256,  // 失效主要原因标签
            25*256,  // 根本原因分析
            25*256   // 改进计划/措施
        };
        
        for (int i = 0; i < columnWidths.length; i++) {
            sheet.setColumnWidth(i, columnWidths[i]);
        }
    }

    /**
     * 过滤富文本中的base64图片内容
     * 移除所有包含base64编码图片的img标签，以防止数据库字段长度超限
     *
     * @param richText 富文本内容
     * @return 过滤后的富文本内容
     */
    public String filterBase64ImagesFromRichText(String richText) {
        if (richText == null || richText.isEmpty()) {
            return richText;
        }

        try {
            // 匹配包含base64图片的img标签的正则表达式
            // 匹配 <img...src="data:image/...;base64,..."...> 格式的标签
            String base64ImagePattern = "<img[^>]*src\\s*=\\s*[\"']data:image/[^;]+;base64,[^\"']*[\"'][^>]*>";

            // 移除所有base64图片标签
            String filteredText = richText.replaceAll(base64ImagePattern, "");

            // 记录过滤操作（仅在实际移除了内容时记录）
            if (!richText.equals(filteredText)) {
                int originalLength = richText.length();
                int filteredLength = filteredText.length();
                log.info("过滤富文本中的base64图片：原始长度 {} 字符，过滤后长度 {} 字符，减少了 {} 字符",
                        originalLength, filteredLength, originalLength - filteredLength);
            }

            return filteredText;
        } catch (Exception e) {
            log.error("过滤富文本中的base64图片时发生异常：{}", e.getMessage(), e);
            // 如果过滤失败，返回原始文本（但这可能仍会导致数据库错误）
            return richText;
        }
    }

    /**
     * 根据井号获取该井所有趟次的基本信息。
     * 此方法专门为井汇总视图的展开行设计，返回指定井号下所有趟次的基本信息列表。
     *
     * @param wellNumber 井号
     * @return 该井所有趟次的基本信息列表，如果未找到则返回空列表
     */
    @Override
    public List<WellOperationAnalysisDetailBasicVO> getRunsByWellNumber(String wellNumber) {
        if (!StringUtils.hasText(wellNumber)) {
            log.warn("井号参数为空，返回空列表");
            return new ArrayList<>();
        }

        try {
            // 1. 构建查询条件
            LambdaQueryWrapper<WellOperationAnalysisDetail> queryWrapper = Wrappers.lambdaQuery(WellOperationAnalysisDetail.class)
                .eq(WellOperationAnalysisDetail::getWellNumber, wellNumber)
                .orderByAsc(WellOperationAnalysisDetail::getRun); // 按趟次升序排列

            // 2. 查询数据库
            List<WellOperationAnalysisDetail> details = this.list(queryWrapper);
            if (CollectionUtils.isEmpty(details)) {
                log.info("未找到井号 {} 的趟次记录", wellNumber);
                return new ArrayList<>();
            }

            // 3. 计算每个井号+作业号组合的最大run号，用于确定jobStatus
            Map<String, Integer> maxRunMap = details.stream()
                .collect(Collectors.groupingBy(
                    detail -> detail.getWellNumber() + "|" + detail.getJobNumber(),
                    Collectors.mapping(WellOperationAnalysisDetail::getRun,
                        Collectors.maxBy(Integer::compareTo))
                ))
                .entrySet().stream()
                .collect(Collectors.toMap(
                    Map.Entry::getKey,
                    entry -> entry.getValue().orElse(0)
                ));

            // 4. 转换为BasicVO对象
            List<WellOperationAnalysisDetailBasicVO> basicVOs = new ArrayList<>();
            for (WellOperationAnalysisDetail detail : details) {
                // 计算正确的jobStatus
                String wellJobKey = detail.getWellNumber() + "|" + detail.getJobNumber();
                Integer maxRun = maxRunMap.get(wellJobKey);
                Integer correctJobStatus = calculateCorrectJobStatus(detail.getJobStatus(), detail.getRun(), maxRun);

                WellOperationAnalysisDetailBasicVO basicVO = convertToBasicVO(detail);
                // 设置重新计算的jobStatus
                basicVO.setJobStatus(correctJobStatus);

                // 5. 获取关联的工具记录
                List<WellAnalysisInstruments> instruments = wellAnalysisInstrumentsService.list(
                    Wrappers.lambdaQuery(WellAnalysisInstruments.class)
                        .eq(WellAnalysisInstruments::getAnalysisDetailIdFk, detail.getId())
                );

                // 6. 转换工具记录为VO对象并设置到BasicVO中
                if (!CollectionUtils.isEmpty(instruments)) {
                    List<WellAnalysisInstrumentsVO> instrumentVOs = instruments.stream()
                        .map(this::convertToInstrumentVO)
                        .collect(Collectors.toList());
                    basicVO.setInstruments(instrumentVOs);
                }

                basicVOs.add(basicVO);
            }

            log.info("成功获取井号 {} 的 {} 个趟次记录", wellNumber, basicVOs.size());
            return basicVOs;

        } catch (Exception e) {
            log.error("根据井号 {} 获取趟次信息失败", wellNumber, e);
            return new ArrayList<>();
        }
    }

    /**
     * 处理并同步所有分析数据，跳过已存在的记录
     * 此方法是一个便捷方法，等同于调用 processAndSynchronizeAllAnalysisData(true)
     *
     * 注意：此方法不使用事务注解，因为它调用的方法链中已经有事务管理
     */
    public void processAndSynchronizeNewAnalysisData() {
        processAndSynchronizeAllAnalysisData(true);
    }

    /**
     * 处理并同步所有分析数据，不跳过已存在的记录
     * 此方法是一个便捷方法，等同于调用 processAndSynchronizeAllAnalysisData(false)
     *
     * 注意：此方法不使用事务注解，因为它调用的方法链中已经有事务管理
     */
    public void processAndSynchronizeAllAnalysisData() { // 无参数版本，默认不跳过已存在记录
        processAndSynchronizeAllAnalysisData(false);
    }

    /**
     * 处理并同步所有分析数据
     *
     * @param skipExistingCheck 是否跳过已存在的记录检查。如果为true，则已经同步过的记录将不再被处理。
     */
    public void processAndSynchronizeAllAnalysisData(boolean skipExistingCheck) {
        // 1. 获取所有JobInfo记录
        List<JobInfo> allJobInfos = jobInfoService.list();
        if (CollectionUtils.isEmpty(allJobInfos)) {
            log.info("No JobInfo records found to process.");
            return;
        }

        // 2. 分批处理JobInfo，避免事务过长
        int batchSize = 100; // 每批处理的JobInfo数量
        List<List<JobInfo>> jobInfoBatches = new ArrayList<>();
        for (int i = 0; i < allJobInfos.size(); i += batchSize) {
            jobInfoBatches.add(allJobInfos.subList(i, Math.min(i + batchSize, allJobInfos.size())));
        }

        log.info("Starting processing for {} JobInfo records in {} batches. Skip existing check: {}",
                 allJobInfos.size(), jobInfoBatches.size(), skipExistingCheck);

        // 3. 处理每批JobInfo
        for (int batchIndex = 0; batchIndex < jobInfoBatches.size(); batchIndex++) {
            List<JobInfo> currentBatch = jobInfoBatches.get(batchIndex);
            processJobInfoBatch(currentBatch, skipExistingCheck, batchIndex + 1, jobInfoBatches.size());
        }
    }

    /**
     * 处理一批JobInfo记录
     *
     * @param jobInfoBatch 要处理的JobInfo批次
     * @param skipExistingCheck 是否跳过已存在的记录检查
     * @param batchNumber 当前批次编号
     * @param totalBatches 总批次数
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void processJobInfoBatch(List<JobInfo> jobInfoBatch, boolean skipExistingCheck, int batchNumber, int totalBatches) {
        try {
            // 批次开始处理的汇总日志
            log.info("开始处理批次 {}/{} - 包含 {} 个JobInfo记录", batchNumber, totalBatches, jobInfoBatch.size());

            // 1. 收集所有JobInfo的WellId
            List<String> wellIds = jobInfoBatch.stream()
                .map(JobInfo::getWellId)
                .filter(StringUtils::hasText)
                .collect(Collectors.toList());

            // 2. 批量查询所有相关的WellInfo
            Map<String, WellInfo> wellInfoMap = new HashMap<>();
            if (!wellIds.isEmpty()) {
                List<WellInfo> wellInfos = wellInfoService.listByIds(wellIds);
                wellInfoMap = wellInfos.stream()
                    .collect(Collectors.toMap(WellInfo::getWellId, Function.identity(), (existing, replacement) -> existing));
            }

            // 3. 处理每个JobInfo
            for (JobInfo currentJobInfo : jobInfoBatch) {
                Long currentJobId = currentJobInfo.getId();
                String wellId = currentJobInfo.getWellId();
                String jobNumber = currentJobInfo.getJobNumber();

                // 获取对应的WellInfo
                WellInfo currentWellInfo = wellInfoMap.get(wellId);
                if (currentWellInfo == null) {
                    // 未找到对应的WellInfo，跳过此JobInfo
                    log.warn("跳过JobInfo - ID: {}, JobNumber: {} - 原因: 未找到对应的WellInfo记录",
                            currentJobId, jobNumber);
                    continue;
                }

                String currentWellNumber = currentWellInfo.getWellNumber();

                // 4. 查询MySQL中已存在的记录 (在skipExistingCheck=false时不需要，但保留代码结构)

                // 5. 批量查询MongoDB中的run数据
                Set<Integer> uniqueRuns = new java.util.HashSet<>();

                // 一次性查询所有MWD运行报告
                Query mwdRunQuery = Query.query(Criteria.where("jobId").is(currentJobId));
                mwdRunQuery.fields().include("run"); // 只获取run字段
                List<MwdRunReport> mwdRuns = mongoTemplate.find(mwdRunQuery, MwdRunReport.class);
                if (!CollectionUtils.isEmpty(mwdRuns)) {
                    Set<Integer> mwdRunNumbers = mwdRuns.stream()
                            .filter(r -> r.getRun() != null)
                            .map(MwdRunReport::getRun)
                            .collect(Collectors.toSet());
                    uniqueRuns.addAll(mwdRunNumbers);
                }

                // 一次性查询所有RSS运行报告
                Query rssRunQuery = Query.query(Criteria.where("jobId").is(currentJobId));
                rssRunQuery.fields().include("run"); // 只获取run字段
                List<RSSRunReport> rssRuns = mongoTemplate.find(rssRunQuery, RSSRunReport.class);
                if (!CollectionUtils.isEmpty(rssRuns)) {
                    Set<Integer> rssRunNumbers = rssRuns.stream()
                            .filter(r -> r.getRun() != null)
                            .map(RSSRunReport::getRun)
                            .collect(Collectors.toSet());
                    uniqueRuns.addAll(rssRunNumbers);
                }

                if (uniqueRuns.isEmpty()) {
                    // 未找到run数据，跳过此JobInfo
                    log.warn("跳过JobInfo - ID: {}, WellNumber: {} - 原因: 未找到任何Run记录",
                            currentJobId, currentWellNumber);
                    continue;
                }

                // 6. 如果启用了跳过已存在记录检查，则预先查询所有已存在的记录
                Map<Integer, Boolean> runHasExistingDetail = new HashMap<>();
                if (skipExistingCheck) {
                    for (Integer run : uniqueRuns) {
                        WellOperationAnalysisDetail existingDetail = baseMapper.findByWellNumberAndRun(currentWellNumber, run);
                        if (existingDetail != null) {
                            // 检查createTime和updateTime是否相等，如果相等则表示记录是初始创建后未经过任何更新的
                            if (existingDetail.getCreateTime() != null && existingDetail.getUpdateTime() != null
                                    && existingDetail.getCreateTime().equals(existingDetail.getUpdateTime())) {
                                // createTime和updateTime相等，标记为需要更新
                                log.info("记录已存在但createTime和updateTime相等，将执行更新操作 - WellNumber: {}, Run: {}", currentWellNumber, run);
                                runHasExistingDetail.put(run, false); // 标记为不存在，这样后续会处理它
                            } else {
                                // createTime和updateTime不相等，标记为跳过
                                log.info("记录已存在且已更新过(createTime != updateTime)，跳过更新 - WellNumber: {}, Run: {}", currentWellNumber, run);
                                runHasExistingDetail.put(run, true); // 标记为存在，这样后续会跳过它
                            }
                        } else {
                            // 记录不存在，标记为需要处理
                            runHasExistingDetail.put(run, false);
                        }
                    }
                }

                // 7. 处理每个run
                for (Integer currentRun : uniqueRuns) {
                    // 如果启用了跳过已存在记录检查，则检查是否已存在对应记录
                    if (skipExistingCheck && runHasExistingDetail.getOrDefault(currentRun, false)) {
                        // 已存在记录，跳过此run
                        continue;
                    }

                    try {
                        // 处理当前run
                        WellOperationAnalysisDetail detail = createOrUpdateAnalysisDetailWithInstruments(currentWellNumber, currentRun, skipExistingCheck);

                        if (detail == null) {
                            // 处理失败，跳过此run
                            log.warn("处理Run: {} 失败 - JobID: {}, WellNumber: {}",
                                    currentRun, currentJobId, currentWellNumber);
                            continue;
                        }
                    } catch (Exception e) {
                        log.error("处理Run: {} 异常 - JobID: {}, WellNumber: {} - 错误: {}",
                                currentRun, currentJobId, currentWellNumber, e.getMessage(), e);
                        // 继续处理下一个run
                    }
                }
            }
        } catch (Exception e) {
            log.error("处理JobInfo批次 {}/{} 异常: {}", batchNumber, totalBatches, e.getMessage(), e);
            throw e; // 重新抛出异常，触发事务回滚
        }
    }

    /**
     * 分页查询井汇总数据。
     * 按井号聚合数据，返回每个井的统计信息。
     *
     * @param current 当前页码
     * @param size 每页数量
     * @param wellNumber 井号（可选，模糊查询）
     * @param isFailure 是否故障（可选）
     * @param orderBy 排序字段（可选）
     * @param orderType 排序方式（可选，asc或desc）
     * @return 井汇总数据的分页结果
     */
    @Override
    public IPage<WellSummaryVO> pageWellSummary(int current, int size, String wellNumber, String isFailure, String orderBy, String orderType) {
        // 1. 创建分页对象
        Page<WellSummaryVO> voPage = new Page<>(current, size);

        // 2. 计算offset
        long offset = (long)(current - 1) * size;
        if (offset < 0) offset = 0;

        // 3. 设置排序字段和顺序，并进行安全验证
        String orderByField = "wellNumber"; // 默认排序字段
        if (StringUtils.hasText(orderBy)) {
            // 将前端字段名映射到数据库字段名，并验证是否在白名单中
            orderByField = mapAndValidateWellSummarySortField(orderBy);
        }
        boolean isAsc = "asc".equalsIgnoreCase(orderType);

        try {
            // 4. 查询井汇总数据
            List<WellSummaryVO> records = baseMapper.findWellSummaryPage(offset, (long)size, wellNumber, isFailure, orderByField, isAsc);
            long total = baseMapper.countWellSummary(wellNumber, isFailure);

            // 5. 设置分页信息
            voPage.setTotal(total);
            voPage.setSize(size);
            voPage.setCurrent(current);
            voPage.setPages((total + size - 1) / size);
            voPage.setRecords(records != null ? records : Collections.emptyList());

            log.info("井汇总分页查询完成 - 参数: current={}, size={}, wellNumber={}, isFailure={}, orderBy={}, orderType={}, 结果: total={}, records={}",
                    current, size, wellNumber, isFailure, orderBy, orderType, total, records != null ? records.size() : 0);

            return voPage;

        } catch (Exception e) {
            log.error("井汇总分页查询失败", e);
            voPage.setRecords(Collections.emptyList());
            voPage.setTotal(0);
            return voPage;
        }
    }

    /**
     * 映射并验证井汇总排序字段
     * 将前端字段名映射到数据库字段名，并验证是否在白名单中
     *
     * @param frontendField 前端字段名
     * @return 映射后的数据库字段名
     */
    private String mapAndValidateWellSummarySortField(String frontendField) {
        // 定义前端字段到数据库字段的映射关系
        Map<String, String> fieldMapping = new HashMap<>();
        fieldMapping.put("wellNumber", "wellNumber");
        fieldMapping.put("block", "block");
        fieldMapping.put("totalRuns", "totalRuns");
        fieldMapping.put("failureRuns", "failureRuns");
        fieldMapping.put("totalWorkHours", "totalWorkHours");
        fieldMapping.put("totalInstruments", "totalInstruments");
        fieldMapping.put("earliestDateIn", "earliestDateIn");
        fieldMapping.put("latestDateOut", "latestDateOut");
        fieldMapping.put("createTime", "createTime");
        fieldMapping.put("updateTime", "updateTime");

        // 添加数据库字段名的直接映射
        fieldMapping.put("create_time", "createTime");
        fieldMapping.put("update_time", "updateTime");
        fieldMapping.put("well_number", "wellNumber");

        // 获取映射后的字段名
        String mappedField = fieldMapping.get(frontendField);
        if (mappedField != null) {
            return mappedField;
        }

        // 如果没有找到映射，记录警告并返回默认字段
        log.warn("未知的井汇总排序字段: {}, 使用默认排序字段: wellNumber", frontendField);
        return "wellNumber";
    }
}