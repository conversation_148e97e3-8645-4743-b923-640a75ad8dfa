package net.tartan.platform.integration.beans.dto;

import lombok.Data;

import java.util.List;

/**
 * 旋导仪器与部件的树结构信息类
 */
@Data
public class RotaryTreeDTO {

    private Long deviceId;

    /**
     * 仪器名称
     */
    private String invName;

    private String serialNumber;

    // 版本号
    private String versionNumber;

    // 品号
    private String invCode;
    // 仪器可用层级 默认为0级
    private Integer level = 0;

    private List<ComponentHierarchyDTO> children;

}
