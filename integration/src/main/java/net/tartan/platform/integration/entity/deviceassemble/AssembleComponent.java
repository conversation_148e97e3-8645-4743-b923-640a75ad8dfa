package net.tartan.platform.integration.entity.deviceassemble;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.enums.EnumRepairType;
import net.tartan.platform.common.enums.EnumRiskType;
import net.tartan.platform.common.enums.EnumRubberType;

import java.math.BigDecimal;

/**
 * <p>
 * 设备库存信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-09
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AssembleComponent{
    /**
     * 品名
     */
    private String invName;
//    /**
//     * 规格
//     */
//    private String invStd;

    /**
     * 序列号，erp的批次号
     */
    private String serialNumber;
    /**
     * 服役固件版本号
     */
    private String serviceVersionNumber;
    /**
     * 升级后固件版本号
     */
    private String updatedVersionNumber;
    /**
     * 服役固件版本号
     */
    private EnumRiskType serviceRiskType;
    /**
     * 升级后固件版本号
     */
    private EnumRiskType updatedRiskType;

    /**
     * 数量
     */
    private Integer quantity;

//    /**
//     * 风险类型
//     */
//    private EnumRiskType riskType;

//    /**
//     * 使用时长
//     */
//    private BigDecimal totalHours;

    /**
     * 修正使用时长
     */
    private BigDecimal reviseTotalHours;
    /**
     * 最高温度
     */
    private BigDecimal maxBht;

    /**
     * 修正最高温度
     */
    private BigDecimal reviseMaxBht;

    /**
     * 维修类型
     */
    private EnumRepairType repairType;

    /**
     * 维修方案
     */
    private String repairAction;

    /**
     * 失效原因
     */
    private String failureReason;

    //螺杆部件额外信息
    //万向轴壳体
    /**
     * 弯度类型:单弯 1 / 可调 0
     */
    private Integer curve;

    /**
     * 弯度
     */
    private BigDecimal angle;

    //定子
    /**
     * 橡胶类型
     */
    private EnumRubberType rubberType;

    /**
     * 区分是否失效，0 -> 未失效，1 -> 失效
     */
    private Integer isBroke;

    // 新增参数

    /**
     * 制造商
     */
    private String manufacturer;

    /**
     * 温度类型
     */
    private String tempType;

    /**
     * 核心部件的备注
     */
    private String note;

    /**
     * 风险值
     */
    private BigDecimal riskValue;
    /**
     * 总循环时间
     */
    private BigDecimal totalCirculateHrs;
    /**
     * 总入井时间
     */
    private BigDecimal totalInWellHrs;

    private Long rmaId;
    private String rmaNumber;

    private Long rmaRepairId;
    private String rmaRepairCode;
}
