package net.tartan.platform.integration.mapper.erp;

import net.tartan.platform.integration.entity.erp.ItemWarehouseBin;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 存货余额明细信息/CHT/存貨餘額明細資料/ENU/Inventory Balance Details Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-24
 */
@Mapper
public interface ErpItemWarehouseBinMapper extends BaseMapper<ItemWarehouseBin> {

    ItemWarehouseBin getLatestTimestamp();

    List<ItemWarehouseBin> listByVersion(@Param("startSyncTimestamp") byte[] startSyncTimestamp, @Param("endSyncTimestamp") byte[] endSyncTimestamp);

    List<ItemWarehouseBin> getAllKit();

}
