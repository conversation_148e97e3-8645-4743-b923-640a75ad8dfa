package net.tartan.platform.integration.entity.deviceassemble.repaircheck.pitTools;

import lombok.Data;
import net.tartan.platform.integration.entity.deviceassemble.repaircheck.BaseCheckData;
import net.tartan.platform.integration.entity.deviceassemble.repaircheck.FaultDiagnosis;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 水力振荡器 维修检查配件表
 *
 * Hydra
 */
@Data
public class HydraulicToolsData extends BaseCheckData {

    //旋扣扭矩List
    private List<Map<String, Object>> torsionList;

    public HydraulicToolsData init(){
        HydraulicToolsData hydraulicToolsData = new HydraulicToolsData();
//        List<List<Map<String, Object>>> torsionList = new ArrayList<>();

//        data.put("公扣接头 传动筒体",0.0);
        List<Map<String, Object>> torsionData1 = new ArrayList<>();

        Map<String, Object> data1 = new HashMap<>();
        data1.put("key","公扣接头 传动筒体");
        data1.put("value",0.0);
        torsionData1.add(data1);

//        data.put("锁母 涡轮压盖",0.0);
//        List<Map<String, Object>> torsionData2 = new ArrayList<>();
        Map<String, Object> data2 = new HashMap<>();
        data2.put("key","锁母 涡轮压盖");
        data2.put("value",0.0);
        torsionData1.add(data2);

//        data.put("传动筒体 中间接头",0.0);
//        List<Map<String, Object>> torsionData3 = new ArrayList<>();
        Map<String, Object> data3 = new HashMap<>();
        data3.put("key","传动筒体 中间接头");
        data3.put("value",0.0);
        torsionData1.add(data3);

//        data.put("中间接头 振荡筒体",0.0);
//        List<Map<String, Object>> torsionData4 = new ArrayList<>();
        Map<String, Object> data4 = new HashMap<>();
        data4.put("key","中间接头 振荡筒体");
        data4.put("value",0.0);
        torsionData1.add(data4);

//        data.put("防掉筒 花键轴",0.0);
//        List<Map<String, Object>> torsionData5 = new ArrayList<>();
        Map<String, Object> data5 = new HashMap<>();
        data5.put("key","防掉筒 花键轴");
        data5.put("value",0.0);
        torsionData1.add(data5);

//        data.put("振荡筒体 花键套",0.0);
//        List<Map<String, Object>> torsionData6 = new ArrayList<>();
        Map<String, Object> data6 = new HashMap<>();
        data6.put("key","振荡筒体 花键套");
        data6.put("value",0.0);
        torsionData1.add(data6);

//        data.put("活塞 花键轴",0.0);
//        List<Map<String, Object>> torsionData7 = new ArrayList<>();
        Map<String, Object> data7 = new HashMap<>();
        data7.put("key","活塞 花键轴");
        data7.put("value",0.0);
        torsionData1.add(data7);


//        torsionList.add(torsionData1);
//        torsionList.add(torsionData2);
//        torsionList.add(torsionData3);
//        torsionList.add(torsionData4);
//        torsionList.add(torsionData5);
//        torsionList.add(torsionData6);
//        torsionList.add(torsionData7);

        hydraulicToolsData.setTorsionList(torsionData1);

        //初始化故障诊断
        hydraulicToolsData.setFaultDiagnosis(new FaultDiagnosis().init());
        return hydraulicToolsData;
    }


}
