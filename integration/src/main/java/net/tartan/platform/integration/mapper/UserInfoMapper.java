package net.tartan.platform.integration.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.tartan.platform.integration.beans.vo.UserAndRoleVo;
import net.tartan.platform.integration.beans.vo.UserInfoVo;
import net.tartan.platform.integration.entity.Department;
import net.tartan.platform.integration.entity.UserInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * 账户信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-29
 */
@Mapper
public interface UserInfoMapper extends BaseMapper<UserInfo> {

    List<UserAndRoleVo> listAll(UserAndRoleVo userInfo);

    List<UserInfoVo> listByRole(long roleId);

    List<UserInfoVo> listByMenId(long menuId);

    List<UserInfoVo> findByDepartmentId(long departmentId);

    List<Long> filter(@Param("userIdList") List<Long> userIdList);

//    List<String> getUserAuthorityInfo(Long userId);

    List<Department> getAllCompanys();

    List<Department> getPrimaryDepartments();

    List<Map<String, Objects>> getUserAndDepartments();

    String getMemberNameByUserId(Long userId);

    UserInfo getUserByName(@Param("userName") String userName);
}
