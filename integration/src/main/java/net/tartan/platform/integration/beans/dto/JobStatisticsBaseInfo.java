package net.tartan.platform.integration.beans.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.integration.entity.JobReportRelation;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class JobStatisticsBaseInfo {
    private long jobId;
    private String jobNumber;
    private String jobTypeName;
    private String wellNumber;
    private List<JobReportRelation> jobReportRelationList;
}
