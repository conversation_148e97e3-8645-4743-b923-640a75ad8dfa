package net.tartan.platform.integration.mapper.erp;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.tartan.platform.integration.entity.deviceassemble.repaircheck.ReplaceItem;
import net.tartan.platform.integration.entity.erp.ErpBomInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ErpBomMapper extends BaseMapper<ErpBomInfo> {

    List<ErpBomInfo> getBomList(@Param("query") ErpBomInfo info);

    List<ReplaceItem> getReplaceItemByBomId(@Param("query") ErpBomInfo info);
}
