package net.tartan.platform.integration.entity.dailyreport;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * mwd_offset_lengths
 * <AUTHOR>
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class MwdOffsetLengths{
    private Long id;

    /**
     * mwd sensor offset report id
     */
    private Long offsetReportId;

    /**
     * 序号
     */
    private String itemNo;

    private String toolModules;

    private BigDecimal length;

    private BigDecimal directional;

    private BigDecimal gamma;

    private BigDecimal gap;

    private BigDecimal log2;

    private BigDecimal log3;

    private BigDecimal log4;
}
