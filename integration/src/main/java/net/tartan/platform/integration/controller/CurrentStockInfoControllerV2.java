package net.tartan.platform.integration.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.integration.beans.query.CurrentStockInfoQuery;
import net.tartan.platform.integration.beans.query.ToolQuery;
import net.tartan.platform.integration.beans.vo.CurrentStockInfoVo;
import net.tartan.platform.integration.entity.InventoryClassInfo;
import net.tartan.platform.integration.service.ICurrentStockInfoService;
import net.tartan.platform.integration.service.IInventoryClassInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <p>
 * 设备库存信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-24
 */
@RestController
@RequestMapping("/currentStock")
public class CurrentStockInfoControllerV2 {

    @Autowired
    private ICurrentStockInfoService currentStockInfoService;

    @Autowired
    private IInventoryClassInfoService inventoryClassInfoService;

    @GetMapping("inventoryClassList")
    public CommonResult getInventoryClassList() {
        List<InventoryClassInfo> list = inventoryClassInfoService.list();
        return CommonResult.success(list);
    }

    @PostMapping("list/{current}/{size}")
    public CommonResult list(@PathVariable long current,
                                  @PathVariable long size,
                                  @RequestBody CurrentStockInfoQuery currentStockInfoQuery) {
        IPage<CurrentStockInfoVo> page = new Page<>(current, size);
        currentStockInfoService.list(page, currentStockInfoQuery);
        return CommonResult.success(page);
    }
    @PostMapping("serialNumberList")
    public CommonResult getSerialNumberList(@RequestBody ToolQuery toolQuery){
        List<String> list = currentStockInfoService.getSerialNumberList(toolQuery);
        return CommonResult.success(list);
    }

//    @PostMapping("list")
//    public CommonResult list(@RequestBody CurrentStockQuery currentStockQuery) {
//        IPage<CurrentStockDto> page = new Page<>(currentStockQuery.getCurrent(), currentStockQuery.getSize());
//        currentStockInfoService.getListByPage(page, currentStockQuery);
//        final CurrentStockVo result = CurrentStockVo.builder()
//                .current(page.getCurrent())
//                .currentStockList(page.getRecords())
//                .size(page.getSize())
//                .total(page.getTotal())
//                .pages(page.getPages())
//                .build();
//        return CommonResult.success(result);
//    }
//
//    @PostMapping("fuzzyList")
//    public CommonResult fuzzyList(@RequestBody CurrentStockQuery currentStockQuery) {
//        IPage<CurrentStockDto> page = new Page<>(currentStockQuery.getCurrent(), currentStockQuery.getSize());
//        currentStockInfoService.getFuzzyListByPage(page, currentStockQuery);
//        final CurrentStockVo result = CurrentStockVo.builder()
//                .current(page.getCurrent())
//                .currentStockList(page.getRecords())
//                .size(page.getSize())
//                .total(page.getTotal())
//                .pages(page.getPages())
//                .build();
//        return CommonResult.success(result);
//    }
//
//    @PostMapping("update")
//    public CommonResult update(@RequestBody @Valid CurrentStockUpdate currentStockUpdate) {
//        CurrentStockInfo currentStockInfo = new CurrentStockInfo();
//        BeanUtils.copyProperties(currentStockUpdate, currentStockInfo);
//        currentStockInfoService.updateById(currentStockInfo);
//        return CommonResult.success();
//    }
}
