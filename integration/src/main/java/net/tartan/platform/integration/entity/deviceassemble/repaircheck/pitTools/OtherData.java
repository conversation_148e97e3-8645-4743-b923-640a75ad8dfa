package net.tartan.platform.integration.entity.deviceassemble.repaircheck.pitTools;

import lombok.Data;
import net.tartan.platform.integration.entity.deviceassemble.repaircheck.BaseCheckData;
import net.tartan.platform.integration.entity.deviceassemble.repaircheck.FaultDiagnosis;

/**
 * 其他类型 实体类
 * Other
 */
@Data
public class OtherData extends BaseCheckData {
    public OtherData init(){
        OtherData other = new OtherData();

        other.setFaultDiagnosis(new FaultDiagnosis().init());
        return other;
    }
}
