package net.tartan.platform.integration.mapper.erp;

import net.tartan.platform.integration.beans.query.IssueReceiptQuery;
import net.tartan.platform.integration.entity.deviceassemble.repaircheck.ReplaceItem;
import net.tartan.platform.integration.entity.erp.IssueReceiptD;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 领/退料单单身/CHT/領//退料單單身/ENU/Material Requisition//Return Note Detail Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-14
 */
@Mapper
public interface ErpIssueReceiptDMapper extends BaseMapper<IssueReceiptD> {

    List<String> selectDocNoList(@Param("docNo") String docNo);

    List<ReplaceItem> list(@Param("query") IssueReceiptQuery issueReceiptQuery);
}
