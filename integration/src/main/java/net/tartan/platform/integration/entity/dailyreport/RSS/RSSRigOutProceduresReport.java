package net.tartan.platform.integration.entity.dailyreport.RSS;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.constant.PfConstant;
import net.tartan.platform.integration.entity.dailyreport.MwdRiProceduresAction;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Document(PfConstant.RSS_ROP_REPORT)
public class RSSRigOutProceduresReport implements Serializable {

    @Transient
    private Long id;

    /**
     * 报告类型
     */
    @Transient
    private String reportType;

    @Id
    @JsonIgnore
    private String reportId;

    /**
     * 作业id
     */
    private Long jobId;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 作业号
     */
    private String jobNumber;

    /**
     * MWD工程师
     */
    private String mwdOEngineer;

    /**
     * 签名日期yyyy-MM-dd
     */
    private String signatureDate;

    /**
     * 截图文件key
     */
    private String screenShoot;

    /**
     * 工具
     */
    private List<MwdRiProceduresAction> actionList;

    private static final long serialVersionUID = 1L;
}
