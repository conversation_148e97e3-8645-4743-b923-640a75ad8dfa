//package net.tartan.platform.integration.beans.request;
//
//import lombok.AllArgsConstructor;
//import lombok.Builder;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//
//import javax.validation.constraints.NotBlank;
//
//@Data
//@Builder
//@NoArgsConstructor
//@AllArgsConstructor
//public class AddDepartmentParams {
//    /**
//     * 部门名称
//     */
//    @NotBlank
//    private String name;
//
//    private Long principalId;
//}
