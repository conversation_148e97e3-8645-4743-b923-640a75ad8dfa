package net.tartan.platform.integration.service.impl.oa;

import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.extern.slf4j.Slf4j;
import net.tartan.platform.integration.entity.MemberInfo;
import net.tartan.platform.integration.entity.OaAutoUserInfo;
import net.tartan.platform.integration.entity.PaymentReqDoc;
import net.tartan.platform.integration.entity.PaymentReqDocD;
import net.tartan.platform.integration.mapper.EmployeeMapper;
import net.tartan.platform.integration.mapper.MemberInfoMapper;
import net.tartan.platform.integration.mapper.OaAutoUserMapper;
import net.tartan.platform.integration.service.DrillingToolNotificationService;
import net.tartan.platform.integration.service.IAutoOaFormService;
import net.tartan.platform.integration.service.OAClientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AutoOaFormServiceImpl implements IAutoOaFormService {
    @Value("${oa.auto-tool.template-code:MYTX_001}")
    private String templateCode;


    @Autowired
    private MemberInfoMapper memberInfoMapper;

    @Autowired
    private OAClientService oaClientService;
    @Autowired
    private OaAutoUserMapper oaAutoUserMapper;

    @Override
    public void autoSendByList() {
        List<OaAutoUserInfo> oaAutoUserInfoList = oaAutoUserMapper.selectAllByType(1L);
        int index = 1;
        for(OaAutoUserInfo info : oaAutoUserInfoList){
            // 查出一个list
            Map<String, Object> result = convertToOAFormat(info.getUserId(), index);
            String callBack = oaClientService.pushDataWithRetry(result, 3);
            System.out.println(callBack);
            index++;
        }

    }

    // index 为序号
    private Map<String, Object> convertToOAFormat(Long userId, int index) {
        Map<String, Object> oaData = new HashMap<>();

        // 设置基本信息
        oaData.put("templateCode", templateCode); // 单据模板ID
        oaData.put("transfertype", "json");
        oaData.put("param", "0");

        // 设置发起人编码
        String senderCode = extractSenderCode();
        log.info("设置发起人编码: {}", senderCode);
        oaData.put("sendMemberCode", senderCode);

        // 转换数据字段
        Map<String, Object> data = new HashMap<>();

        MemberInfo memberInfo = memberInfoMapper.getMemberByUserId(userId);

        // 主表字段映射 - 基于OA端 MonthlyReportEnum.monthlyReportMainField
        // 单据编号
        data.put("DOC_NO", "MYTX-" + formatDateToYMD(new Date(), "yyyyMMdd") + padWithZeros(index,4));
        data.put("EMPLOYEE_ID", memberInfo.getSortId().toString());      // 申请人员
        data.put("DEPARTMENT", memberInfo.getOrgDepartmentId().toString());                   // 部门
        data.put("DATE", formatDateToYMD(new Date(), "yyyy-MM-dd")); // 填写日期

        oaData.put("data", data);

        log.info("数据转换完成，单据: {}", oaData);
        return oaData;
    }

    // 获取OA里的用户id，（同步到综合平台这边来的字段叫 sortId）
    private String extractSenderCode() {
        MemberInfo memberInfo = memberInfoMapper.getMemberByUserId(123L);
        return memberInfo.getSortId().toString();
    }

    private String formatDateToYMD(Date date, String format) {
        if (date == null) {
            return null; // 或者返回空字符串 ""
        }
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(date);
    }

    // 序号格式化， 1 -> "0001", 182 -> "0182"
    private String padWithZeros(int number, int length) {
        return String.format("%0" + length + "d", number);
    }


}
