package net.tartan.platform.integration.beans.vo.cert;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.integration.entity.mwdCert.MwdCertBaseInfo;

/**
 * 仪器合格证的前端视图
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CertVo {

    private Long id;
    private Long mwdId;
    private Long deviceType;

    //审批状态： 0-未审批，-1-未通过，2-通过
    private Integer approvalStatus;

    //合格证创建人
    private Long createdBy;
    private String creatorName;

    //合格证审批人
    private Long approvedBy;
    private String approverName;

    //合格证的内容详情
    private MwdCertBaseInfo certInfo;

    private String createTime;
    private String updateTime;
}
