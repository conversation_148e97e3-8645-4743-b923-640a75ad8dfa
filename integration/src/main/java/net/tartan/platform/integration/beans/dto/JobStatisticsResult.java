package net.tartan.platform.integration.beans.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class JobStatisticsResult {

    /**
     * 作业编号
     */
    private String jobNumber;
    /**
     * 作业类型
     */
    private String jobType;
    /**
     * 井号
     */
    private String wellNumber;
    /**
     * 总趟次
     */
    private Long totalRun;
    /**
     * 方位gamma
     */
    private Long azimuthGamma;
    /**
     * 方位gamma
     */
    private Long naturalGamma;
    /**
     * 近钻头
     */
    private Long atBit;
    /**
     * 失效趟次
     */
    private Long failureRun;
    /**
     * 失效原因
     */
    private FailureReasonStatisticsDto failureReason;
    /**
     * 施工井段
     */
    private String consDepthRange;
    /**
     * 总进尺
     */
    private Double totalFootage;
    /**
     * 周期
     */
    private String period;
    /**
     * 温度
     */
    private Double temperature;
    /**
     * 垂深
     */
    private Double tvd;
}
