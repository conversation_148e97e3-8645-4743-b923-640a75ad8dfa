package net.tartan.platform.integration.beans.vo.devicerisktype;

import lombok.Data;
import net.tartan.platform.common.enums.EnumRiskType;

import java.util.List;

@Data
public class DeviceRiskTypeVo {


    private Long deviceId;

    /**
     * 仪器类型
     */
    private Long deviceType;
    /**
     * 品名
     */
    private String invName;

    /**
     * 序列号
     */
    private String serialNumber;
    /**
     * 风险类型
     */
    private EnumRiskType riskType;
    /**
     * 是否风险
     */
    private boolean risk;


    private List<ComponentRiskTypeVo> componentRiskTypeList;
}
