package net.tartan.platform.integration.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.common.beans.ResultCode;
import net.tartan.platform.integration.beans.dto.MwdWorkStatisticsDto;
import net.tartan.platform.integration.beans.dto.WorkOrderMwdDto;
import net.tartan.platform.integration.beans.query.WorkOrderMwdQuery;
import net.tartan.platform.integration.beans.vo.*;
import net.tartan.platform.integration.entity.WorkOrderMwd;
import net.tartan.platform.integration.entity.deviceassemble.MwdDeviceAssembleRecord;
import net.tartan.platform.integration.exception.BusinessException;
import net.tartan.platform.integration.service.IWorkOrderMwdService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * mwd工单信息表 前端控制器
 *
 * <AUTHOR>
 * @since 2022-05-09
 */
@RestController
@RequestMapping("/workOrderMwd")
public class WorkOrderMwdController {

    @Autowired
    private IWorkOrderMwdService workOrderMwdService;

    @PostMapping("list/{current}/{size}")
    public CommonResult list(@PathVariable long current, @PathVariable long size, @RequestBody(required = false) WorkOrderMwdQuery query) {
        IPage<WorkOrderMwdDetailResponse> page = new Page<>(current, size);
        if (query == null) {
            query = new WorkOrderMwdQuery();
        }
        workOrderMwdService.list(page, query);
        return CommonResult.success(page);
    }

    @PostMapping("history/detail")
    public CommonResult historyDetail(@RequestBody(required = false) WorkOrderMwdQuery query) {
        if (query == null) {
            query = new WorkOrderMwdQuery();
        }
        return CommonResult.success(workOrderMwdService.getDeviceRepairDetail(query));
    }

    @PostMapping("add")
    public CommonResult add(@RequestBody @Valid WorkOrderMwdDto orderMwdDto) {
        if (orderMwdDto.getMwdNumber() == null) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        Long mwdId = workOrderMwdService.add(orderMwdDto);
        WorkOrderMwdVo workOrderMwdVo = workOrderMwdService.info(mwdId);
        return CommonResult.success(workOrderMwdVo);
    }

    @PostMapping("add/batch")
    public CommonResult addBatch(@RequestBody @Valid List<WorkOrderMwdDto> orderMwdDtoList) {

        if (CollectionUtils.isEmpty(orderMwdDtoList)) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }

        for (WorkOrderMwdDto workOrderMwdDto : orderMwdDtoList) {
            if (workOrderMwdDto.getMwdNumber() == null) {
                throw new BusinessException(ResultCode.PARAMS_ERROR);
            }
            Long mwdId = workOrderMwdService.add(workOrderMwdDto);
            if (workOrderMwdDto.getRepairDetailId() != null) {
                workOrderMwdService.addRepairDetailRelation(mwdId, workOrderMwdDto.getRepairDetailId());
            }
        }
        return CommonResult.success();
    }

    @GetMapping("info")
    public CommonResult info(@RequestParam("mwdId") long mwdId) {
        WorkOrderMwdVo workOrderMwdVo = workOrderMwdService.info(mwdId);
        return CommonResult.success(workOrderMwdVo);
    }

    @PostMapping("getSerialNumber")
    public CommonResult serialNumberQuery(@RequestParam("serialNumber") String serialNumber) {

        return CommonResult.success(workOrderMwdService.selectSerialNumberQuery(serialNumber));
    }


    @PostMapping("update")
    public CommonResult update(@RequestBody @Valid WorkOrderMwdVo orderMwdVo) {
        if (orderMwdVo.getMwdId() == null) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        workOrderMwdService.update(orderMwdVo);
        WorkOrderMwdVo workOrderMwdVo = workOrderMwdService.info(orderMwdVo.getMwdId());
        return CommonResult.success(workOrderMwdVo);
    }

    @PostMapping("delete")
    public CommonResult delete(@RequestBody @Valid WorkOrderMwdVo orderMwdVo) {
        if (orderMwdVo.getMwdId() == null) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        workOrderMwdService.delete(orderMwdVo.getMwdId());
        return CommonResult.success();
    }

    /**
     * 台账完成存档
     *
     * @param orderMwd
     * @return
     */
    @PostMapping("finish")
    public CommonResult finish(@RequestBody @Valid WorkOrderMwd orderMwd) {
        if (orderMwd.getMwdId() == null) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        workOrderMwdService.updateFinish(orderMwd);
        return CommonResult.success();
    }

    /**
     * 仪器的组装与拆卸
     *
     * @param record
     * @return
     */
    @PostMapping("assemble")
    public CommonResult assemble(@RequestBody @Valid MwdDeviceAssembleRecord record) {
        if (record.getMwdId() == null) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        workOrderMwdService.assemble(record);
        return CommonResult.success();
    }

    /**
     * 检查更新的组装与拆卸信息，
     * 查看新装上的核心部件是否已经被使用了
     * @param record
     * @return
     */
    @PostMapping("assembleCheck")
    public CommonResult assembleCheck(@RequestBody @Valid MwdDeviceAssembleRecord record) {
        return CommonResult.success(workOrderMwdService.assembleCheck(record));
    }

//    /**
//     * 部件维修
//     *
//     * @param componentRepairMwdDto
//     * @return
//     */
//    @PostMapping("component/repair")
//    public CommonResult componentRepair(@RequestBody @Valid ComponentRepairMwdDto componentRepairMwdDto) {
//        workOrderMwdService.componentRepair(componentRepairMwdDto);
//        return CommonResult.success();
//    }

    /**
     * MWD维修统计
     *
     * @param mwdWorkStatisticsDto
     * @return
     */
    @PostMapping("statistics")
    public CommonResult statistics(@RequestBody MwdWorkStatisticsDto mwdWorkStatisticsDto) {
        List<MwdToolVo> listVo = workOrderMwdService.statistics(mwdWorkStatisticsDto);
        return CommonResult.success(listVo);
    }

    // workOrderMwd/statistics 年统计版
    @PostMapping("statistics/yearly")
    public CommonResult statisticsYearly(@RequestBody MwdWorkStatisticsDto mwdWorkStatisticsDto) {
        List<MwdStatisticsYearlyVo> listVo = workOrderMwdService.statisticsYearly(mwdWorkStatisticsDto);
        return CommonResult.success(listVo);
    }


    /**
     * 同步MWD维修历史数据
     */
    @PostMapping("sync/history")
    public CommonResult syncHistory() {
        workOrderMwdService.syncHistory();
        return CommonResult.success();
    }

    /**
     * MWD维修报告
     */
    @GetMapping("repair/report/{mwdId}")
    public CommonResult repairReport(@PathVariable("mwdId") Long mwdId) {
        if (mwdId == null) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        workOrderMwdService.generateRepairReport(mwdId);
        return CommonResult.success();
    }

    /**
     * MWD工单导出
     */
    @PostMapping("order/export")
    public CommonResult orderExport(@RequestBody(required = false) WorkOrderMwdQuery query) {
        if (query == null) {
            query = new WorkOrderMwdQuery();
        }
        workOrderMwdService.orderExportExcel(query);
        return CommonResult.success();
    }

    @PostMapping("labourHour")
    public CommonResult getMwdLabourHour(@RequestBody MwdHourStatisticsVo mwdHourStatisticsVo) {
        String year = mwdHourStatisticsVo.getYear();
        String memberId = mwdHourStatisticsVo.getMemberId();
        String startTime = mwdHourStatisticsVo.getStartTime();
        String endTime = mwdHourStatisticsVo.getEndTime();
        return CommonResult.success(workOrderMwdService.getMwdLabourHour(year, memberId, startTime, endTime));
    }

    @PostMapping("mwdWorkAmount")
    public CommonResult getMwdWorkAmount(@RequestBody MwdHourStatisticsVo mwdHourStatisticsVo) {

        return CommonResult.success(workOrderMwdService.getMwdWorkAmountYear(mwdHourStatisticsVo));
    }

    @PostMapping("mwdWorkAmount/week")
    public CommonResult getMwdWorkAmountWeekly(@RequestBody MwdHourStatisticsVo mwdHourStatisticsVo) {
        String year = mwdHourStatisticsVo.getYear();
        String week = mwdHourStatisticsVo.getWeekNumber();
        if (year == null || week == null) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        return CommonResult.success(workOrderMwdService.getMwdWorkAmountWeek(year, week));
    }

//    // 添加部件失效统计接口v1 根据component_repair_mwd的repair_type = 'REPAIR'来统计
//    // 返回 List<Map<String, Integer>>  每个map显示：<invName, repairCount>
//    @PostMapping("/component/repair/statistics")
//    public CommonResult getComponentStatistics(@RequestBody MwdHourStatisticsVo mwdHourStatisticsVo) {
//        if (mwdHourStatisticsVo == null) {
//            throw new BusinessException(ResultCode.PARAMS_ERROR);
//        }
//        String year = mwdHourStatisticsVo.getYear();
//        if (year == null) {
//            throw new BusinessException(ResultCode.PARAMS_ERROR);
//        }
//        return CommonResult.success(workOrderMwdService.getComponentStatistics(year));
//    }

    //添加部件失效统计接口v2 根据work_order_mwd_detail的failure_component_type来统计
    @PostMapping("/component/failed/statistics")
    public CommonResult getFailedStatistics(@RequestBody MwdHourStatisticsVo mwdHourStatisticsVo) {
        if (mwdHourStatisticsVo == null) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        String year = mwdHourStatisticsVo.getYear();
        if (year == null) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        return CommonResult.success(workOrderMwdService.getFailedComponentStatistics(year, mwdHourStatisticsVo.getMonthList()));
    }

    @PostMapping("sharedList/{current}/{size}")
    public CommonResult sharedList(@PathVariable long current, @PathVariable long size, @RequestBody(required = false) MwdSharedVo query) {
        IPage<MwdSharedVo> page = new Page<>(current, size);
        if (query == null) {
            query = new MwdSharedVo();
        }
        return CommonResult.success(workOrderMwdService.getMwdSharedList(page, query));
    }

    @PostMapping("sharedList_v2/{current}/{size}")
    public CommonResult sharedList_v2(@PathVariable long current, @PathVariable long size, @RequestBody(required = false) MwdSharedVo query) {
        IPage<MwdSharedVo> page = new Page<>(current, size);
        if (query == null) {
            query = new MwdSharedVo();
        }
        return CommonResult.success(workOrderMwdService.getMwdSharedList_v2(page, query));
    }

    @PostMapping("update/shareStatus")
    public CommonResult updateShareStatus(@RequestBody MwdSharedVo query) {

        if (query == null || ObjectUtils.isEmpty(query.getMwdId()) || ObjectUtils.isEmpty(query.getShareStatus())) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        workOrderMwdService.updateShareStatus(query);
        return CommonResult.success();
    }

    @GetMapping("owner/select")
    public CommonResult ownerSelect() {
        return CommonResult.success(workOrderMwdService.ownerSelect());
    }

}
