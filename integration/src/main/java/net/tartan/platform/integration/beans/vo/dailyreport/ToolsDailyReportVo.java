package net.tartan.platform.integration.beans.vo.dailyreport;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.enums.EnumReportType;
import net.tartan.platform.integration.entity.dailyreport.*;

import java.math.BigDecimal;
import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ToolsDailyReportVo {

    private EnumReportType reportType;

    private long lastModified;

    /**
     * 日期 yyyy-MM-dd
     */
    private String date;

    /**
     * 本报告时间区间
     */
    private String reportDate;

    /**
     * 井号
     */
    private String wellNumber;

    /**
     * 客户
     */
    private String client;

    /**
     * 井队
     */
    private String drillingCrew;

    /**
     * 人员到井日期yyyy-MM-dd HH:mm:ss
     */
    private String workPaDate;

    /**
     * 设备到井日期yyyy-MM-dd HH:mm:ss
     */
    private String workDaDate;

    /**
     * 累计待命时间 hours
     */
    private BigDecimal workCumstTime;

    /**
     * 当日待命时间 hours
     */
    private BigDecimal workCurstTime;

    /**
     * 累计井下时间 hours
     */
    private BigDecimal workCumdwTime;

    /**
     * 当日井下时间 hours
     */
    private BigDecimal workCurdwTime;

    /**
     * 入井深度m
     */
    private BigDecimal workEntryDepth;

    /**
     * 累计进尺m
     */
    private BigDecimal workCumFootage;

    /**
     * 钻头型号
     */
    private String bitType;

    /**
     * 水眼
     */
    private String bitWaterEye;

    /**
     * 钻头制造商
     */
    private String bitMan;

    private String bitIadc;

    private String bitTfa;

    /**
     * 本次入井日期和时间 yyyy-MM-dd HH:mm:ss
     */
    private String runEntryDate;

    /**
     * 当前井深
     */
    private BigDecimal runCurDepth;

    /**
     * 下钻时井深
     */
    private BigDecimal runDrillDepth;

    /**
     * 累计待命时间 hours
     */
    private BigDecimal runCumstTime;

    /**
     * 当日待命时间 hours
     */
    private BigDecimal runCurstTime;

    /**
     * 累计循环时间 hours
     */
    private BigDecimal runCumclTime;

    /**
     * 当日循环时间 hours
     */
    private BigDecimal runCurclTime;

    /**
     * 累计钻进时间 hours
     */
    private BigDecimal runCumdrTime;

    /**
     * 当日钻进时间 hours
     */
    private BigDecimal runCurdrTime;

    /**
     * 平均机械钻速 m/hours
     */
    private BigDecimal runAvgRop;

    /**
     * 累计进尺m
     */
    private BigDecimal runCumFootage;

    /**
     * 当日进尺m
     */
    private BigDecimal runCurFootage;

    /**
     * 井斜方位传感器到钻头的距离 m
     */
    private BigDecimal daSensorDist;

    /**
     * gamma到钻头的距离 m
     */
    private BigDecimal gammaDist;

    /**
     * 近钻头测点到钻头的距离m
     */
    private BigDecimal nearBitDist;

    /**
     * 泥浆的性能g/cm3
     */
    private BigDecimal mudDen;

    /**
     * 碱度
     */
    private BigDecimal mudAlk;

    /**
     * 塑性粘度(mPa.s)
     */
    private BigDecimal mudPlsVis;

    /**
     * 屈服值(pa)
     */
    private BigDecimal mudYeVal;

    /**
     * 漏斗粘度(s)
     */
    private BigDecimal mudFunVis;

    /**
     * 静切力(pa)
     */
    private BigDecimal mudSsFor;

    /**
     * 含砂(%)
     */
    private BigDecimal mudSandCon;

    /**
     * 固相（%）
     */
    private BigDecimal mudSolidPha;

    /**
     * API失水(ml)
     */
    private BigDecimal mudWaterLoss;

    /**
     * 泥饼(mm)
     */
    private BigDecimal mudCake;

    /**
     * D&I温度(oC)
     */
    private BigDecimal mudDiTemp;

    /**
     * 泥浆体系
     */
    private String mudSys;

    /**
     * 频率/速度
     */
    private BigDecimal mwdSpeed;

    /**
     * 噪音比值
     */
    private BigDecimal mwdNoiseRatio;

    /**
     * SPT1/2强度
     */
    private BigDecimal mwdSptInt;

    private static final long serialVersionUID = 1L;

    /**
     * 工况
     */
    private List<ToolsDailyActivity> activityList;
    /**
     * 钻进参数
     */
    private List<ToolsDailyDrill> drillList;
    /**
     * 仪器设备情况
     */
    private List<ToolsDailyDevice> deviceList;
    /**
     * 测斜记录
     */
    private List<ToolsDailyDeviation> deviationList;
    /**
     * 钻具组合
     */
    private List<ToolsDailyBha> bhaList;
}
