package net.tartan.platform.integration.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.common.beans.ResultCode;
import net.tartan.platform.integration.beans.dto.ProjectInfoDto;
import net.tartan.platform.integration.beans.query.ProjectQuery;
import net.tartan.platform.integration.beans.vo.ProjectInfoVo;

import net.tartan.platform.integration.exception.BusinessException;
import net.tartan.platform.integration.service.IProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * project 项目管理接口
 */
@RestController
@RequestMapping("/project")
public class ProjectController {
    @Autowired
    private IProjectService projectService;

    @PostMapping("add")
    public CommonResult addProject(@RequestBody ProjectInfoDto dto) {
        projectService.addProject(dto);
        return CommonResult.success();
    }

    @GetMapping("info")
    public CommonResult getProjectInfo(@RequestParam("projectId") long projectId) {
        ProjectInfoVo projectInfoVo = projectService.getInfo(projectId);
        return CommonResult.success(projectInfoVo);
    }

    @PostMapping("list/{current}/{size}")
    public CommonResult list(@PathVariable long current, @PathVariable long size, @RequestBody(required = false) ProjectQuery query) {
        IPage<ProjectInfoVo> page = new Page<>(current, size);
        if (query == null) {
            query = new ProjectQuery();
        }
        projectService.listPage(page, query);
        return CommonResult.success(page);
    }

    @PostMapping("listWriteable/{current}/{size}")
    public CommonResult list_writeable(@PathVariable long current, @PathVariable long size, @RequestBody(required = false) ProjectQuery query) {
        IPage<ProjectInfoVo> page = new Page<>(current, size);
        if (query == null) {
            query = new ProjectQuery();
        }
        projectService.listPage_writeable(page, query);
        return CommonResult.success(page);
    }

    @PostMapping("update")
    public CommonResult update(@RequestBody @Valid ProjectInfoDto dto) {
        if (dto.getId() == null) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        projectService.updateProject(dto);
        ProjectInfoVo projectInfoVo = projectService.getInfo(dto.getId());
        return CommonResult.success(projectInfoVo);
    }

    @PostMapping("finish")
    public CommonResult updateFinish(@RequestBody @Valid ProjectInfoDto dto) {
        if (dto.getId() == null) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        projectService.updateFinish(dto);
        ProjectInfoVo projectInfoVo = projectService.getInfo(dto.getId());
        return CommonResult.success(projectInfoVo);
    }

    @PostMapping("delete")
    public CommonResult delete(@RequestBody @Valid ProjectInfoDto dto) {
        if (dto.getId() == null) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        projectService.deleteProject(dto.getId());
        return CommonResult.success();
    }

//    @PostMapping("checkName")
//    public CommonResult checkName(@RequestParam("projectId") String projectId,
//                                  @RequestParam("projectName") String projectName,
//                                  @RequestParam("projectNumber") String projectNumber) {
//        projectService.checkName(projectId,projectName,projectNumber);
//        return CommonResult.success();
//    }

}
