package net.tartan.platform.integration.beans.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClientJobVo {

    private Long jobId;
    /**
     * 井号
     */
    private String jobNumber;


    private int jobStatus;

    /**
     * 作业类型
     */
    private Long jobType;

    /**
     * 创建时间
     */
    private Date createTime;
}
