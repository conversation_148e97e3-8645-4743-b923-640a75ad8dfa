package net.tartan.platform.integration.controller;

import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.integration.beans.request.*;
import net.tartan.platform.integration.beans.vo.MenuInfoVo;
import net.tartan.platform.integration.beans.vo.MenuUpdateVo;
import net.tartan.platform.integration.entity.MenuInfo;
import net.tartan.platform.integration.service.MenuResourceService;
import net.tartan.platform.integration.service.MenuService;
import net.tartan.platform.integration.service.RoleMenuInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * menu 用户菜单管理
 */
@RestController
@RequestMapping("menu")
public class MenuController {

    @Autowired
    private MenuService menuService;
    @Autowired
    private RoleMenuInfoService roleMenuInfoService;
    @Autowired
    private MenuResourceService menuResourceService;

    @PostMapping("add")
    public CommonResult add(@RequestBody @Validated AddMenuInfoParams params) {
        menuService.add(params);
        return CommonResult.success(null);
    }

    @PostMapping("update")
    public CommonResult update(@RequestBody @Validated UpdateMenuInfoParams updateMenuInfoParams) {
        menuService.update(updateMenuInfoParams);
        return CommonResult.success();
    }

    @RequestMapping("delete")
    public CommonResult delete(@RequestParam("id") long id) {
        menuService.delete(id);
        return CommonResult.success();
    }

    @GetMapping("info")
    public CommonResult getInfo(@RequestParam("id") long id) {
        MenuInfo info = menuService.getInfo(id);
        return CommonResult.success(info);
    }

    /**
     * 根据条件查询菜单(不具层级关系)
     * @param query 查询条件
     * @return
     */
    @PostMapping("query")
    public CommonResult query(@RequestBody MenuInfo query) {
        List<MenuInfo> result = menuService.query(query);
        return CommonResult.success(result);
    }

    /**
     * 获取菜单树
     * @return
     */
    @RequestMapping("tree")
    public CommonResult queryWithHierarchy() {
        List<MenuInfoVo> result = menuService.getTree();
        return CommonResult.success(result);
    }

    /**
     * 获取角色下已配置的菜单id列表
     * @param roleId 角色id
     * @return
     */
    @GetMapping("queryIdsByRole")
    public CommonResult queryIdsByRole(@RequestParam("roleId") long roleId) {
        List<Long> result = roleMenuInfoService.getIdListByRole(roleId);
        return CommonResult.success(result);
    }

    /**
     * 查询某角色下的菜单(具有层级关系)
     * @param roleId 角色id
     * @return
     */
    @GetMapping("getTreeByRole")
    public CommonResult queryHierarchyListByRole(@RequestParam("roleId") long roleId) {
        List<MenuInfoVo> result = roleMenuInfoService.getListByRole(roleId);
        return CommonResult.success(result);
    }

    /**
     * 查询某用户下的菜单(具有层级关系)
     * @param userId 用户id
     * @return
     */
    @GetMapping("getTreetByUser")
    public CommonResult queryHierarchyListByUser(@RequestParam("userId") long userId) {
        List<MenuInfoVo> result = roleMenuInfoService.getListByUser(userId);
        return CommonResult.success(result);
    }

    /**
     * 给角色分配资源
     * @param resourceAssignParams
     * @return
     */
    @PostMapping("resource/assign")
    public CommonResult assign(@RequestBody @Validated ResourceAssignParams resourceAssignParams) {
        menuResourceService.assign(resourceAssignParams);
        return CommonResult.success(null);
    }

    /**
     * 删除角色下某一个资源
     * @param params
     * @return
     */
    @PostMapping("resource/delete")
    public CommonResult delete(@RequestBody @Validated DeleteMenuResourceRelationParams params) {
        menuResourceService.delete(params);
        return CommonResult.success();
    }

    /**
     * 批量删除角色下多个资源
     * @param params
     * @return
     */
    @PostMapping("resource/batchDelete")
    public CommonResult batchDelete(@RequestBody @Validated BatchDeleteMenuResourceRelationParams params) {
        menuResourceService.batchDelete(params);
        return CommonResult.success();
    }

    /**
     * 修改同级下的排序id
     * @param params
     * @return
     */
    @PostMapping("update/sort")
    public CommonResult updateSort(@RequestBody MenuUpdateVo params) {
        int line = menuService.updateSortByParentId(params);
        return CommonResult.success(line);
    }

}
