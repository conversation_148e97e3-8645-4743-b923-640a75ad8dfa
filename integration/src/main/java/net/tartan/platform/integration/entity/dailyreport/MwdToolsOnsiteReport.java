package net.tartan.platform.integration.entity.dailyreport;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.constant.PfConstant;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * mwd_tools_onsite_report
 *
 * <AUTHOR>
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = PfConstant.MWD_TOOLS_ONSITE_REPORT)
public class MwdToolsOnsiteReport{
    @Transient
    private Long id;

    @Id
    @JsonIgnore
    private String reportId;

    /**
     * 作业id
     */
    @NotNull
    private Long jobId;
    private String jobNumber;

    /**
     * 工具
     */
    private List<MwdTool> toolList;
}
