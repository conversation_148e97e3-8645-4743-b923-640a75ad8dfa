//package net.tartan.platform.integration.beans.vo;
//
//import lombok.Data;
//import lombok.NoArgsConstructor;
//import net.tartan.platform.integration.utils.CommonUtil;
//
//import java.util.*;
//import java.util.stream.Collectors;
//
//@Data
//@NoArgsConstructor
//public class MwdHourVo {
//
//    //通过dataFormat()来处理
////    private List<Map<String,String>> resultList;
////
////    public List<Map<String,String>> dataFormat(String year, List<MwdHourStatisticsVo> list){
////        this.resultList = new ArrayList<>();
////        String[] monthList = new String[]{"Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"};
////        List<String> typeList = list.stream().map(MwdHourStatisticsVo::getType).distinct().collect(Collectors.toList());
////        for(String type : typeList){
////            //List boys = studentList.stream().filter(s->s.getGender() && s.getHeight() >= 1.8).collect(Collectors.toList());
////            List<MwdHourStatisticsVo> typeHour = list.stream().filter(
////                    mwdHourStatisticsVo -> mwdHourStatisticsVo.getType().equals(type)
////            ).collect(Collectors.toList());
////            typeHour = typeHourFix(year,typeHour);
////            Map<String,String> monthResult = new TreeMap(new Comparator() {
////                @Override
////                public int compare(Object o1, Object o2) {
////                    String str1 = o1.toString();
////                    String str2 = o2.toString();
////                    if(!str1.contains("-")){
////                        return -1;
////                    }else if(!str2.contains("-")){
////                        return 1;
////                    }
////                    int num1 = Integer.parseInt(str1.substring(str1.indexOf("-") + 1));
////                    int num2 = Integer.parseInt(str2.substring(str2.indexOf("-") + 1));
////                    return num1 - num2;
////                }
////            });
////            // 按照日期排序
////            double total = 0.0;
////            for(int i = 0; i < 12; i++){
////                if(typeHour.get(i) != null ){
////                    MwdHourStatisticsVo mwdHourStatisticsVo = typeHour.get(i);
////                    double hour = mwdHourStatisticsVo.getHour()==null||mwdHourStatisticsVo.getHour() < 0 ? 0.0 : mwdHourStatisticsVo.getHour();
////                    monthResult.put(mwdHourStatisticsVo.getDate(), hour+"");
////                    total+=hour;
////                }
////                else {
////                    monthResult.put(monthList[i] , "");
////                }
////            }
//////            monthResult.put("Total",total+"");
////            monthResult.put("type",type);
////            this.resultList.add(monthResult);
////        }
////        Map<String,String> totalMap = getTotal(year,list);
////        resultList.add(totalMap);
////        return this.resultList;
////    }
////
////    private static String monthFormat(String date){
////
////        String getMonth = date.substring(date.indexOf("-") + 1);
////        switch (getMonth){
////            case "01":
////                return "Jan";
////            case "02":
////                return "Feb";
////            case "03":
////                return "Mar";
////            case "04":
////                return "Apr";
////            case "05":
////                return "May";
////            case "06":
////                return "Jun";
////            case "07":
////                return "Jul";
////            case "08":
////                return "Aug";
////            case "09":
////                return "Sep";
////            case "10":
////                return "Oct";
////            case "11":
////                return "Nov";
////            case "12":
////                return "Dec";
////            default :
////                return "";
////        }
////    }
////
////    private List<MwdHourStatisticsVo> typeHourFix (String year, List<MwdHourStatisticsVo> typeHour){
////
////        //初始化一年的数据格式
////        List<MwdHourStatisticsVo> initList = new ArrayList<>();
////        for(int i = 0; i < 12; i++){
////            MwdHourStatisticsVo mwdHourStatisticsVo;
////            String month = i + 1 < 10 ? "0" + ( i + 1 ) : "" + ( i + 1 );
////            String date = year + "-" + month;
////            List<MwdHourStatisticsVo> tempList = typeHour.stream().filter(o -> o.getDate().equals(date)).collect(Collectors.toList());
////            if(tempList.size() > 0){
////                mwdHourStatisticsVo = tempList.get(0);
////            }else{
////                mwdHourStatisticsVo = MwdHourStatisticsVo.builder().date(date).build();
////            }
////
////            initList.add(mwdHourStatisticsVo);
////        }
////        return initList;
////    }
////
////    private static Map<String,String> getTotal(String year, List<MwdHourStatisticsVo> list){
////
////        double[] resultTotal = new double[12];
////        for(MwdHourStatisticsVo mwdHourStatisticsVo : list){
////            String date = mwdHourStatisticsVo.getDate();
////            int index = Integer.parseInt(date.substring(date.indexOf("-")+1)) -1;
////            resultTotal[index] += mwdHourStatisticsVo.getHour() == null ? 0.0 : mwdHourStatisticsVo.getHour();
////        }
////
////        Map<String,String> monthResult = new TreeMap(new Comparator() {
////            @Override
////            public int compare(Object o1, Object o2) {
////                String str1 = o1.toString();
////                String str2 = o2.toString();
////                if(!str1.contains("-")){
////                    return -1;
////                }else if(!str2.contains("-")){
////                    return 1;
////                }
////                int num1 = Integer.parseInt(str1.substring(str1.indexOf("-") + 1));
////                int num2 = Integer.parseInt(str2.substring(str2.indexOf("-") + 1));
////                return num1 - num2;
////            }
////        });
////        for(int a = 0; a<resultTotal.length; a++){
////            String month = a+1<10 ? year+"-0"+(a+1):year+"-"+(a+1);
////            String amount = resultTotal[a]+"";
////            monthResult.put(month,amount);
////        }
////        monthResult.put("type","Total");
////        return monthResult;
////    }
//
//}
