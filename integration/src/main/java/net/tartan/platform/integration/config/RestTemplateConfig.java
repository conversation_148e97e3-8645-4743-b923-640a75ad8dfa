package net.tartan.platform.integration.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * RestTemplate配置
 * 用于HTTP客户端通信
 * 
 * <AUTHOR>
 * @since 2025-07-11
 */
@Slf4j
@Configuration
public class RestTemplateConfig {

    @Autowired
    private SyncProperties syncProperties;

    /**
     * 配置RestTemplate
     * 
     * @return RestTemplate实例
     */
    @Bean
    public RestTemplate restTemplate() {
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.setRequestFactory(clientHttpRequestFactory());
        
        log.info("RestTemplate配置完成");
        return restTemplate;
    }

    /**
     * 配置HTTP请求工厂
     * 
     * @return ClientHttpRequestFactory
     */
    @Bean
    public ClientHttpRequestFactory clientHttpRequestFactory() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        
        // 设置连接超时时间
        factory.setConnectTimeout(syncProperties.getOa().getHttp().getConnectTimeout());
        
        // 设置读取超时时间
        factory.setReadTimeout(syncProperties.getOa().getHttp().getReadTimeout());
        
        log.info("HTTP客户端配置完成 - 连接超时: {}ms, 读取超时: {}ms", 
                syncProperties.getOa().getHttp().getConnectTimeout(),
                syncProperties.getOa().getHttp().getReadTimeout());
        
        return factory;
    }
}
