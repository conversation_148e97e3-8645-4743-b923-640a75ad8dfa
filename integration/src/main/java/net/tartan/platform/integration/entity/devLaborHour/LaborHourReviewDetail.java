package net.tartan.platform.integration.entity.devLaborHour;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.enums.EnumReviewStatus;

import java.util.Date;
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LaborHourReviewDetail {
    /**
     * 序号
     */
    private Integer sequence;
    /**
     * 审核人
     */
    private String reviewUser;

    /**
     * 审核人的memberId
     */
    private String reviewUserMemberId;

    /**
     * 审批状态
     */
    private EnumReviewStatus reviewStatus;

    /**
     * 审批日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date reviewDate;
}
