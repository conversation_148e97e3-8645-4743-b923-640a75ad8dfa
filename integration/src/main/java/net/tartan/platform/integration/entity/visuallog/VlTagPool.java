package net.tartan.platform.integration.entity.visuallog;

import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * <AUTHOR>
 * @since 2023-10-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class VlTagPool implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "tag_id", type = IdType.AUTO)
    private Integer tagId;

    /**
     * 标签名
     */
    private String tagName;

    /**
     * 标签描述
     */
    private String tagDesc;

    /**
     * 是否激活
     */
    private Integer active;

    /**
     * 单位
     */
    private String unitClass;

    private Integer yNPlot;

    private Integer yNEdit;

    private Integer yNPrec;

    private Integer yNUnitCnvt;

    private Date tDate;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


}
