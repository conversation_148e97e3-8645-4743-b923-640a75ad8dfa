package net.tartan.platform.integration.entity.dailyreport;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * mwd_surveys_activity
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MwdSurveysActivity{
    private Long id;

    /**
     * 调查报告id
     */
    private Long surveysReportId;

    /**
     * 日期yyyy-MM-dd
     */
    private String date;

    /**
     * 钻头位置
     */
    private BigDecimal bitDepth;

    /**
     * 测深
     */
    private BigDecimal surveyDepth;

    /**
     * 井斜
     */
    private BigDecimal inc;

    /**
     * 方位
     */
    private BigDecimal azi;

    /**
     * 磁倾角
     */
    private BigDecimal dipa;

    /**
     * 磁场强度
     */
    private BigDecimal magf;

    /**
     * 重力场
     */
    private BigDecimal grav;

    /**
     * 电池电压
     */
    private BigDecimal batv;

    /**
     * 温度
     */
    private BigDecimal temp;

    /**
     * 备注
     */
    private String comments;

    /**
     * 地层
     */
    private String formation;

    /**
     * 岩芯
     */
    private String rockCore;
}
