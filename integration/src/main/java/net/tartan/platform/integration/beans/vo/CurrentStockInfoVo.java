package net.tartan.platform.integration.beans.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class CurrentStockInfoVo {
  /**
   * 库位名称
   */
  private String binName;
  /**
   * 仓库名称
   */
  private String warehouseName;
  /**
   * 存货大类编码
   */
  private String invClassCode;
  /**
   * 存货大类名称
   */
  private String invClassName;

  private String stockId;
  /**
   * 存货编码
   */
  private String invCode;
  /**
   * 存货名称
   */
  private String invName;
  /**
   * 存货型号
   */
  private String invStd;
  /**
   * 序列号
   */
  private String serialNumber;
  /**
   * 状态
   */
  private Long status;
  /**
   * 数量
   */
  private Integer quantity;
}
