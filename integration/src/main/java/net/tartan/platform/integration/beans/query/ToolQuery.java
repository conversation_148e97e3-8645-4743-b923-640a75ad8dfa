package net.tartan.platform.integration.beans.query;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.enums.EnumRiskType;
import net.tartan.platform.common.enums.EnumToolType;

import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ToolQuery {
  /**
   * 大类编号
   */
  private String invClassCode;

  private String fieldName;
  /**
   * 需要分组的统计的参数列表,里面每项值代表DeviceVo里的属性名，分组优先级由高到低。
   */
  private List<String> conditionFieldNameList;
  /**
   * 代表要统计的属性，是DeviceVo里的属性名。
   */
  private String counterFieldName;
  /**
   * 表示在统计counterFieldName 时，只关注出现在counterValueList中的参数值。
   */
  private List<String> counterValueList;

  private EnumToolType toolType;
  private String deviceTypeListStr;// 以字符串形式表示的列表
  private List<Long> deviceTypeList;

  /**
   * 品名
   */
  private String invName;
  private String parentInvName;

  /**
   * 温度类型
   */
  private String tempTypeName;

  /**
   * 状态列表
   */
  private List<Long> statusList;

  private List<String> fieldNameList;

  /**
   * 风险类型
   */
  private EnumRiskType riskType;
  private String riskTypeStr;

  /**
   * 厂家
   */
  private String manufacturer;

  /**
   * 存货编码
   */
  private String invCode;
  /**
   * 批次号
   */
  private String serialNumber;
  private List<String> serialNumberList;
  private String parentSerialNumber;

  /**
   * 旋导序列号
   */
  private String suSerialNumber;
  private String mwdSerialNumber;
  private String lcpSerialNumber;

  private List<Long> deviceIdList;

  @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
  private Date startDate;

  @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
  private Date endDate;

  private  Integer hasSn;

  private Boolean isBlur;

  private String orderBy;

  private String orderType;

  // 参数过滤
  private Long deviceId;
  private Long componentId;

  /**
   * 仪器类型
   */
  private Long deviceType;

  private String deviceTypeStr;

  /**
   * 仪器库存类型 库房盘点按照这个来分类
   */
  private Long stockType;
  private String stockTypeStr;

  /**
   * 创建时间
   */
  private Date createTime;

  /**
   * 更新时间
   */
  private Date updateTime;

  /**
   * 仪器状态
   * data_dictionary 表中 business_type 为 20 的所有均为仪器状态
   */
  private Long status;

  private String statusStr;

  // 新增字段

  //received/send date 接收/发出日期
  private String receivedSendDate;

  //配置数量（串数）
  private String configQuantity;

  //location地点
  private String location;

  //note
  private String note;

//    //配置类型 （基础配置齐备不含仪器、LWD-远端方位Gamma、LWD-近钻头方位Gamma、LWD-自然Gamma、常规MWD测斜、NA）
//    private String configType;

  //owner所属
  private String owner;

  //clientName客户名（如果所属是client则需要填具体的客户名）
  private String clientName;

  //规格（近钻类型）（温度类型（其部件core的temp_type））
  private String specification;

  //kitNumber所属的kit箱
  private String kitNumber;

  //上井建议
  private String wellSuggest;

  //完工时间（根据工单的完成时间来更新）
  private String finishDate;

  //新旧状态（1号、新）
  private String newOldStatus;

  //完工天数（TODAY()-完工时间+1）
  private Integer finishDays;

  //库存天数（工时：TODAY()-receivedDate+1）
  private Integer loadInDays;

  /**
   * 仪器创建人
   */
  private Long createdBy;
  private String createdByStr;

  /**
   * 最后修改人
   */
  private Long lastModifiedBy;
  private String lastModifiedByStr;

}
