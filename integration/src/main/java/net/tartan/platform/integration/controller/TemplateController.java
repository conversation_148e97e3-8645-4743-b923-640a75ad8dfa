package net.tartan.platform.integration.controller;

import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.common.beans.ResultCode;
import net.tartan.platform.integration.entity.TemplateItems;
import net.tartan.platform.integration.exception.BusinessException;
import net.tartan.platform.integration.service.ITemplateService;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 出入库模板
 * 管理出入库过程中的模板项
 */
@RestController
@RequestMapping("/template")
public class TemplateController {

    @Autowired
    private ITemplateService templateService;

    /**
     * 获取模板项列表
     * @param templateId 可选的模板ID过滤条件
     * @return 模板项列表
     */
    @GetMapping("/items")
    public CommonResult getTemplateItems(@RequestParam(required = false) Integer templateId) {
        return CommonResult.success(templateService.getTemplateItems(templateId));
    }

    /**
     * 通过ID获取模板项
     * @param id 模板项ID
     * @return 模板项详情
     */
    @GetMapping("/items/{id}")
    public CommonResult getTemplateItemById(@PathVariable Integer id) {
        TemplateItems item = templateService.getTemplateItemById(id);
        if (item == null) {
            return CommonResult.failed("找不到模板项");
        }
        return CommonResult.success(item);
    }

    /**
     * 创建新的模板项
     * @param templateItem 要创建的模板项
     * @return 成功结果
     */
    @PostMapping("/items")
    public CommonResult createTemplateItem(@RequestBody TemplateItems templateItem) {
        if (templateItem == null || ObjectUtils.isEmpty(templateItem.getTemplateId())) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        
        boolean success = templateService.createTemplateItem(templateItem);
        if (success) {
            return CommonResult.success();
        } else {
            return CommonResult.failed("创建模板项失败");
        }
    }

    /**
     * 更新现有模板项
     * @param id 模板项ID
     * @param templateItem 更新的模板项数据
     * @return 成功结果
     */
    @PutMapping("/items/{id}")
    public CommonResult updateTemplateItem(@PathVariable Integer id, @RequestBody TemplateItems templateItem) {
        if (templateItem == null) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        
        boolean success = templateService.updateTemplateItem(id, templateItem);
        if (success) {
            return CommonResult.success();
        } else {
            return CommonResult.failed("更新模板项失败");
        }
    }

    /**
     * 删除模板项
     * @param id 模板项ID
     * @return 成功结果
     */
    @DeleteMapping("/items/{id}")
    public CommonResult deleteTemplateItem(@PathVariable Integer id) {
        boolean success = templateService.deleteTemplateItem(id);
        if (success) {
            return CommonResult.success();
        } else {
            return CommonResult.failed("删除模板项失败");
        }
    }

    /**
     * 通过父ID获取模板项
     * @param parentId 父模板项ID
     * @return 子模板项列表
     */
    @GetMapping("/items/byParent/{parentId}")
    public CommonResult getTemplateItemsByParentId(@PathVariable Integer parentId) {
        return CommonResult.success(templateService.getTemplateItemsByParentId(parentId));
    }
    
    /**
     * 获取分类项（isCategory=1的项）
     * @param templateId 模板ID
     * @return 分类项列表
     */
    @GetMapping("/items/categories")
    public CommonResult getCategoryItems(@RequestParam Integer templateId) {
        if (ObjectUtils.isEmpty(templateId)) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        
        return CommonResult.success(templateService.getCategoryItems(templateId));
    }
}
