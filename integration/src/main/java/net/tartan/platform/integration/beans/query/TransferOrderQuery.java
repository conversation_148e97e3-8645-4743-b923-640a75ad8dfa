package net.tartan.platform.integration.beans.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.enums.EnumTransferReceiveStatus;

import java.util.List;

/**
 * 调拨单查询参数
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TransferOrderQuery {
    // 调拨单id
    private Long transferId;
    private List<Long> transferIdList;

    // 详情单id
    private List<Long> detailIdList;

    //调拨单号
    private String transferNumber;
    private String transferNumberAbs;

    //发出井号
    private String fromWellNumber;
    //发往井号
    private String toWellNumber;

    //发出kit号
    private String fromKitNumber;
    //发往kit号
    private String toKitNumber;

    //接收状态
    private String receiveStatus;

    // 查询仪器调拨状态的参数
    //所在井号
    private String wellNumber;
    // 所在kit号
    private String kitNumber;
    // 序列号
    private String serialNumber;
    private List<String> serialNumberList;

}
