package net.tartan.platform.integration.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.common.beans.ResultCode;
import net.tartan.platform.integration.beans.query.WellInfoQuery;
import net.tartan.platform.integration.beans.vo.WellInfoVo;
import net.tartan.platform.integration.beans.vo.WellJobNumberVo;
import net.tartan.platform.integration.entity.WellDeleteInfo;
import net.tartan.platform.integration.entity.WellInfo;
import net.tartan.platform.integration.exception.BusinessException;
import net.tartan.platform.integration.service.IWellInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * wellInfo 井管理
 */
@Slf4j
@RestController
@RequestMapping("wellInfo")
public class WellInfoController {
    @Autowired
    private IWellInfoService wellInfoService;

    /**
     * 根据井名模糊查询，每次只反10条数据
     *
     * @return
     */
    @PostMapping("fuzzyList")
    public CommonResult fuzzyList(@RequestParam("wellNumberKey") String wellNumberKey) {
        List<WellInfo> wellInfoList = wellInfoService.fuzzyList(wellNumberKey);
        return CommonResult.success(wellInfoList);
    }

    @PostMapping("list/{current}/{size}")
    public CommonResult list(@PathVariable long current,
                             @PathVariable long size,
                             @RequestBody WellInfoQuery wellInfoQuery) {
        IPage<WellInfoVo> page = new Page<>(current, size);
        wellInfoService.getListByPage(page, wellInfoQuery);
//        List<WellInfoVo> wellInfoList = wellInfoService.list(wellInfoQuery);
        return CommonResult.success(page);
    }

    @PostMapping("update")
    public CommonResult updateWellInfo(@RequestBody @Valid WellInfo wellInfo) {
        wellInfoService.updateById(wellInfo);
        return CommonResult.success();
    }

    @PostMapping("wellJob")
    public CommonResult getwellJob(){
        List<WellJobNumberVo> wellJobList = wellInfoService.getJobWellNumber();
        return CommonResult.success(wellJobList);
    }

//    @GetMapping("getAllShips")
//    public CommonResult getAllShips() {
//        List<String> ships = IWellInfoService.getAllShips();
//        return CommonResult.success(ships);
//    }

//    @PostMapping("info")
//    public CommonResult info(@RequestBody WellInfo wellInfo) {
//        WellInfo wellInfoList = IWellInfoService.info(wellInfo);
//        return CommonResult.success(wellInfoList);
//    }

//    @PostMapping("add")
//    public CommonResult addWellInfo(@RequestBody @Valid WellInfo wellInfo) {
//        IWellInfoService.save(wellInfo);
//        return CommonResult.success();
//    }
    @PostMapping("delete")
    public CommonResult deleteWellInfo(@RequestBody @Valid WellInfo wellInfo) {
        wellInfoService.removeById(wellInfo.getWellId());
        return CommonResult.success();
    }

    @PostMapping("delete_v2")
    public CommonResult deleteWellInfo_v2(@RequestBody @Valid WellDeleteInfo wellDeleteInfo) {
        if(wellDeleteInfo.getWellInfo().getWellId().isEmpty()){
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        if(wellDeleteInfo.getIsBlock()){
            wellInfoService.removeAndBlock(wellDeleteInfo.getWellInfo().getWellId());
        }else{
            wellInfoService.removeById(wellDeleteInfo.getWellInfo().getWellId());
        }
        return CommonResult.success();
    }

}
