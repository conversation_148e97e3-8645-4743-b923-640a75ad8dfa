package net.tartan.platform.integration.entity.deviceassemble.repaircheck;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class AzDmCheckData extends BaseCheckData {
    private List<CheckDataModel> ringOutTestList;
    private List<CheckDataModel> checksList;
    private List<CheckDataModel> dimensionsList;
    private List<CheckDataModel> rollTestList;
    private List<CheckDataModel> heatTestList;
    private List<CheckDataModel> functionTestList;
    private List<CheckDataModel> configurationCheckList;
//    private List<RingOutTest> ringOutTestList;
//    private List<Checks> checksList;
//    private List<Dimensions> dimensionsList;
//    private List<RollTest> rollTestList;
//    private List<HeatTest> heatTestList;
//    private List<FunctionTest> functionTestList;
//    private List<ConfigurationCheck> configurationCheckList;

    public AzDmCheckData init() {
        AzDmCheckData azDmCheckData = new AzDmCheckData();

        azDmCheckData.setRingOutTestList(ringOutTestListInit());

        azDmCheckData.setChecksList(checksListInit());

        azDmCheckData.setDimensionsList(dimensionsListInit());

        azDmCheckData.setRollTestList(rollTestListInit());

        azDmCheckData.setHeatTestList(heatTestListInit());

        azDmCheckData.setFunctionTestList(functionTestListInit());

        azDmCheckData.setConfigurationCheckList(configurationCheckListInit());

        //初始化故障诊断
        azDmCheckData.setFaultDiagnosis(new FaultDiagnosis().init());
        return azDmCheckData;
    }

    private List<CheckDataModel> ringOutTestListInit() {
        List<CheckDataModel> checkDataModelList = new ArrayList<>();

        CheckDataModel checkDataModel0 = new CheckDataModel();
        checkDataModel0.setName("U1-D1,U2-D2,…,U10-D10");
        checkDataModel0.setValue("<1Ω");
        checkDataModel0.setIn("");
        checkDataModel0.setOut("");
        checkDataModelList.add(checkDataModel0);

        CheckDataModel checkDataModel1 = new CheckDataModel();
        checkDataModel1.setName("U1-D2");
        checkDataModel1.setValue(">100KΩ");
        checkDataModel1.setIn("");
        checkDataModel1.setOut("");
        checkDataModelList.add(checkDataModel1);

        CheckDataModel checkDataModel2 = new CheckDataModel();
        checkDataModel2.setName("U1-D3");
        checkDataModel2.setValue(">1MΩ");
        checkDataModel2.setIn("");
        checkDataModel2.setOut("");
        checkDataModelList.add(checkDataModel2);

        CheckDataModel checkDataModel3 = new CheckDataModel();
        checkDataModel3.setName("U1-D4");
        checkDataModel3.setValue(">55KΩ");
        checkDataModel3.setIn("");
        checkDataModel3.setOut("");
        checkDataModelList.add(checkDataModel3);

        CheckDataModel checkDataModel4 = new CheckDataModel();
        checkDataModel4.setName("U1-D5");
        checkDataModel4.setValue(">45KΩ");
        checkDataModel4.setIn("");
        checkDataModel4.setOut("");
        checkDataModelList.add(checkDataModel4);

        CheckDataModel checkDataModel5 = new CheckDataModel();
        checkDataModel5.setName("U1-D6");
        checkDataModel5.setValue(">45KΩ");
        checkDataModel5.setIn("");
        checkDataModel5.setOut("");
        checkDataModelList.add(checkDataModel5);

        CheckDataModel checkDataModel6 = new CheckDataModel();
        checkDataModel6.setName("U1-D7");
        checkDataModel6.setValue(">90KΩ");
        checkDataModel6.setIn("");
        checkDataModel6.setOut("");
        checkDataModelList.add(checkDataModel6);

        CheckDataModel checkDataModel7 = new CheckDataModel();
        checkDataModel7.setName("U1-D8");
        checkDataModel7.setValue(">300KΩ");
        checkDataModel7.setIn("");
        checkDataModel7.setOut("");
        checkDataModelList.add(checkDataModel7);

        return checkDataModelList;
    }

    private List<CheckDataModel> checksListInit() {
        List<CheckDataModel> checkDataModelList = new ArrayList<>();

        CheckDataModel checkDataModel0 = new CheckDataModel();
        checkDataModel0.setName("Pin Test Top");
        checkDataModel0.setIn("");
        checkDataModel0.setOut("");
        checkDataModelList.add(checkDataModel0);

        CheckDataModel checkDataModel1 = new CheckDataModel();
        checkDataModel1.setName("Pin Test Btm");
        checkDataModel1.setIn("");
        checkDataModel1.setOut("");
        checkDataModelList.add(checkDataModel1);

        CheckDataModel checkDataModel2 = new CheckDataModel();
        checkDataModel2.setName("Sensor MDM Top");
        checkDataModel2.setIn("");
        checkDataModel2.setOut("");
        checkDataModelList.add(checkDataModel2);

        CheckDataModel checkDataModel3 = new CheckDataModel();
        checkDataModel3.setName("Sensor MDM Btm");
        checkDataModel3.setIn("");
        checkDataModel3.setOut("");
        checkDataModelList.add(checkDataModel3);

        CheckDataModel checkDataModel4 = new CheckDataModel();
        checkDataModel4.setName("DM MDM Top");
        checkDataModel4.setIn("");
        checkDataModel4.setOut("");
        checkDataModelList.add(checkDataModel4);

        CheckDataModel checkDataModel5 = new CheckDataModel();
        checkDataModel5.setName("DM MDM Btm");
        checkDataModel5.setIn("");
        checkDataModel5.setOut("");
        checkDataModelList.add(checkDataModel5);

        CheckDataModel checkDataModel6 = new CheckDataModel();
        checkDataModel6.setName("Transorb Tape");
        checkDataModel6.setIn("");
        checkDataModel6.setOut("");
        checkDataModelList.add(checkDataModel6);

        CheckDataModel checkDataModel7 = new CheckDataModel();
        checkDataModel7.setName("Transorb MDM");
        checkDataModel7.setIn("");
        checkDataModel7.setOut("");
        checkDataModelList.add(checkDataModel7);

        return checkDataModelList;
    }

    private List<CheckDataModel> dimensionsListInit() {
        List<CheckDataModel> checkDataModelList = new ArrayList<>();

        CheckDataModel checkDataModel0 = new CheckDataModel();
        checkDataModel0.setName("Top Box x Box Intermodule");
        checkDataModel0.setIn("");
        checkDataModel0.setOut("");
        checkDataModelList.add(checkDataModel0);

        CheckDataModel checkDataModel1 = new CheckDataModel();
        checkDataModel1.setName("Intermodule Housing");
        checkDataModel1.setIn("");
        checkDataModel1.setOut("");
        checkDataModelList.add(checkDataModel1);

        CheckDataModel checkDataModel2 = new CheckDataModel();
        checkDataModel2.setName("Barrel Top");
        checkDataModel2.setIn("");
        checkDataModel2.setOut("");
        checkDataModelList.add(checkDataModel2);

        CheckDataModel checkDataModel3 = new CheckDataModel();
        checkDataModel3.setName("Barrel Bottom");
        checkDataModel3.setIn("");
        checkDataModel3.setOut("");
        checkDataModelList.add(checkDataModel3);

        CheckDataModel checkDataModel4 = new CheckDataModel();
        checkDataModel4.setName("Bottom Interconnect Housing");
        checkDataModel4.setIn("");
        checkDataModel4.setOut("");
        checkDataModelList.add(checkDataModel4);

        return checkDataModelList;
    }

    private List<CheckDataModel> rollTestListInit() {
        List<CheckDataModel> checkDataModelList = new ArrayList<>();

        CheckDataModel checkDataModel0 = new CheckDataModel();
        checkDataModel0.setName("Inclination");
        checkDataModel0.setValue("");
        checkDataModelList.add(checkDataModel0);

        CheckDataModel checkDataModel1 = new CheckDataModel();
        checkDataModel1.setName("Azimuth");
        checkDataModel1.setValue("");
        checkDataModelList.add(checkDataModel1);

        CheckDataModel checkDataModel2 = new CheckDataModel();
        checkDataModel2.setName("Dip Angle");
        checkDataModel2.setValue("");
        checkDataModelList.add(checkDataModel2);

        CheckDataModel checkDataModel3 = new CheckDataModel();
        checkDataModel3.setName("Magnetic");
        checkDataModel3.setValue("");
        checkDataModelList.add(checkDataModel3);

        CheckDataModel checkDataModel4 = new CheckDataModel();
        checkDataModel4.setName("Gravity");
        checkDataModel4.setValue("");
        checkDataModelList.add(checkDataModel4);

        CheckDataModel checkDataModel5 = new CheckDataModel();
        checkDataModel5.setName("Test Report(Yes/No)");
        checkDataModel5.setValue("");
        checkDataModelList.add(checkDataModel5);

        return checkDataModelList;
    }

    private List<CheckDataModel> heatTestListInit() {
        List<CheckDataModel> checkDataModelList = new ArrayList<>();

        CheckDataModel checkDataMode0 = new CheckDataModel();
        checkDataMode0.setName("Max temp");
        checkDataMode0.setValue("");
        checkDataModelList.add(checkDataMode0);

        CheckDataModel checkDataMode1 = new CheckDataModel();
        checkDataMode1.setName("Pass(Yes/No)");
        checkDataMode1.setValue("");
        checkDataModelList.add(checkDataMode1);

        return checkDataModelList;
    }

    private List<CheckDataModel> functionTestListInit() {
        List<CheckDataModel> checkDataModelList = new ArrayList<>();

        CheckDataModel checkDataModel0 = new CheckDataModel();
        checkDataModel0.setName("Pass(Yes/No)");
        checkDataModel0.setValue("");
        checkDataModelList.add(checkDataModel0);

        CheckDataModel checkDataModel1 = new CheckDataModel();
        checkDataModel1.setName("Screenshot(Yes/No)");
        checkDataModel1.setValue("");
        checkDataModelList.add(checkDataModel1);

        return checkDataModelList;
    }

    private List<CheckDataModel> configurationCheckListInit() {
        List<CheckDataModel> checkDataModelList = new ArrayList<>();

        CheckDataModel checkDataModel0 = new CheckDataModel();
        checkDataModel0.setName("Firmware Version");
        checkDataModel0.setValue("");
        checkDataModelList.add(checkDataModel0);

        CheckDataModel checkDataModel1 = new CheckDataModel();
        checkDataModel1.setName("Hardware Version)");
        checkDataModel1.setValue("");
        checkDataModelList.add(checkDataModel1);

        CheckDataModel checkDataModel2 = new CheckDataModel();
        checkDataModel2.setName("Low Bat Threshold");
        checkDataModel2.setValue("");
        checkDataModelList.add(checkDataModel2);

        CheckDataModel checkDataModel3 = new CheckDataModel();
        checkDataModel3.setName("High Bat Threshold");
        checkDataModel3.setValue("");
        checkDataModelList.add(checkDataModel3);

        CheckDataModel checkDataModel4 = new CheckDataModel();
        checkDataModel4.setName("Configuration Report(Yes/No),need print");
        checkDataModel4.setValue("");
        checkDataModelList.add(checkDataModel4);

        return checkDataModelList;
    }
//    public AzDmCheckData init() {
//        AzDmCheckData azDmCheckData = new AzDmCheckData();
//
//        RingOutTest ringOutTest = new RingOutTest();
//        ringOutTestList = ringOutTest.init();
//        azDmCheckData.setRingOutTestList(ringOutTestList);
//
//        Checks checks = new Checks();
//        checksList = checks.init();
//        azDmCheckData.setChecksList(checksList);
//
//        Dimensions dimensions = new Dimensions();
//        dimensionsList = dimensions.init();
//        azDmCheckData.setDimensionsList(dimensionsList);
//
//        RollTest rollTest = new RollTest();
//        rollTestList = rollTest.init();
//        azDmCheckData.setRollTestList(rollTestList);
//
//        HeatTest heatTest = new HeatTest();
//        heatTestList = heatTest.init();
//        azDmCheckData.setHeatTestList(heatTestList);
//
//        FunctionTest functionTest = new FunctionTest();
//        functionTestList = functionTest.init();
//        azDmCheckData.setFunctionTestList(functionTestList);
//
//        ConfigurationCheck configurationCheck = new ConfigurationCheck();
//        configurationCheckList = configurationCheck.init();
//        azDmCheckData.setConfigurationCheckList(configurationCheckList);
//
//        return azDmCheckData;
//    }
}
