package net.tartan.platform.integration.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.common.beans.ResultCode;
import net.tartan.platform.integration.beans.dto.RepairRmaDto;
import net.tartan.platform.integration.beans.query.RepairRmaQuery;
import net.tartan.platform.integration.beans.query.WorkOrderMwdQuery;
import net.tartan.platform.integration.beans.vo.RepairRmaVo;
import net.tartan.platform.integration.entity.repair.RepairRma;
import net.tartan.platform.integration.exception.BusinessException;
import net.tartan.platform.integration.service.IRepairRmaService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * rma 返修单 前端控制器
 * </p>
 */
@RestController
@RequestMapping("/repairRma")
public class RepairRmaController {


    @Autowired
    private IRepairRmaService repairRmaService;

    @PostMapping("add")
    public CommonResult add(@RequestBody @Valid RepairRmaDto repairRmaDto) {

        Long repairId = repairRmaService.addRepair(repairRmaDto);
        RepairRmaVo repairInfo = repairRmaService.getRepairInfo(repairId, repairRmaDto.isNeedExcel());
        return CommonResult.success(repairInfo);
    }

    @GetMapping("addFromRepair")
    public CommonResult addFromRepair(@RequestParam("repairId") long repairId) {

        return CommonResult.success(repairRmaService.addFromRepair(repairId));
    }

    @PostMapping("selectFromMwd")
    public CommonResult selectFromMwd(@RequestBody @Valid WorkOrderMwdQuery query) {
        if(ObjectUtils.isEmpty(query)
                || ObjectUtils.isEmpty(query.getMwdId())
                || ObjectUtils.isEmpty(query.getAssembleComponent())
                || StringUtils.isEmpty(query.getRepairCode())){
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        Long repairId = repairRmaService.selectFromMwd(query);
        RepairRmaVo repairInfo = repairRmaService.getRepairInfo(repairId);
        return CommonResult.success(repairInfo);
    }

    @PostMapping("info")
    public CommonResult info(@RequestBody @Valid RepairRmaDto repairRmaDto) {
        if(ObjectUtils.isEmpty(repairRmaDto) || ObjectUtils.isEmpty(repairRmaDto.getRepairId())){
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        RepairRmaVo repairInfo = repairRmaService.getRepairInfo(repairRmaDto.getRepairId(), repairRmaDto.isNeedExcel());
        return CommonResult.success(repairInfo);
    }

    @PostMapping("update")
    public CommonResult update(@RequestBody @Valid RepairRmaDto repairRma) {
        if(ObjectUtils.isEmpty(repairRma.getRepairId())){
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        repairRmaService.updateRepair(repairRma);
        RepairRmaVo repairInfo = repairRmaService.getRepairInfo(repairRma.getRepairId(), repairRma.isNeedExcel());
        return CommonResult.success(repairInfo);
    }

    @PostMapping("receive")
    public CommonResult receive(@RequestBody @Valid RepairRma repairRma) {
        if(ObjectUtils.isEmpty(repairRma.getRepairId())){
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        repairRmaService.receive(repairRma.getRepairId());
        RepairRmaVo repairInfo = repairRmaService.getRepairInfo(repairRma.getRepairId());
        return CommonResult.success(repairInfo);
    }
    @PostMapping("deleteRepair")
    public CommonResult deleteRepair(@RequestBody @Valid RepairRma repairRma) {
        if(ObjectUtils.isEmpty(repairRma.getRepairId())){
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        repairRmaService.deleteRepair(repairRma.getRepairId());
        return CommonResult.success();
    }

    @PostMapping("list/{current}/{size}")
    public CommonResult getListByPage(@PathVariable long current,
                                       @PathVariable long size,
                                       @RequestBody RepairRmaQuery repairRmaQuery) {
        IPage<RepairRmaVo> page = new Page<>(current, size);
        if(ObjectUtils.isEmpty(repairRmaQuery)){
            repairRmaQuery = new RepairRmaQuery();
        }
        repairRmaService.getListByPage(page, repairRmaQuery);
        return CommonResult.success(page);
    }

}
