package net.tartan.platform.integration.entity.dailyreport;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.constant.PfConstant;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * mwd_failure_report
 *
 * <AUTHOR>
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@Document(collection = PfConstant.MWD_FAILURE_REPORT)
public class MwdFailureReport implements Serializable {
    @Transient
    private Long id;

    @Id
    @JsonIgnore
    private String reportId;

    /**
     * 作业id
     */
    @NotNull
    private Long jobId;
    private String jobNumber;
    /**
     * 日期
     */
    @NotEmpty
    private String date;

    /**
     * 仪器箱
     */
    private String kit;
    /**
     * 库房是否确认
     */
    private Integer isConfirm;

    /**
     * 工程师
     */
    private String operator;

    /**
     * 客户公司
     */
    private String client;

    /**
     * 井号
     */
    private String wellNumber;
    /**
     * 位置
     */
    private String location;

    /**
     * 区块
     */
    private String field;

    /**
     * 井队号
     */
    private String drillingRig;

    /**
     * 失效日期yyyy-MM-dd
     */
    private String failureDate;
    /**
     * 入井日期
     */
    private String inHoleDate;

    /**
     * 报告日期yyyy-MM-dd
     */
    private String reportDate;

    /**
     * 失效描述
     */
    private String failureSymptoms;

    /**
     * 采取措施及效果
     */
    private String actionTaken;

    /**
     * 地面检查及结果
     */
    private String surfaceInspection;

    /**
     * 车间调查结果
     */
    private String shopInspectionc;

    /**
     * 改进计划/措施
     */
    private String improvementPlanMeasures;

//    /**
//     * 是否地面损坏
//     */
//    private Integer surfaceFailed;
//
//    /**
//     * 是否脉冲损坏
//     */
//    private Integer pulserFailed;
//
//    /**
//     * 是否探管损坏
//     */
//    private Integer dmFailed;
//
//    /**
//     * 是否电池1损坏
//     */
//    private Integer bat1Failed;
//
//    /**
//     * 是否电池2损坏
//     */
//    private Integer bat2Failed;
//
//    /**
//     * 是否司显损坏
//     */
//    private Integer rfdFailed;
//
//    /**
//     * 是否伽马损坏
//     */
//    private Integer gammaFailed;
//
//    /**
//     * 是否井下损坏
//     */
//    private Integer downholeFailed;
//
//    /**
//     * 是否传感器损坏
//     */
//    private Integer transducerFailed;
//
//    /**
//     * 是否线缆损坏
//     */
//    private Integer cablesFailed;
//
//    /**
//     * 是否电脑损坏
//     */
//    private Integer computersFailed;
//
//    /**
//     * 是否软件损坏
//     */
//    private Integer softwareFailed;
//
//    /**
//     * 是否司显接受盒损坏
//     */
//    private Integer rfdBoxFailed;
//
//    /**
//     * 是否其他损坏
//     */
//    private Integer otherFailed;

    /**
     * 井下总时间
     */
    private BigDecimal totalDowntime;

    /**
     * 是否发现问题（是：1，否：2，其他：3）
     */
    private Integer findProblem;

    /**
     * 排量
     */
    private BigDecimal flowRate;

    /**
     * 限流环
     */
    private BigDecimal orfice;

    /**
     * 蘑菇头
     */
    private BigDecimal poppet;

    /**
     * 脉宽
     */
    private BigDecimal pulsewth;

    /**
     * 井下时间
     */
    private BigDecimal runHours;

    /**
     * 测深
     */
    private BigDecimal measDepth;

    /**
     * 井斜
     */
    private BigDecimal inc;

    /**
     * 方位
     */
    private BigDecimal azm;

    /**
     * 磁偏角
     */
    private BigDecimal magdec;

    /**
     * 温度
     */
    private BigDecimal temp;

    /**
     * 电池电压
     */
    private BigDecimal batteryVolts;
    /**
     * 趟次
     */
    private Integer run;
    /**
     * 工具
     */
    private List<MwdTool> toolList;
    private List<MwdComponent> componentList;

    private static final long serialVersionUID = 1L;
}
