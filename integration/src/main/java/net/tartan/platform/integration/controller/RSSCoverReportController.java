package net.tartan.platform.integration.controller;

import lombok.extern.slf4j.Slf4j;
import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.common.beans.ResultCode;
import net.tartan.platform.integration.entity.dailyreport.RSS.RSSCoverReport;
import net.tartan.platform.integration.service.report.RSSCoverReportServiceImpl;
import net.tartan.platform.integration.service.report.UpdateRSSCoverReportParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * RSS封面报告控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/report/rss/cover")
public class RSSCoverReportController {

    @Autowired
    private RSSCoverReportServiceImpl rssCoverReportService;

    /**
     * 添加RSS封面报告
     */
    @PostMapping
    public CommonResult<String> addReport(@RequestBody @Valid RSSCoverReport report) {
        log.info("添加RSS封面报告, jobId: {}", report.getJobId());
        try {
            rssCoverReportService.addReport(report);
            return CommonResult.success("添加成功");
        } catch (Exception e) {
            log.error("添加RSS封面报告失败", e);
            return CommonResult.failed(e.getMessage());
        }
    }

    /**
     * 根据ID获取RSS封面报告
     */
    @GetMapping("/{id}")
    public CommonResult<RSSCoverReport> getReport(@PathVariable("id") Long id) {
        log.info("获取RSS封面报告, id: {}", id);
        try {
            RSSCoverReport report = rssCoverReportService.getReport(id);
            if (report == null) {
                return CommonResult.failed("报告不存在");
            }
            return CommonResult.success(report);
        } catch (Exception e) {
            log.error("获取RSS封面报告失败", e);
            return CommonResult.failed(e.getMessage());
        }
    }

    /**
     * 更新RSS封面报告
     */
    @PutMapping("/{id}")
    public CommonResult<String> updateReport(@PathVariable("id") Long id, @RequestBody @Valid RSSCoverReport report) {
        log.info("更新RSS封面报告, id: {}", id);
        try {
            UpdateRSSCoverReportParams params = new UpdateRSSCoverReportParams();
            params.setId(id);
            params.setReport(report);
            rssCoverReportService.updateReport(params);
            return CommonResult.success("更新成功");
        } catch (Exception e) {
            log.error("更新RSS封面报告失败", e);
            return CommonResult.failed(e.getMessage());
        }
    }

    /**
     * 删除RSS封面报告
     */
    @DeleteMapping("/{id}")
    public CommonResult<String> deleteReport(@PathVariable("id") Long id) {
        log.info("删除RSS封面报告, id: {}", id);
        try {
            rssCoverReportService.deleteReport(id);
            return CommonResult.success("删除成功");
        } catch (Exception e) {
            log.error("删除RSS封面报告失败", e);
            return CommonResult.failed(e.getMessage());
        }
    }

    /**
     * 根据作业编号获取RSS封面报告
     */
    @GetMapping("/job/{jobNumber}")
    public CommonResult<RSSCoverReport> getReportByJobNumber(@PathVariable("jobNumber") String jobNumber) {
        log.info("根据作业编号获取RSS封面报告, jobNumber: {}", jobNumber);
        try {
            RSSCoverReport report = rssCoverReportService.getSyncReport(jobNumber, null);
            if (report == null) {
                return CommonResult.failed("报告不存在");
            }
            return CommonResult.success(report);
        } catch (Exception e) {
            log.error("根据作业编号获取RSS封面报告失败", e);
            return CommonResult.failed(e.getMessage());
        }
    }
} 