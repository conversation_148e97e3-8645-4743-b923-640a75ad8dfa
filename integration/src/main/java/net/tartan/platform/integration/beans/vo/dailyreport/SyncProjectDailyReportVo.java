package net.tartan.platform.integration.beans.vo.dailyreport;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.enums.EnumReportType;
import net.tartan.platform.integration.entity.dailyreport.ProjectDailyActivity;
import net.tartan.platform.integration.entity.dailyreport.ProjectDailyStatistic;

import java.math.BigDecimal;
import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SyncProjectDailyReportVo {

    private EnumReportType reportType;

    private long lastModified;

    /**
     * 汇报日期
     */
    private String date;

    /**
     * 天气
     */
    private String weather;

    /**
     * 汇报人
     */
    private String reporter;

    /**
     * 作业公司
     */
    private String operatorCompany;

    /**
     * 井名
     */
    private String wellNumber;

    /**
     * 钻机
     */
    private String drillingMachine;

    /**
     * 井深
     */
    private BigDecimal depth;

    /**
     * 套管鞋(m)
     */
    private BigDecimal casingShoe;

    /**
     * 套管尺寸(mm)
     */
    private BigDecimal casingSize;

    /**
     * 井眼尺寸 (mm)
     */
    private BigDecimal boreholeSize;

    /**
     * 设计井深
     */
    private BigDecimal designSize;

    /**
     * 开趟钻数
     */
    private Integer run;

    /**
     * 泥浆体系
     */
    private String mudSystem;

    /**
     * 密度 (g/cm3)
     */
    private BigDecimal density;

    /**
     * 粘度(s)
     */
    private BigDecimal viscosity;

    /**
     * 含油%
     */
    private BigDecimal oilContent;

    /**
     * 固含%
     */
    private BigDecimal solidContent;

    /**
     * 含砂%
     */
    private BigDecimal sandContent;

    /**
     * 循环温度（℃）
     */
    private BigDecimal temp;

    /**
     * 钻头厂家
     */
    private String drillMan;
    private String drillInvCode;
    private String drillSerialNumber;

    /**
     * 钻头喷嘴
     */
    private String drillNozzle;

    /**
     * 入井次数
     */
    private BigDecimal drillIntoNum;

    /**
     * 钻头纯钻时间（h）
     */
    private BigDecimal drillTime;

    /**
     * 螺杆厂家
     */
    private String screwMan;
    private String screwInvCode;
    private String screwSerialNumber;

    /**
     * 螺杆尺寸
     */
    private BigDecimal screwSize;

    /**
     * 入井次数
     */
    private BigDecimal screwIntoNum;

    /**
     * 螺杆循环时间（h）
     */
    private BigDecimal screwTime;

    /**
     * 震击器厂家
     */
    private String knMan;
    private String knInvCode;
    private String knSerialNumber;

    /**
     * 震击器尺寸
     */
    private BigDecimal knSize;

    /**
     * 入井次数
     */
    private BigDecimal knIntoNum;

    /**
     * 震击器入井时间（h）
     */
    private BigDecimal knTime;

    /**
     * 水力厂家
     */
    private String wpMan;
    private String wpInvCode;
    private String wpSerialNumber;

    /**
     * 水力尺寸
     */
    private BigDecimal wpSize;

    /**
     * 入井次数
     */
    private BigDecimal wpIntoNum;

    /**
     * 水力循环时间（h）
     */
    private BigDecimal wpTime;

    /**
     * 所钻层位
     */
    private String drillPos;

    /**
     * 井深（m)
     */
    private BigDecimal drillDepth;

    /**
     * 所剩进尺（m）
     */
    private BigDecimal remainFootage;

    /**
     * 日进尺（m)
     */
    private BigDecimal dailyFootage;

    /**
     * 单日纯钻（h）
     */
    private BigDecimal odDrillTime;

    /**
     * 单日循环（h）
     */
    private BigDecimal odCircleTime;

    /**
     * 累计纯钻（h）
     */
    private BigDecimal cumDrillTime;

    /**
     * 累计循环（h）
     */
    private BigDecimal cumCircleTime;

    /**
     * 滑动钻时（min/m）
     */
    private BigDecimal sildeDrillTime;

    /**
     * 复合钻时（min/m）
     */
    private BigDecimal rcDrillTime;

    /**
     * 仪器零长（m）
     */
    private BigDecimal zeroLength;

    /**
     * 仪器信号强度
     */
    private BigDecimal ss;

    /**
     * 排量（L/S）
     */
    private BigDecimal cc;

    /**
     * 泵压（Mpa）
     */
    private BigDecimal pp;

    /**
     * 上提/下放摩阻（T）
     */
    private BigDecimal friction;

    /**
     * 钻进扭矩（KN.m）
     */
    private BigDecimal torque;

    /**
     * 项目经理
     */
    private String manager;

    /**
     * 定向工程师（带队）
     */
    private String deLeader;

    /**
     * 定向工程师
     */
    private String de;

    /**
     * 仪器工程师（带队）
     */
    private String ieLeader;

    /**
     * 仪器工程师
     */
    private String ie;

    /**
     * 人员到井时间
     */
    private String perArrTime;

    /**
     * 设备到井时间
     */
    private String equArrTime;

    /**
     * 我方提供服务
     */
    private String serPro;

    /**
     * 钻具组合
     */
    private String bha;

    /**
     * 本趟入转盘面时间
     */
    private String intoTurnTime;

    /**
     * 本趟出转盘面时间
     */
    private String outerTurnTime;

    /**
     * 纯钻时间
     */
    private BigDecimal tripDrillTime;

    /**
     * 循环时间
     */
    private BigDecimal tripCircleTime;

    /**
     * 本趟入井总时间
     */
    private BigDecimal tripTotalTime;

    /**
     * 起钻原因
     */
    private String pullDrillRs;

    /**
     * 螺杆详情
     */
    private String screwDetail;

    private List<ProjectDailyStatistic> statisticList;

    private List<ProjectDailyActivity> activityList;
}
