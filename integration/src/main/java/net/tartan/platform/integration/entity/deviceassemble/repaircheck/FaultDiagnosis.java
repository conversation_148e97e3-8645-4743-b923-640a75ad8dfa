package net.tartan.platform.integration.entity.deviceassemble.repaircheck;

import lombok.Data;

/**
 * <p>
 * 故障诊断
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-09
 */
@Data
public class FaultDiagnosis {

    /**
     * 入场拍照
     */
    private String inPicture;

    /**
     * 入厂初步检查结果
     */
    private String preliminaryTestResult;
    /**
     * 检查与测试结果
     */
    private String testResult;

    /**
     * 外观检测结果
     */
    private String faceCheckResults;

    /**
     * 复制故障现象结果
     */
    private String faultReappearResults;

    /**
     * 下载数据分析
     */
    private String dataAnalysis;
    /**
     * 根本原因&改善方法
     */
    private String rootReasonAndImproveMethod;
    /**
     * 维修建议
     */
    private String repairAdvice;

    public FaultDiagnosis init() {
        FaultDiagnosis faultDiagnosis = new FaultDiagnosis();
        faultDiagnosis.setInPicture("");
        faultDiagnosis.setPreliminaryTestResult("");
        faultDiagnosis.setTestResult("");
        faultDiagnosis.setFaceCheckResults("");
        faultDiagnosis.setFaultReappearResults("");
        faultDiagnosis.setDataAnalysis("");
        faultDiagnosis.setRootReasonAndImproveMethod("");
        faultDiagnosis.setRepairAdvice("");
        return faultDiagnosis;
    }


}
