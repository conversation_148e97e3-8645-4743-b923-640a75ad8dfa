package net.tartan.platform.integration.entity.deviceassemble;

import lombok.Data;
import net.tartan.platform.common.constant.PfConstant;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
@Document(collection = PfConstant.MWD_DEVICE_ASSEMBLE_RECORD)
public class MwdDeviceAssembleRecord extends DeviceAssembleRecord {
    @Id
    private String recordId;
    /**
     * 台账id
     */
    @NotNull
    private Long mwdId;
}
