package net.tartan.platform.integration.beans.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LaborHourVo {

    private Long id;

    /**
     * 工时考勤日期 （填报当天日期）
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date laborDate;

    /**
     * 星期几
     */
    private String week;

    /**
     * 所属项目编号
     */
    private Long projectId;
    private String projectName;
    private String projectNumber;

    /**
     * 时间区间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 根据时间区间计算出的工时
     * 午休为 12:00-13:00， 需要扣除休息时间
     */
    private BigDecimal laborHours;

    /**
     * 工作内容总结
     */
    private String workSummary;

    /**
     * 提交时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date submitTime;

    /**
     * 提交人姓名
     */
    private String submitUserName;

    /**
     * 提交人的memberId
     */
    private String submitMemberId;

    private String departmentName;

    /**
     * 最后编辑时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdateTime;

    /**
     * 最后编辑人姓名
     */
    private String lastUpdateBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 项目状态，0 -> 进行中， 1 -> 已结项
     */
    private Integer projectStatus;
}
