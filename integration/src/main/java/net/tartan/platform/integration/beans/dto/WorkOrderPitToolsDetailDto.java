package net.tartan.platform.integration.beans.dto;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.tartan.platform.common.enums.EnumRiskType;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 井下工具台账仪器详情 同 MWD台账仪器详情
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WorkOrderPitToolsDetailDto implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 详情id
     */
    @TableId(value = "detail_id", type = IdType.AUTO)
    private Long detailId;

    /**
     * 台账id
     */
    private Long pitToolsId;

    /**
     * 品名(主要用于没有序列号的工单，知道修的叫啥名)
     */
    private String invName;

    /**
     * 序列号，批次号
     */
    private String serialNumber;

    /**
     * 仪器类型
     */
    private Long deviceType;

    /**
     * 所有者
     */
    private String owner;

    /**
     * 现场联系人
     */
    private String contactUser;
    /**
     * 现场联系人的联系方式
     */
    private String contactNumber;

    /**
     * 入井时间
     */
    private BigDecimal inWellHour;

    /**
     * 循环时间
     */
    private BigDecimal circulateHrs;
    /**
     * 接收日期
     */
    private String receiveDate;

    /**
     * 入井趟次
     */
    private Integer run;

    /**
     * 最高温度
     */
    private BigDecimal maxBht;
    /**
     * 循环温度
     */
    private BigDecimal circulateBht;


    /**
     * 返回原因
     */
    private String returnReason;

    /**
     * 根本原因
     */
    private String rootReason;

    /**
     * 维修方案
     */
    private String repairAction;
    /**
     * 工单创建人
     */
    private Long createdBy;

    /**
     * 最后修改人
     */
    private Long lastModifiedBy;

    /**
     * 最后修改时间
     */
    private Date lastModifiedDate;
    /**
     * 备注
     */
    private String notes;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    // 以下是新增的仪器信息
//    /**
//     * 风险类型
//     */
//    @TableField(value = "risk_type", updateStrategy= FieldStrategy.IGNORED)
//    private EnumRiskType riskType;

    /**
     * 上次维修时间
     */
    private String lastRepairDate;

    //螺杆参数信息
    /**
     * 弯度:单弯 1 / 可调 0
     */
    private Integer curve;

    /**
     * 弯度
     */
    private BigDecimal angle;

    /**
     * 扣型
     */
    private String claspType;

    /**
     * 泥浆类型
     */
    private String mudType;

    /**
     * 最大外径
     */
    private BigDecimal odMax;

    /**
     * 是否带扶正器：带 1 / 不带 0
     */
    private Integer takeStb;

    /**
     * 扶正器尺寸
     */
    private BigDecimal stbSize;

    /**
     * 扶正器描述
     */
    private String stbDescribe;

    /**
     * 耐温
     */
    private BigDecimal endureTemperature;

    /**
     * 压差
     */
    private BigDecimal pressureSub;

    /**
     * 扭矩
     */
    private BigDecimal torque;
}
