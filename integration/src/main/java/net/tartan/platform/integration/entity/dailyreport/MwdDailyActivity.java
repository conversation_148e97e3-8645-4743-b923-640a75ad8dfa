package net.tartan.platform.integration.entity.dailyreport;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * mwd_daily_activity
 * <AUTHOR>
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MwdDailyActivity{
    private Long id;

    /**
     * nwd报告id
     */
    private Long dailyReportId;

    /**
     * 起始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 起始井深
     */
    private BigDecimal depthFrom;

    /**
     * 结束井深
     */
    private BigDecimal depthTo;

    /**
     * 工况
     */
    private String activity;

    /**
     * 模式
     */
    private String mode;

    /**
     * 立压
     */
    private String spp;

    /**
     * 钻压
     */
    private String drillPressure;

    /**
     * 转速
     */
    private String rpm;

    /**
     * 信号强度
     */
    private BigDecimal ss;

    /**
     * 扭矩
     */
    private String torque;

    /**
     * 排量
     */
    private BigDecimal fr;

    /**
     * TI/TA/BF/WF/SF/SD
     */
    private String info;

    /**
     * 描述
     */
    private String note;
}
