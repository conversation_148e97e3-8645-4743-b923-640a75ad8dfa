package net.tartan.platform.integration.beans.dto.process;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.tartan.platform.common.enums.EnumProjectType;
import net.tartan.platform.common.enums.EnumWorkOrderType;
import net.tartan.platform.integration.entity.process.ProcessFlowDetail;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 工艺流程卡
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-17
 */
@Data
public class ProcessFlowCardDto{

    private Long processFlowId;

    /**
     * 工艺编号
     */
    private String processFlowCode;

    /**
     * 项目类型
     */
    private EnumProjectType projectType;

    /**
     * 工单类型
     */
    private EnumWorkOrderType workOrderType;

    /**
     * 工艺名称
     */
    private String processFlowName;

    /**
     * 材质
     */
    private String materialQuality;

    /**
     * 炉号
     */
    private String furnaceNumber;

    /**
     * 计划结束日期
     */
    private String planEndDate;

    /**
     * 实际结束日期
     */
    private String actualEndDate;

    /**
     * 需求数量
     */
    private Integer quantity;

    /**
     * 是否完成 0：未完成 1：已完成
     */
    private Integer finished;
    /**
     * 负责人
     */
    private Long director;

    /**
     * 创建时间
     */
    private String createTime;

    private List<ProcessFlowDetailDto> flowDetailList;

}
