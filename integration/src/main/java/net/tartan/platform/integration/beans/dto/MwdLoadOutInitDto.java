package net.tartan.platform.integration.beans.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.tartan.platform.integration.beans.vo.MwdLoadOutDetailVo;

import java.util.Date;
import java.util.List;
@Data
@EqualsAndHashCode(callSuper = false)
public class MwdLoadOutInitDto {

    private Long loadOutId;

    private Long deviceId;

    private String inCheckMember;
    private String outCheckMember;

    private String dateOut;
    private String dateIn;

    private String location;
    private String wellNumber;


    private List<MwdLoadOutDetailVo> loadOutDetailList;

    /**
     * 创建时间
     */
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date updateTime;
}
