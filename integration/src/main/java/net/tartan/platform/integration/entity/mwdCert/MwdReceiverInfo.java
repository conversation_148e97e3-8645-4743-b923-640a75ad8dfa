package net.tartan.platform.integration.entity.mwdCert;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.constant.PfConstant;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 接收器出厂合格证
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = PfConstant.MWD_CERT_REPORT)
public class MwdReceiverInfo extends MwdCertBaseInfo{

    /**
     * 出厂序列状态
     * 生产序列显示为Pd,风险序列显示为Ri，试验序列显示为Te
     */
    private String riskType = "";

    /**
     * 外观检查
     */
    private String surfaceCheck = "合格";

    /**
     * 针孔测试
     */
    private String needleCheck = "合格";

    /**
     * Ringout测试
     */
    private String ringoutCheck = "合格";

    /**
     * 绝缘环检查
     */
    private String deadRingCheck = "合格";

    /**
     * 功能测试
     */
    private String functionCheck = "合格";

    /**
     * 检测结果
     */
    private String checkResult = "仪器按照达坦接收器检查测试规范进行出厂检测并通过。";

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
    }
}
