package net.tartan.platform.integration.beans.dto;

import lombok.Data;
import net.tartan.platform.common.enums.EnumRepairType;
import net.tartan.platform.common.enums.EnumRiskType;

import java.math.BigDecimal;

@Data
public class ComponentRepairRecordDto {

    /**
     * 存货编码
     */
    private String invName;

    /**
     * 序列号，erp的批次号
     */
    private String serialNumber;
    /**
     * 服役固件版本号
     */
    private String serviceVersionNumber;
    /**
     * 升级后固件版本号
     */
    private String updatedVersionNumber;
    /**
     * 服役风险类型
     */
    private EnumRiskType serviceRiskType;
    /**
     * 更新的风险类型
     */
    private EnumRiskType updatedRiskType;

//    /**
//     * 维修类型
//     */
//    private EnumRepairType repairType;
//
//    /**
//     * 维修方案
//     */
//    private String repairAction;
//
//    /**
//     * 区分是否失效，0 -> 未失效，1 -> 失效
//     */
//    private Integer isBroke;
//
//    /**
//     * 失效原因
//     */
//    private String failureReason;

    /**
     * 备注
     */
    private String notes;

    /**
     * 最高温度
     */
    private BigDecimal maxBht;

    /**
     * 修正最高温度
     */
    private BigDecimal reviseMaxBht;

    /**
     * 总入井时间
     */
    private BigDecimal totalInWellHrs;

    /**
     * 修正使用时长
     */
    private BigDecimal reviseTotalHours;


}
