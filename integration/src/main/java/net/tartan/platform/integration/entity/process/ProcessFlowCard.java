package net.tartan.platform.integration.entity.process;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;
import net.tartan.platform.common.enums.EnumProjectType;
import net.tartan.platform.common.enums.EnumWorkOrderType;

/**
 * <p>
 * 工艺流程卡
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ProcessFlowCard implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "process_flow_id", type = IdType.AUTO)
    private Long processFlowId;

    /**
     * 工艺编号
     */
    private String processFlowCode;

    /**
     * 项目类型
     */
    private EnumProjectType projectType;

    /**
     * 工单类型
     */
    private EnumWorkOrderType workOrderType;

//    /**
//     * 零件号
//     */
//    private String partNumber;

    /**
     * 工艺名称
     */
    private String processFlowName;

    /**
     * 材质
     */
    private String materialQuality;

    /**
     * 炉号
     */
    private String furnaceNumber;

    /**
     * 计划结束日期
     */
    private String planEndDate;

    /**
     * 实际结束日期
     */
    private String actualEndDate;

    /**
     * 需求数量
     */
    private Integer quantity;

    /**
     * 是否完成 0：未完成 1：已完成
     */
    private Integer finished;
    /**
     * 负责人
     */
    private Long director;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


}
