package net.tartan.platform.integration.controller;

import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.common.enums.EnumEmailBusinessType;
import net.tartan.platform.integration.entity.EmailTemplate;
import net.tartan.platform.integration.service.EmailTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * email前端控制器
 * </p>
 */
@RestController
@RequestMapping("/email")
public class EmailTemplateController {

    @Autowired
    private EmailTemplateService emailTemplateService;

    @ResponseBody
    @RequestMapping("getByType")
    public CommonResult delete(@RequestParam("type") EnumEmailBusinessType businessType,
                               @RequestParam(value = "id", required = false, defaultValue = "0") Long id) {

        return CommonResult.success(emailTemplateService.getEmailTemplateByType(businessType, id));
    }
    @PostMapping("send")
    public CommonResult sendEmail(@RequestBody @Valid EmailTemplate emailTemplate) {

        emailTemplateService.sendEmailByTemplate(emailTemplate);
        return CommonResult.success();
    }

}
