package net.tartan.platform.integration.entity.dailyreport;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.constant.PfConstant;
import net.tartan.platform.integration.entity.dailyreport.RSS.RSSOos;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * mwd_daily_report
 *
 * <AUTHOR>
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = PfConstant.MWD_DAILY_REPORT)
public class MwdDailyReport implements Serializable {
    @Id
    @JsonIgnore
    private String reportId;

    @Transient
    private Long id;

    /**
     * 报告井id
     */
    @NotNull
    private Long jobId;
    private String jobNumber;
    private String jobType;

    /**
     * 工程师
     */
    private String operator;

    /**
     * 日期
     */
    @NotEmpty
    private String date;

    /**
     * 仪器箱
     */
    private String kit;

    /**
     * 开始日期
     */
    private String startDate;

    /**
     * 施工天数
     */
    private Integer constructionDays;

    /**
     * 监督人
     */
    private String superintendent;

    private String dd1;

    private String dd2;

    private String mwd1;

    private String mwd2;

    /**
     * 趟次
     */
    private Integer run;

    /**
     * 开始井深
     */
    private BigDecimal startDepth;

    /**
     * 结束井深
     */
    private BigDecimal endDepth;

    /**
     * 泥浆类型
     */
    private String mudType;

    /**
     * 泥浆密度
     */
    private BigDecimal mudDen;

    /**
     * 粘度
     */
    private BigDecimal viscosity;

    /**
     * 固相含量
     */
    private BigDecimal solidContent;

    /**
     * 含沙量
     */
    private BigDecimal sandContent;

    /**
     * 含油量
     */
    private String oilContent;

    /**
     * 酸碱度
     */
    private BigDecimal ph;

    /**
     * 塑性粘度
     */
    private BigDecimal pv;

    /**
     * 动切力
     */
    private BigDecimal vp;

    /**
     * 失水
     */
    private BigDecimal fl;

    /**
     * 温度
     */
    private BigDecimal temp;

    /**
     * 探管内角差
     */
    private BigDecimal imo;

    /**
     * 钻具角差
     */
    private BigDecimal dao;

    /**
     * 磁偏角
     */
    private BigDecimal magEc;

    /**
     * 伽马零长
     */
    private BigDecimal gammaDist;

    /**
     * 近钻伽马零长
     */
    private BigDecimal atBitDist;

    /**
     * 限流环
     */
    private BigDecimal orfice;

    /**
     * 蘑菇头
     */
    private BigDecimal poppit;

    /**
     * 脉宽
     */
    private BigDecimal plsw;

    /**
     * 磁场强度
     */
    private BigDecimal nmag;

    /**
     * 磁倾角
     */
    private BigDecimal ndip;

    /**
     * 24小时计划
     */
    private String plan;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    private List<MwdWorkSummary> workSummaryList;
    /**
     * OOS列表
     */
    private List<RSSOos> oosList;

    private List<MwdTool> toolList;

    private List<MwdDailyActivity> activityList;

    private static final long serialVersionUID = 1L;

}
