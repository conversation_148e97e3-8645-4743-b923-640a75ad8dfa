package net.tartan.platform.integration.beans.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 出入库流转记录
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LoadOutServiceVo {

    private String deviceTypeStr;

    private String serialNumber;

    private Long loadOutId;

    private Long deviceId;

    private Long status;

    private String inCheckMember;
    private String outCheckMember;

    private String dateOut;
    private String dateIn;

    private String location;
    private String wellNumber;

    private String jobNumber;

    /**
     * 创建时间
     */
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date updateTime;
}
