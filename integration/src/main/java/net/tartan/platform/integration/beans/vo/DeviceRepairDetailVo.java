package net.tartan.platform.integration.beans.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
public class DeviceRepairDetailVo {

    /**
     * 仪器分类
     */
    private Long deviceType;
    /**
     * 序列号
     */
    private String serialNumber;

    private String[] receiveDateRange;

    /**
     * 最高温度
     */
    private Double maxBht;

    /**
     * 总循环时间
     */
    private BigDecimal totalCirculateHrs;

    /**
     * 总入井时间
     */
    private BigDecimal totalInWellHrs;

    /**
     * 维修总次数
     */
    private Integer repairTotalCount;
}
