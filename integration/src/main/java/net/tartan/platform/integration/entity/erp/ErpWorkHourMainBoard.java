package net.tartan.platform.integration.entity.erp;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 工单信息主表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("WIP_TRANSFER_DOC")
public class ErpWorkHourMainBoard {

    /**
     * 主表外键
     * WIP_TRANSFER_DOC_ID
     */
    private String transferDocId;

    /**
     * 工单号
     */
    private String docNo;

    /**
     * 工单日期
     * DOC_DATE
     */
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date docDate;

}
