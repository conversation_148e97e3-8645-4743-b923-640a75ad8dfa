package net.tartan.platform.integration.beans.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.enums.EnumRiskType;

import java.math.BigDecimal;
import java.util.Date;

@Data
@NoArgsConstructor
public class ComponentServiceVo {

  private Long historyId;

  /**
   * 工单类型（mwd,井下工具）
   */
  private String orderType;
  /**
   * 台账id
   */
  private Long commonId;
  /**
   * 工单号 入场操作单号
   */
  private String mwdNumber;
  private String pitToolsNumber;

  /**
   * 工单号 出场操作单号
   */
  private String outMwdNumber;
  private String outPitToolsNumber;
//  /**
//   * 母件存货编码
//   */
//  private String parentInvCode;
  /**
   * 母件存货品名
   */
  private String parentInvName;

  /**
   * 母件序列号，erp的批次号
   */
  private String parentSerialNumber;
  /**
   * 品名
   */
  private String invName;
//  /**
//   * 品号
//   */
//  private String invCode;
  /**
   * 序列号，erp的批次号
   */
  private String serialNumber;
  /**
   * 服役开始日期
   */
  private String startDate;
  /**
   * 服役结束日期
   */
  private String endDate;
  /**
   * 结束服役单号
   */
  private Long endMwdId;
  /**
   * 备注
   */
  private String notes;

  /**
   * 创建时间
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date createTime;
  /**
   * 更新时间
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date updateTime;
  /**
   * 作业号
   */
  private String jobNumber;
  /**
   * 井号
   */
  private String wellNumber;
  /**
   * 循环时间
   */
  private BigDecimal circulateHrs;
  /**
   * 最高温度
   */
  private BigDecimal maxBht;
  /**
   * 入井时长
   */
  private BigDecimal inWellHour;
  /**
   * 入井趟次
   */
  private Integer run;

  private Long failureType;

  private String failureName;
  /**
   * 固件版本号
   */
  private String versionNumber;

  /**
   * 修正最高温度
   */
  private BigDecimal reviseMaxBht;

  /**
   * 总入井时间
   */
  private BigDecimal totalInWellHrs;

  /**
   * 修正使用时长
   */
  private BigDecimal reviseTotalHours;

  /**
   * 服役固件版本号
   */
  private String serviceVersionNumber;
  /**
   * 升级后固件版本号
   */
  private String updatedVersionNumber;

  /**
   * 服役风险类型
   */
  private EnumRiskType serviceRiskType;
  /**
   * 更新后的服役风险类型
   */
  private EnumRiskType updatedRiskType;
}
