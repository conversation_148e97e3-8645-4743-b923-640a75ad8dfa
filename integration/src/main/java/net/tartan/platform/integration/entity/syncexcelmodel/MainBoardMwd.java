package net.tartan.platform.integration.entity.syncexcelmodel;

import lombok.Data;

import java.util.Date;

/**
 * <p>
 * MWD台账仪器详情
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-09
 */
@Data
public class MainBoardMwd {
//    //顺序要与excel保持完全一致否则会出现错位
//    private String mwdId;//ok
//    private String mwdNumber;//ok
//    private String deviceTypeStr;//ok
//    private String serialNumber;//ok
//    private String kitNumber;//ok
//    private String finish;//ok
//    private String inSN;
//    private String outSN;
//    private String owner;//ok
//    private String jobNumber;//ok
//    private String wellNumber;//ok
//    private String contactUser;//ok
//    private String repairUser;//ok
//    private String receiveDate;//ok
//    private String startDate;//ok
//    private String estimatedEndDate;//ok
//    private String endDate;//ok
//    private String overdueReasons;//ok
//    private String approveUser;//ok
//    private String returnReason;//ok
//    private String findings;//ok
//    private String rootReason;//ok
//    private String repairAction;//ok
//    private String sixPFourS;//ok
//    private String fourPSixS;//ok
//    private String inWellHour;//ok
//    private String circulateHrs;//ok
//    private String maxBht;//ok
//    private String run;//ok
//    private String failureType;//ok
//    private String completedFr;
//    private String completedFrDate;
//    private String failureComponentType;//ok
//    private String failureReasonType;//ok
//    private String eaStatus;
//    private String responsAppraisal;
//    private String notes;//ok
//    private String laborHours;


    //螺杆拆卸
//    private String number;
//    private String date;
//    private String location;
//    private String hour;
//    private String chuandongzhou;//核心部件
//    private String upTcStaticCircle;
//    private String upTcDynamicCircle;
//    private String downTcStaticCircle;
//    private String downTcDynamicCircle;
//    private String chuandongzhouketi;//传动轴壳体  核心部件
//    private String waterCap;//水帽
//    private String cardanShaft;//万向轴总成
//    private String zhuanzi;//转子  核心部件
//    private String wanxiangzhouketi;//万向轴壳体 核心部件
//    private String dingzi;//定子  核心部件
//    private String fangdiaojietou;//防掉接头  核心部件
//    private String daiyongjietou;//代用接头  核心部件
//    private String wanketijiaodu;//弯壳体角度
//    private String xiangjiaoleixing;//橡胶类型
//    private String pinggujieguo;//评估结果

    //螺杆组装
    private String number;
    private String type;
    private String date;
    private String location;
    private String hour;
    private String chuandongzhou;//核心部件
    private String chuandongzhouketi;//核心部件
    private String fuzhengqi;//
    private String chuanzhoucheng;//
    private String waterCap;//水帽
    private String upTcStaticCircle;
    private String upTcDynamicCircle;
    private String downTcStaticCircle;
    private String downTcDynamicCircle;
    private String cardanShaft;//万向轴总成
    private String zhuanzi;//转子  核心部件
    private String wanxiangzhouketi;//万向轴壳体 核心部件
    private String dingzi;//定子  核心部件
    private String fangdiaojietou;//防掉接头  核心部件
    private String daiyongjietou;//代用接头  核心部件
    private String wanketijiaodu;//弯壳体角度
    private String xiangjiaoleixing;//橡胶类型
    private String yacha;//评估结果
    private String niuju;//评估结果
    private String beizhu;//评估结果
    private String pinggujieguo;//评估结果

}
