package net.tartan.platform.integration.entity.dailyreport;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * mwd_cover_personnel
 * <AUTHOR>
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MwdCoverPersonnel{
    private Long id;

    /**
     * cover id
     */
    private Long coverReportId;

    /**
     * 人员类型
     */
    private String type;

    /**
     * 人员姓名
     */
    private String name;

}
