package net.tartan.platform.integration.entity.dailyreport;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * tools_daily_bha
 * <AUTHOR>
public class ToolsDailyBha implements Serializable {
    private Long id;

    /**
     * 工具日报id
     */
    private Long toolsReportId;

    /**
     * 钻具名称
     */
    private String name;

    /**
     * 序列号
     */
    private String serialNum;

    /**
     * 外径
     */
    private BigDecimal outerDiameter;

    /**
     * 长度
     */
    private BigDecimal length;

    /**
     * 累计长度
     */
    private BigDecimal cumLength;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getToolsReportId() {
        return toolsReportId;
    }

    public void setToolsReportId(Long toolsReportId) {
        this.toolsReportId = toolsReportId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSerialNum() {
        return serialNum;
    }

    public void setSerialNum(String serialNum) {
        this.serialNum = serialNum;
    }

    public BigDecimal getOuterDiameter() {
        return outerDiameter;
    }

    public void setOuterDiameter(BigDecimal outerDiameter) {
        this.outerDiameter = outerDiameter;
    }

    public BigDecimal getLength() {
        return length;
    }

    public void setLength(BigDecimal length) {
        this.length = length;
    }

    public BigDecimal getCumLength() {
        return cumLength;
    }

    public void setCumLength(BigDecimal cumLength) {
        this.cumLength = cumLength;
    }
}
