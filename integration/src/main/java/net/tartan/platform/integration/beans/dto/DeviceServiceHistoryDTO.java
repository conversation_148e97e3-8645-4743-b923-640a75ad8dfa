package net.tartan.platform.integration.beans.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.enums.EnumCirculateType;

import java.math.BigDecimal;
import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeviceServiceHistoryDTO {

    private Long deviceId;

    /**
     * 仪器流转时的状态
     * data_dictionary 表中 business_type 为 20 的所有均为仪器状态
     */
    private Long status;

    /**
     * 地点
     */
    private String location;

    /**
     * 作业号
     */
    private Long jobId;

    /**
     * 井号
     */
    private String wellNumber;

    /**
     * 数据插入
     * 需要手填
     */
    private String date;
    /**
     * 流转类型
     */
    private EnumCirculateType circulateType;
    /**
     * 业务号
     * 根据流转类型 可以表示不同业务的编号
     */
    private String businessNumber;
    /**
     * 业务id
     * 根据流转类型 可以表示不同业务的id
     */
    private Long businessId;

    // 由工单填入
    /**
     * 循环时间
     */
    private BigDecimal circulateHrs;
    /**
     * 最高温度
     */
    private BigDecimal maxBht;
    /**
     * 入井时长
     */
    private BigDecimal inWellHour;

    private List<DeviceServiceHistoryDTO> deviceHistory;

}
