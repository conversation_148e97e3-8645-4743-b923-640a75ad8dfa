package net.tartan.platform.integration.beans.vo.loadOutDevice;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 定向接头 库房视图
 * 16014
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UbhoVo {


    private Long deviceId;
    private Long deviceType;
    private Long deviceTypeStr;
    private Long stockType;
    private String stockTypeStr;
    private String invName;
    private String serialNumber;
    private Long status;
    private String statusStr;

    //received/send date 接收/发出日期
    private String receivedSendDate;

    private String location;

    //owner所属
    private String owner;

    //clientName客户名（如果所属是client则需要填具体的客户名）
    private String clientName;

    //note
    private String note;

    //规格
    private String specification;
    // 是否有sn号 0表示无 1表示有
    // 如果是无号 则代表数据是device_no_sn表中的
    // 如果是有号 则代表数据是device表中的
    private Integer hasSn;

    /**
     * 仪器创建人
     */
    private Long createdBy;
    private String createdByStr;

    /**
     * 仪器最后修改人
     */
    private Long lastModifiedBy;
    private String lastModifiedByStr;

    /**
     * 公母扣型
     */
    private String buckleType;

    /**
     * 材料号
     */
    private String materialNumber;
}
