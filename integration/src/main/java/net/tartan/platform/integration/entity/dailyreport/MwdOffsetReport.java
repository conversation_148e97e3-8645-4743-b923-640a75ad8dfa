package net.tartan.platform.integration.entity.dailyreport;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.constant.PfConstant;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * mwd_offset_report
 *
 * <AUTHOR>
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@Document(collection = PfConstant.MWD_OFFSET_REPORT)
public class MwdOffsetReport{
    @Transient
    private Long id;

    @Id
    @JsonIgnore
    private String reportId;

    /**
     * 作业id
     */
    @NotNull
    private Long jobId;

    /**
     * 临界长度
     */
    private BigDecimal criticalLen;

    private BigDecimal lenToUb;

    private BigDecimal lenToTom;

    private BigDecimal mwdLen;

    private BigDecimal mwdDepth;

    private BigDecimal lenToDir;

    private BigDecimal lenToGam;

    private BigDecimal lenToGap;

    private BigDecimal lenToLog2;

    private BigDecimal lenToLog3;

    private BigDecimal lenToLog4;

    private BigDecimal monel;

    private BigDecimal ponyOrOther1;

    private BigDecimal ponyOrOther2;

    private BigDecimal ubho;

    private BigDecimal floatOrOther;

    private BigDecimal motor;

    private BigDecimal bit;

    private List<MwdOffsetInfo> infoList;

    private List<MwdOffsetLengths> lengthsList;

    private static final long serialVersionUID = 1L;
}
