package net.tartan.platform.integration.mongo;

import net.tartan.platform.integration.entity.DrillingToolNotification;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 钻具通知单MongoDB Repository
 * 
 * <AUTHOR>
 * @since 2025-07-15
 */
@Repository
public interface DrillingToolNotificationRepository extends MongoRepository<DrillingToolNotification, String> {

    /**
     * 根据单据编号查询
     */
    DrillingToolNotification findByNo(String no);

    /**
     * 根据工单号查询
     */
//    List<DrillingToolNotification> findByWorkOrderNo(String workOrderNo);

    /**
     * 根据客户名称查询
     */
    List<DrillingToolNotification> findByCustomerName(String customerName);

    /**
     * 根据井名查询
     */
    List<DrillingToolNotification> findByWellName(String wellName);

    /**
     * 根据Kit号查询
     */
    List<DrillingToolNotification> findByKitNo(String kitNo);

    /**
     * 根据发起人编码查询
     */
    List<DrillingToolNotification> findByInitiatorCode(String initiatorCode);

    /**
     * 根据OA同步状态查询
     */
    List<DrillingToolNotification> findByOaSyncStatus(String oaSyncStatus);

    /**
     * 查询待同步的记录
     */
    @Query("{'oaSyncStatus': {$in: ['pending', 'failed']}, 'status': 'active'}")
    List<DrillingToolNotification> findPendingSyncRecords();

    /**
     * 根据日期范围查询
     */
    @Query("{'notificationDate': {$gte: ?0, $lt: ?1}, 'status': 'active'}")
    List<DrillingToolNotification> findByDateRange(Date startDate, Date endDate);

    /**
     * 根据钻具名称查询
     */
    @Query("{'drillingTools.name': ?0, 'status': 'active'}")
    List<DrillingToolNotification> findByToolName(String toolName);

    /**
     * 根据钻具序列号查询
     */
    @Query("{'drillingTools.serialNumber': ?0, 'status': 'active'}")
    List<DrillingToolNotification> findByToolSerialNumber(String serialNumber);

    /**
     * 查询指定时间之后创建的记录
     */
    @Query("{'createdTime': {$gte: ?0}, 'status': 'active'}")
    List<DrillingToolNotification> findByCreatedTimeAfter(Date createdTime);

    /**
     * 统计各状态的记录数量
     */
    @Query(value = "{}", count = true)
    long countByStatus(String status);

    /**
     * 统计OA同步状态的记录数量
     */
    @Query(value = "{'oaSyncStatus': ?0}", count = true)
    long countByOaSyncStatus(String oaSyncStatus);

    /**
     * 查询最近的记录
     */
    @Query(value = "{'status': 'active'}", sort = "{'createdTime': -1}")
    List<DrillingToolNotification> findRecentRecords();

    /**
     * 根据多个条件查询
     */
    @Query("{'customerName': {$regex: ?0, $options: 'i'}, 'wellName': {$regex: ?1, $options: 'i'}, 'status': 'active'}")
    List<DrillingToolNotification> findByCustomerAndWell(String customerName, String wellName);

    /**
     * 删除指定时间之前的记录（软删除）
     */
    @Query("{'createdTime': {$lt: ?0}}")
    List<DrillingToolNotification> findOldRecords(Date beforeDate);

    List<DrillingToolNotification> findByJobNumber(String jobNumber);
}
