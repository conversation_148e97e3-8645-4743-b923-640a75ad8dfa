package net.tartan.platform.integration.controller;

import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.integration.beans.request.AddResourceParams;
import net.tartan.platform.integration.beans.request.UpdateResourceParams;
import net.tartan.platform.integration.beans.vo.ResourceTree;
import net.tartan.platform.integration.beans.vo.ResourceVo;
import net.tartan.platform.integration.entity.Resource;
import net.tartan.platform.integration.service.ResourceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("resource")
public class ResourceController {

    @Autowired
    private ResourceService resourceService;

    /**
     * 添加资源
     * @param params
     * @return
     */
    @PostMapping("add")
    public CommonResult addResource(@RequestBody @Validated AddResourceParams params) {
        resourceService.add(params);
        return CommonResult.success(null);
    }

    /**
     * 查询资源列表
     * @return
     */
    @GetMapping("list")
    public CommonResult list() {
        List<ResourceVo> list = resourceService.listAll();
        return CommonResult.success(list);
    }

    /**
     * 查询资源列表（进行分类）
     * @return
     */
    @GetMapping("listWithCategory")
    public CommonResult listWithCategory() {
        List<ResourceVo> list = resourceService.listWithCategory();
        return CommonResult.success(list);
    }

    /**
     * 查询资源列表（进行分类）
     * @return
     */
    @GetMapping("tree")
    public CommonResult getResourceTree() {
        List<ResourceTree> list = resourceService.getResourceTree();
        return CommonResult.success(list);
    }

    /**
     * 更新资源
     * @param updateResourceParams
     * @return
     */
    @PostMapping("update")
    public CommonResult update(@RequestBody @Validated UpdateResourceParams updateResourceParams) {
        resourceService.update(updateResourceParams);
        return CommonResult.success();
    }

    /**
     * 删除资源
     * @param id
     * @return
     */
    @RequestMapping("delete")
    public CommonResult delete(@RequestParam("id") long id) {
        resourceService.delete(id);
        return CommonResult.success();
    }

    /**
     * 查询用户下所有资源
     * @param userId 用户id
     * @return
     */
    @GetMapping("listByUser")
    public CommonResult listByUser(@RequestParam("userId") long userId) {
        List<Resource> resourceList = resourceService.listByUser(userId);
        return CommonResult.success(resourceList);
    }

    /**
     * 查询角色下所有资源
     * @param roleId 角色id
     * @return
     */
    @GetMapping("listByRole")
    public CommonResult listByRole(@RequestParam("roleId") long roleId) {
        List<Resource> resourceList = resourceService.listByRole(roleId);
        return CommonResult.success(resourceList);
    }

    /**
     * 查询菜单下所有资源
     * @param menuId 菜单id
     * @return
     */
    @GetMapping("listByMenu")
    public CommonResult listByMenu(@RequestParam("menuId") long menuId) {
        List<Resource> resourceList = resourceService.listByMenu(menuId);
        return CommonResult.success(resourceList);
    }
}
