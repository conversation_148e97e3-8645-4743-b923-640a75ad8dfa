package net.tartan.platform.integration.beans.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.enums.EnumRiskType;
import net.tartan.platform.common.enums.EnumRubberType;

import java.math.BigDecimal;
import java.util.Date;

@Data
@NoArgsConstructor
public class ComponentInfoDetailVo {


    private Long historyId;

    /**
     * 工单类型（mwd,井下工具）
     */
    private String orderType;

    private Long componentId;
    /**
     * 台账id
     */
    private Long commonId;
    /**
     * 工单号
     */
    private String mwdNumber;
    private String pitToolsNumber;

    /**
     * 仪器类型
     */
    private Long deviceType;

    /**
     * 仪器风险类型
     */
    private EnumRiskType riskType;

    /**
     * 母件存货品名
     */
    private String parentInvName;

    /**
     * 母件序列号，erp的批次号
     */
    private String parentSerialNumber;
    /**
     * 品名
     */
    private String invName;

    /**
     * 序列号，erp的批次号
     */
    private String serialNumber;
    /**
     * 服役开始日期
     */
    private String startDate;
    /**
     * 服役结束日期
     */
    private String endDate;
    /**
     * 结束服役单号
     */
    private Long endMwdId;
//    /**
//     * 备注
//     */
//    private String notes;

    /**
     * 入库时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date stockInDate;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    /**
     * 作业号
     */
    private String jobNumber;
    /**
     * 井号
     */
    private String wellNumber;
    /**
     * 循环时间
     */
    private BigDecimal circulateHrs;

    /**
     * 入井时长
     */
    private BigDecimal inWellHour;
    /**
     * 入井趟次
     */
    private Integer run;

    private Long failureType;

    private String failureName;
    /**
     * 固件版本号
     */
    private String versionNumber;


    // 统计数据
    /**
     * 最高温度
     */
    private BigDecimal maxBht;
    /**
     * 修正最高温度
     */
    private BigDecimal reviseMaxBht;

    /**
     * 总循环时间
     */
    private BigDecimal totalCirculateHrs;

    /**
     * 总入井时间
     */
    private BigDecimal totalInWellHrs;

    /**
     * 总服役次数
     */
    private Integer serveTotalCount;

//    /**
//     * 使用时长
//     */
//    private BigDecimal totalHours;

    /**
     * 修正使用时长
     */
    private BigDecimal reviseTotalHours;

    //定子
    /**
     * 橡胶类型
     */
    private EnumRubberType rubberType;

    //螺杆部件额外信息
    //万向轴壳体
    /**
     * 弯度类型:单弯 1 / 可调 0
     */
    private Integer curve;
    /**
     * 弯度
     */
    private BigDecimal angle;

    // 新增的一些部件参数

    /**
     * 制造商
     */
    private String manufacturer;

    /**
     * 温度类型
     */
    private String tempType;

    /**
     * 备注
     */
    private String note;

    /**
     * 风险值
     */
    private BigDecimal riskValue;

    // 部件的最后更新人
    private String lastUpdateUser;

    // 部件的创建人
    private String createdByUser;

    // 旋导部件参数
    // 品号
    private String invCode;
    // 最后修改时间
    private String lastAssemblyDate;
    private Long parentComponentId;
    // 部件可用层级
    private Integer level;
    private Boolean isLeaf;
    private Integer sort;
}
