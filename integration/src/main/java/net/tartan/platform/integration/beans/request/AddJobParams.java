package net.tartan.platform.integration.beans.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AddJobParams {

    @NotEmpty
    private String jobNumber;

    @NotNull
    private String wellId;

    @NotNull
    private Long jobType;

    @NotNull
    private int jobId;

    @NotNull
    private int jobStatus;

//    private String checkKit="true";
}
