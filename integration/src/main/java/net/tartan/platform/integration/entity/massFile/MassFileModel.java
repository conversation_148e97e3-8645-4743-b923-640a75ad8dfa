package net.tartan.platform.integration.entity.massFile;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import net.tartan.platform.common.enums.EnumMassFileType;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = false)
public class MassFileModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "mass_id", type = IdType.AUTO)
    private Long massId;

    private Long id; //统用的id 用来统一下面的四个id targetId

    private Long jobId;
    private Long mwdId;
    private Long pitToolsId;
    private Long projectId;
    private Long rmaId;
    private Long rmaRepairId;

    //当属于车间检查的上传文件时 和jobId一起使用
    private String date;

    // Y 是唯一文件  N 不是唯一文件
    private String isUnique;

    private EnumMassFileType massFileType;

    private String massFileId;

    private String massFileName;

    private String massFilePath;

    private String previewPath;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
