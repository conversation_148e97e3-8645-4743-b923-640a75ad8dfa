package net.tartan.platform.integration.entity.repair;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.tartan.platform.common.enums.EnumRepairReceiveStatus;
import net.tartan.platform.common.enums.EnumToolType;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class Repair implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "repair_id", type = IdType.AUTO)
    private Long repairId;

    /**
     * 返修单号
     */
    private String repairCode;
    /**
     * 工具类型
     */
    @NotNull
    private EnumToolType toolType;

    /**
     * 工单号
     */
    private String jobNumber;

    /**
     * 井号
     */
    private String wellNumber;

    /**
     * kit箱
     */
    private String kitNumber;

    /**
     * 返回日期
     */
    private String returnDate;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 现场联系人
     */
    private String contactUser;

    /**
     * 现场联系人的联系方式
     */
    private String contactNumber;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


    /**
     * 返修单接收状态
     * SUBMITTED - 已提交（未接收）
     * RECEIVED - 已接收
     */
//    private EnumRepairReceiveStatus receiveStatus;

    /**
     * 返修的的接收人姓名
     */
    private String receivedBy;

    /**
     * 返修的的创建人姓名
     */
    private String createdBy;

    /**
     * 接收时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date receiveDate;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Boolean isFromOa;
}
