package net.tartan.platform.integration.beans.vo.dailyreport;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.enums.EnumReportType;
import net.tartan.platform.integration.entity.dailyreport.MwdOffsetInfo;
import net.tartan.platform.integration.entity.dailyreport.MwdOffsetLengths;

import java.math.BigDecimal;
import java.util.List;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MwdOffsetReportVo {

    private EnumReportType reportType;

    private long lastModified;

    /**
     * 临界长度
     */
    private BigDecimal criticalLen;

    private BigDecimal lenToUb;

    private BigDecimal lenToTom;

    private BigDecimal mwdLen;

    private BigDecimal mwdDepth;

    private BigDecimal lenToDir;

    private BigDecimal lenToGam;

    private BigDecimal lenToGap;

    private BigDecimal lenToLog2;

    private BigDecimal lenToLog3;

    private BigDecimal lenToLog4;

    private BigDecimal monel;

    private BigDecimal ponyOrOther1;

    private BigDecimal ponyOrOther2;

    private BigDecimal ubho;

    private BigDecimal floatOrOther;

    private BigDecimal motor;

    private BigDecimal bit;

    private List<MwdOffsetInfo> infoList;

    private List<MwdOffsetLengths> lengthsList;
}
