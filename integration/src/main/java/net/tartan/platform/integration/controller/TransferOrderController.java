package net.tartan.platform.integration.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.integration.beans.dto.TransferOrderDto;
import net.tartan.platform.integration.beans.query.TransferOrderQuery;
import net.tartan.platform.integration.beans.vo.DeviceTransferInfoVo;
import net.tartan.platform.integration.beans.vo.TransferOrderInfoVo;
import net.tartan.platform.integration.service.ITransferOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 调拨单控制层
 * </p>
 */
@RestController
@RequestMapping("/transferOrder")
public class TransferOrderController {

    @Autowired
    private ITransferOrderService transferOrderService;

    @PostMapping("info")
    public CommonResult info(@RequestBody TransferOrderQuery query) {
        TransferOrderInfoVo vo = transferOrderService.info(query);
        return CommonResult.success(vo);
    }

    @PostMapping("insert")
    public CommonResult info(@RequestBody TransferOrderDto dto) {
        return CommonResult.success(transferOrderService.insert(dto));
    }

    @PostMapping("page/{current}/{size}")
    public CommonResult selectPage(@PathVariable long current,
                                      @PathVariable long size,
                                      @RequestBody(required = false) TransferOrderQuery query) {
        IPage<TransferOrderInfoVo> page = new Page<>(current, size);
        if (query == null) {
            query = new TransferOrderQuery();
        }
        return CommonResult.success(transferOrderService.transferOrderPage(page, query));
    }

    @PostMapping("update/receiveStatus")
    public CommonResult updateReceiveStatus(@RequestBody TransferOrderQuery query) {

        transferOrderService.updateReceiveStatus(query);
        return CommonResult.success();
    }
    @PostMapping("update/fieldReceiveStatus")
    public CommonResult updateReceiveStatus_field(@RequestBody TransferOrderQuery query) {

        transferOrderService.updateReceiveStatus_field(query);
        return CommonResult.success();
    }

    @PostMapping("updateInfo")
    public CommonResult updateTransferOrderInfo(@RequestBody TransferOrderDto dto) {

        transferOrderService.updateTransferOrderInfo(dto);
        return CommonResult.success();
    }
    @PostMapping("delete")
    public CommonResult deleteTransferOrderInfo(@RequestBody TransferOrderQuery query) {

        transferOrderService.deleteTransferOrderInfo(query);
        return CommonResult.success();
    }
    @PostMapping("export")
    public CommonResult exportTransferOrder(@RequestBody TransferOrderQuery query) {

        transferOrderService.exportTransferOrder(query);
        return CommonResult.success();
    }
    @PostMapping("statusList")
    public CommonResult queryDeviceTransferInfoList(@RequestBody TransferOrderQuery query) {

        List<DeviceTransferInfoVo> transferInfoVos = transferOrderService.queryDeviceTransferInfo(query);
        return CommonResult.success(transferInfoVos);
    }
}
