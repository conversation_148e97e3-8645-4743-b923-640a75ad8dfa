package net.tartan.platform.integration.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.integration.beans.query.MemberQuery;
import net.tartan.platform.integration.beans.vo.MemberInfoVo;
import net.tartan.platform.integration.beans.vo.MemberTree;
import net.tartan.platform.integration.entity.MemberInfo;
import net.tartan.platform.integration.service.IMemberInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

@RestController
@RequestMapping("member")
public class MemberController {

    @Autowired
    private IMemberInfoService memberService;

    @PostMapping("list")
    public CommonResult list(@RequestBody MemberQuery request) {
        LambdaQueryWrapper<MemberInfo> memberLambdaQueryWrapper = new LambdaQueryWrapper<>();
        memberLambdaQueryWrapper.eq(MemberInfo::getIsEnable, 1);
        memberLambdaQueryWrapper.eq(MemberInfo::getIsDelete, 0);
        if (Objects.nonNull(request.getDepartmentId())) {
            memberLambdaQueryWrapper.eq(MemberInfo::getOrgDepartmentId, request.getDepartmentId());
        }
        if (Objects.nonNull(request.getName())) {
            memberLambdaQueryWrapper.likeRight(MemberInfo::getName, request.getName());
        }
        if (Objects.nonNull(request.getPhoneNumber())) {
            memberLambdaQueryWrapper.likeRight(MemberInfo::getPhoneNumber, request.getPhoneNumber());
        }
        if (Objects.nonNull(request.getEmail())) {
            memberLambdaQueryWrapper.likeRight(MemberInfo::getEmail, request.getEmail());
        }
        IPage<MemberInfo> page = new Page<>(request.getCurrent(), request.getSize());
        memberService.page(page, memberLambdaQueryWrapper);
        final MemberInfoVo result = MemberInfoVo.builder()
                .current(page.getCurrent())
                .pages(page.getPages())
                .memberInfoList(page.getRecords())
                .size(page.getSize())
                .total(page.getTotal())
                .build();
        return CommonResult.success(result);
    }

    @RequestMapping("tree")
    public CommonResult tree(@RequestBody MemberQuery request) {
        MemberTree memberTree = memberService.tree(request);
        return CommonResult.success(memberTree);
    }
}
