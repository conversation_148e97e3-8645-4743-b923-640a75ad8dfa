package net.tartan.platform.integration.beans.query;

import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.enums.EnumToolType;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@NoArgsConstructor
public class RepairQuery {
    /**
     * 工具类型
     */
    @NotNull
    private EnumToolType toolType;

    private String repairCode;

    private String wellNumber;

    private String orderBy;// createTime returnDate
    private String orderType;// desc asc
    private List<Long> detailIdList;
}
