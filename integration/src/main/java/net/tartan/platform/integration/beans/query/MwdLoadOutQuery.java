package net.tartan.platform.integration.beans.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MwdLoadOutQuery {
    private Long loadOutId;
    private String serialNumber;
    private Long deviceType;
    private String wellNumber;
    private String location;
    private String dateIn;
    private String dateOut;
    private String jobNumber;

    private List<String> serialNumberList;
    private List<Long> deviceTypeList;

    // 出库时间的时间区间
    private String dateOutStart;
    private String dateOutEnd;
    // 入库时间的时间区间
    private String dateInStart;
    private String dateInEnd;
}
