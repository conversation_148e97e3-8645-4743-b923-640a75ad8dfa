package net.tartan.platform.integration.mapper.erp;

import net.tartan.platform.integration.entity.erp.Mo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 工单/CHT/工單/ENU/MO Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-09
 */
public interface ErpMoMapper extends BaseMapper<Mo> {

    List<Mo> listByDate(@Param("startDate") String startDate, @Param("endDate") String endDate);

    Mo getDocInfo(@Param("docNo") String docNo);
}
