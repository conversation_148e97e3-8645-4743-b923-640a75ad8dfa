package net.tartan.platform.integration.beans.vo.excel;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import net.tartan.platform.integration.beans.vo.WorkOrderPitToolsVo;

import java.math.BigDecimal;
import java.util.Date;

/**
 * excel导出使用的数据
 */
@Data
public class PitToolsExcelExport {

    /**
     * 工单单号
     */
    private String pitToolsNumber;

//    /**
//     * erp的维修单号
//     */
//    private String moDocNo;

    /**
     * 作业编号
     */
    private String jobNumber;

    /**
     * 井号
     */
    private String wellNumber;
    /**
     * 详情id
     */
    private Long detailId;

    /**
     * 台账id
     */
    private Long pitToolsId;

    /**
     * 品名
     */
    private String invName;


    /**
     * 序列号
     */
    private String serialNumber;

    /**
     * 仪器类型
     */
    private Long deviceType;
    private String deviceTypeStr;
    /**
     * 接收日期
     */
    private String receiveDate;
    /**
     * 1：已完成，0：未完成
     */
    private Integer finish;
//    /**
//     * 维修状态1：已完成，0：未完成，-1：滞留
//     */
//    private Integer status;

    /**
     * 维修人员
     */
    private String repairUser;
    /**
     * 维修耗时
     */
    private BigDecimal laborHours;
    /**
     * 工单创建人
     */
    private Long createdBy;
    private String createdByStr;

    /**
     * 最后修改人
     */
    private Long lastModifiedBy;
    private String lastModifiedByStr;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 入井时间
     */
    private BigDecimal inWellHour;

    /**
     * 循环时间
     */
    private BigDecimal circulateHrs;
    /**
     * 入井趟次
     */
    private Integer run;

    /**
     * 最高温度
     */
    private BigDecimal maxBht;

    /**
     * 工单类型： 组装 ASSEMBLE, 拆卸 DISASSEMBLE
     */
    private String workOrderType;

    /**
     * 完成日期
     */
    private String endDate;

    /**
     * 所有者
     */
    private String owner;

    /**
     * 现场联系人
     */
    private String contactUser;
    /**
     * 现场联系人的联系方式
     */
    private String contactNumber;

    /**
     * 返回原因
     */
    private String returnReason;

    /**
     * 详细信息
     */
    private WorkOrderPitToolsVo workOrderPitToolsVo;

    private String location;

}
