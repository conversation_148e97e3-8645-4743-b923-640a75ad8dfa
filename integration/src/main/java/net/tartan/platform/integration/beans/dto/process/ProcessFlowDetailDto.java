package net.tartan.platform.integration.beans.dto.process;

import lombok.Data;
import net.tartan.platform.integration.entity.process.ProcessFlowOperate;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 工艺流程详情
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-17
 */
@Data
public class ProcessFlowDetailDto implements Serializable {

    private static final long serialVersionUID = 1L;
    private Long flowId;

    /**
     * 工艺流程卡id
     */
    private Long processFlowId;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 工序名称
     */
    private String flowName;

    /**
     * 工序描述
     */
    private String flowDesc;
    /**
     * 工序要求数量
     */
    private Integer processRequiredQuantity;

    /**
     * 合格数量
     */
    private Integer qualifiedQuantity;
    /**
     * 返修数量
     */
    private Integer repairQuantity;
    /**
     * 废弃数量
     */
    private Integer abandonQuantity;

    /**
     * 结束日期
     */
    private String endDate;

    /**
     * 标准工时
     */
    private Double standardHours;

    /**
     * 准备工时
     */
    private Double prepareHours;

    /**
     * 是否外包0：不外包 1：外包
     */
    private Integer outsource;
    /**
     * 质检员
     */
    private Long checkUserId;


    private List<ProcessFlowOperate> operateList;

}
