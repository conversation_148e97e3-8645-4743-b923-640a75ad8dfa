package net.tartan.platform.integration.controller;

import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.common.beans.ResultCode;
import net.tartan.platform.integration.entity.erp.ErpBomInfo;
import net.tartan.platform.integration.exception.BusinessException;
import net.tartan.platform.integration.service.erp.IErpBomService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 更换清单中使用的DOM模板
 */
@RestController
@RequestMapping("/bom")
public class BOMController {


    @Autowired
    private IErpBomService erpDomService;

    // 模糊查询BOM列表
    @PostMapping("list")
    public CommonResult list(@RequestBody(required = false) ErpBomInfo query) {
        if (query == null) {
            query = new ErpBomInfo();
        }
        return CommonResult.success(erpDomService.getBomList(query));
    }

    // 根据BOM id 获取替换清单
    @PostMapping("item/list")
    public CommonResult getReplaceItemByBomId(@RequestBody ErpBomInfo query) {
        if (query == null || query.getBomId() == null || query.getBomId().equals("")) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        return CommonResult.success(erpDomService.getReplaceItemByBomId(query));
    }



}
