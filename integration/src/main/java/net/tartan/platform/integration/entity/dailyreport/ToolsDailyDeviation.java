package net.tartan.platform.integration.entity.dailyreport;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * tools_daily_deviation
 * <AUTHOR>
public class ToolsDailyDeviation implements Serializable {
    private Long id;

    /**
     * 工具日报id
     */
    private Long toolsReportId;

    /**
     * 测深m
     */
    private BigDecimal md;

    /**
     * 井斜角
     */
    private BigDecimal wellAngle;

    /**
     * 方位角
     */
    private BigDecimal azimuth;

    /**
     * 狗腿度°/30m
     */
    private BigDecimal doglegDegree;

    /**
     * 位移m
     */
    private BigDecimal movement;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getToolsReportId() {
        return toolsReportId;
    }

    public void setToolsReportId(Long toolsReportId) {
        this.toolsReportId = toolsReportId;
    }

    public BigDecimal getMd() {
        return md;
    }

    public void setMd(BigDecimal md) {
        this.md = md;
    }

    public BigDecimal getWellAngle() {
        return wellAngle;
    }

    public void setWellAngle(BigDecimal wellAngle) {
        this.wellAngle = wellAngle;
    }

    public BigDecimal getAzimuth() {
        return azimuth;
    }

    public void setAzimuth(BigDecimal azimuth) {
        this.azimuth = azimuth;
    }

    public BigDecimal getDoglegDegree() {
        return doglegDegree;
    }

    public void setDoglegDegree(BigDecimal doglegDegree) {
        this.doglegDegree = doglegDegree;
    }

    public BigDecimal getMovement() {
        return movement;
    }

    public void setMovement(BigDecimal movement) {
        this.movement = movement;
    }
}
