package net.tartan.platform.integration.entity.dailyreport.RSS;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.constant.PfConstant;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Document(collection = PfConstant.RSS_RUN_REPORT)
public class RSSRunReport implements Serializable {
    @Transient
    private Long id;

    /**
     * 报告类型
     */
    @Transient
    private String reportType;

    @Id
    @JsonIgnore
    private String reportId;

    /**
     * cover id
     */
    @NotNull
    private Long jobId;
    private String jobNumber;
    /**
     * 趟次
     */
    @NotNull
    private Integer run;

    /**
     * BPA使用时间
     */
    private BigDecimal bypassTimeHrs;

    /**
     * BPA使用时间
     */
    private BigDecimal bpaHrs;

    /**
     * BPA SN
     */
    private String bpaSn;

    /**
     * 入井时间
     */
    private String dateIn;

    /**
     * 出井时间
     */
    private String dateOut;

    /**
     * 入井深度
     */
    private BigDecimal depthIn;

    /**
     * 出井深度
     */
    private BigDecimal depthOut;

    /**
     * 最高温度
     */
    private BigDecimal maxBht;

    /**
     * 信号强度
     */
    private Double signalStrength;

    /**
     * 井眼尺寸mm
     */
    private BigDecimal holeSize;

    /**
     * 泥浆数据
     */
    private String mudData;

    /**
     * 泥浆类型
     */
    private String mudType;

    /**
     * 泥浆密度
     */
    private BigDecimal mudDen;

    /**
     * 排量m3/min
     */
    private BigDecimal flowRate;

    /**
     * 粘度
     */
    private BigDecimal viscosity;

    /**
     * 破乳电压
     */
    private BigDecimal emulsionbreakingVoltage;

    /**
     * HTHP
     */
    private BigDecimal hthp;

    /**
     * 泥饼
     */
    private BigDecimal filterCake;

    /**
     * 油水比
     */
    private String oilWaterRatio;

    /**
     * 固相含量
     */
    private BigDecimal solidContent;

    /**
     * 含沙量
     */
    private BigDecimal sandContent;

    /**
     * 含油量
     */
    private String oilContent;

    /**
     * 酸碱度
     */
    private BigDecimal ph;

    /**
     * 塑性粘度
     * plastic viscosity
     */
    private BigDecimal pv;

    /**
     * 动切力
     */
    private BigDecimal vp;

    /**
     * 初终切
     */
    private String cjp;

    /**
     * 六速
     */
    private String ls;

    /**
     * 钻头厂家
     */
    private String bitMfg;

    /**
     * 钻头类型
     */
    private String insertToothPdc;

    /**
     * 钻头型号
     */
    private String bitModel;

    /**
     * 螺杆厂家
     */
    private String motorMfg;

    /**
     * 螺杆尺寸
     */
    private String motorSize;

    /**
     * 定子/转子
     */
    private String mLobesStage;

    /**
     * 钻头
     */
    private String bitReason;

    /**
     * 螺杆
     */
    private String motorReason;

    /**
     * 工具
     */
    private String toolReason;

    /**
     * 其他
     */
    private String otherReason;

    /**
     * 井下脉冲高度PSI
     */
    private BigDecimal psi;

    /**
     * 井底DOWN HOLE TIME hrs
     */
    private BigDecimal dhtData;

    /**
     * 循环
     */
    private BigDecimal circulatingData;

    /**
     * 纯钻
     */
    private BigDecimal drillingData;

    /**
     * 工具列表
     */
    private List<RSSTool> toolList;

    /**
     * OOS列表
     */
    private List<RSSOos> oosList;


    private Double ss;
}
