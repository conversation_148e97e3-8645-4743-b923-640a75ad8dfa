package net.tartan.platform.integration.service;

import net.tartan.platform.integration.entity.DrillingToolNotification;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 钻具通知单服务接口
 * 
 * <AUTHOR>
 * @since 2025-07-15
 */
public interface DrillingToolNotificationService {

    /**
     * 创建钻具通知单
     * 
     * @param notification 钻具通知单实体
     * @return 创建的钻具通知单
     */
    DrillingToolNotification createNotification(DrillingToolNotification notification);

    /**
     *
     * @return 初始化钻具通知单信息（不保存 只创建 将NO自动生成）
     */
    DrillingToolNotification initNotification();

    /**
     * 发送钻具通知单
     * @param notification
     */
    boolean sentNotification(DrillingToolNotification notification);

    /**
     * 根据ID查询钻具通知单
     * 
     * @param id 钻具通知单ID
     * @return 钻具通知单
     */
    DrillingToolNotification getNotificationById(String id);

    /**
     * 根据单据编号查询钻具通知单
     * 
     * @param no 单据编号
     * @return 钻具通知单
     */
    DrillingToolNotification getNotificationByNo(String no);

    /**
     * 更新钻具通知单
     * 
     * @param notification 钻具通知单实体
     * @return 更新后的钻具通知单
     */
    DrillingToolNotification updateNotification(DrillingToolNotification notification);

    /**
     * 删除钻具通知单（软删除）
     * 
     * @param id 钻具通知单ID
     */
    void deleteNotification(String id);

    /**
     * 查询所有钻具通知单
     * 
     * @return 钻具通知单列表
     */
    List<DrillingToolNotification> getAllNotifications();

    /**
     * 根据条件查询钻具通知单
     * 
     * @param conditions 查询条件
     * @return 钻具通知单列表
     */
    List<DrillingToolNotification> getNotificationsByConditions(Map<String, Object> conditions);

    /**
     * 根据日期范围查询钻具通知单
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 钻具通知单列表
     */
    List<DrillingToolNotification> getNotificationsByDateRange(Date startDate, Date endDate);

    /**
     * 查询待同步到OA的钻具通知单
     * 
     * @return 待同步的钻具通知单列表
     */
    List<DrillingToolNotification> getPendingSyncNotifications();

    /**
     * 同步钻具通知单到OA系统
     * 
     * @param id 钻具通知单ID
     * @return 同步结果
     */
    boolean syncNotificationToOA(String id);

    /**
     * 批量同步钻具通知单到OA系统
     * 
     * @return 同步结果统计
     */
    Map<String, Object> batchSyncNotificationsToOA();

    /**
     * 更新钻具通知单OA同步状态
     * 
     * @param id 钻具通知单ID
     * @param syncStatus 同步状态
     * @param syncError 同步错误信息（可选）
     * @return 更新后的钻具通知单
     */
    DrillingToolNotification updateSyncStatus(String id, String syncStatus, String syncError);

    /**
     * 生成OA送签数据
     * 
     * @param notification 钻具通知单
     * @return OA送签数据
     */
    Map<String, Object> generateOASubmitData(DrillingToolNotification notification);

    /**
     * 统计钻具通知单数量
     * 
     * @return 统计结果
     */
    Map<String, Long> countNotifications();
}
