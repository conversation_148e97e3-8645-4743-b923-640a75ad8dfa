package net.tartan.platform.integration.controller;

import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.integration.entity.ResourceCategory;
import net.tartan.platform.integration.service.ResourceCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("resource/category")
public class ResourceCategoryController {

    @Autowired
    private ResourceCategoryService resourceCategoryService;

    /**
     * 查询资源类型列表
     */
    @RequestMapping("list")
    public CommonResult findList() {
        List<ResourceCategory> list = resourceCategoryService.findList();
        return CommonResult.success(list);
    }
}
