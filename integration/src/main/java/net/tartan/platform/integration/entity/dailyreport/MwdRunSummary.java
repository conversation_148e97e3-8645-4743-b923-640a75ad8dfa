package net.tartan.platform.integration.entity.dailyreport;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MwdRunSummary{
    private Long id;

    /**
     * 仪器运行报告id
     */
    private Long runReportId;

    /**
     * 日期yyyy-MM-dd
     */
    private String date;

    /**
     * 时间HH:mm:ss
     */
    private String time;
    private String startTime;
    private String endTime;

    /**
     * 井深
     */
    private BigDecimal depth;

    /**
     * 描述
     */
    private String description;

}
