package net.tartan.platform.integration.beans.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.enums.EnumMenuType;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class AddMenuInfoParams implements Serializable {
    /**
     * 父级ID
     */
    private Long parentId;

    /**
     * 菜单名称
     */
    private String title;

    /**
     * 类型
     */
    @NotNull
    private EnumMenuType type;

    /**
     * 菜单排序
     */
    private Integer sort;

    /**
     * 菜单名称
     */
    private String name;

    /**
     * 前端图标
     */
    private String icon;

    /**
     * 是否隐藏
     */
    private Boolean hidden;

    /**
     * 组件名称
     */
    private String component;

    /**
     * 路由地址
     */
    private String path;

    /**
     * 授权标识
     */
    private String authFlag;

    /**
     * 默认跳转路径
     */
    private String redirect;

    /**
     * 是否不适用缓存
     */
    private Boolean noCache;
}
