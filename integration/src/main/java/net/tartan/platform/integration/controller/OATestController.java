package net.tartan.platform.integration.controller;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * OA系统接口测试控制器
 * 基于ERP反编译代码的接口规范进行测试
 * 
 * <AUTHOR>
 * @since 2025-01-11
 */
@RestController
@RequestMapping("/api/oa-test")
public class OATestController {

    @Value("${oa.base-url:http://***************:8096/seeyon/rest}")
    private String oaBaseUrl;

    @Value("${oa.rest.auth.username:rest_erp}")
    private String oaUsername;

    @Value("${oa.rest.auth.password:tartanoa2022}")
    private String oaPassword;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 测试OA认证接口
     * 基于ERP代码：ip["SERVICE_URL"].ToString() + "/token"
     * 
     * @return 认证测试结果
     */
    @PostMapping("/auth")
    public Map<String, Object> testOAAuth() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 构建认证URL
            String authUrl = oaBaseUrl + "/token";
            
            // 构建认证请求体（基于ERP代码格式）
            Map<String, String> authRequest = new HashMap<>();
            authRequest.put("userName", oaUsername);
            authRequest.put("password", oaPassword);
            
            String requestBody = objectMapper.writeValueAsString(authRequest);
            
            // 发送认证请求
            String response = sendPostRequest(authUrl, requestBody, null);
            
            // 解析响应
            JsonNode responseNode = objectMapper.readTree(response);
            
            result.put("success", true);
            result.put("authUrl", authUrl);
            result.put("requestBody", requestBody);
            result.put("response", response);
            
            // 检查是否有token
            if (responseNode.has("id")) {
                result.put("token", responseNode.get("id").asText());
                result.put("authSuccess", true);
            } else {
                result.put("authSuccess", false);
                result.put("message", responseNode.has("message") ? 
                    responseNode.get("message").asText() : "No token received");
            }
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("errorType", e.getClass().getSimpleName());
        }
        
        return result;
    }

    /**
     * 测试OA数据推送接口
     * 基于ERP代码：ip["SERVICE_URL"].ToString() + "/customSendForm"
     * 
     * @return 推送测试结果
     */
    @PostMapping("/push-data")
    public Map<String, Object> testOADataPush() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 1. 先获取认证token
            String token = getAuthToken();
            if (token == null) {
                result.put("success", false);
                result.put("error", "Failed to get auth token");
                return result;
            }
            
            // 2. 构建测试数据（基于ERP代码的数据结构）
            Map<String, Object> testData = createTestPaymentData();
            String requestBody = objectMapper.writeValueAsString(testData);
            
            // 3. 发送数据推送请求
            String pushUrl = oaBaseUrl + "/customSendForm";
            String response = sendPostRequest(pushUrl, requestBody, token);
            
            // 4. 解析响应
            JsonNode responseNode = objectMapper.readTree(response);
            
            result.put("success", true);
            result.put("pushUrl", pushUrl);
            result.put("token", token);
            result.put("requestBody", requestBody);
            result.put("response", response);
            
            // 检查推送结果
            if (responseNode.has("type")) {
                String type = responseNode.get("type").asText();
                result.put("pushSuccess", "S".equals(type));
                result.put("responseType", type);
                if (responseNode.has("message")) {
                    result.put("message", responseNode.get("message").asText());
                }
            }
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("errorType", e.getClass().getSimpleName());
        }
        
        return result;
    }

    /**
     * 获取认证token
     */
    private String getAuthToken() {
        try {
            String authUrl = oaBaseUrl + "/token";
            Map<String, String> authRequest = new HashMap<>();
            authRequest.put("userName", oaUsername);
            authRequest.put("password", oaPassword);
            
            String requestBody = objectMapper.writeValueAsString(authRequest);
            String response = sendPostRequest(authUrl, requestBody, null);
            
            JsonNode responseNode = objectMapper.readTree(response);
            return responseNode.has("id") ? responseNode.get("id").asText() : null;
            
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 创建测试用的付款申请单数据
     * 基于ERP代码中的数据结构
     */
    private Map<String, Object> createTestPaymentData() {
        Map<String, Object> root = new HashMap<>();
        root.put("sendMemberCode", "TEST_USER");
        root.put("templateCode", "YFSHD_001");
        
        Map<String, Object> data = new HashMap<>();
        data.put("Owner_Org", "测试公司");
        data.put("DOC_NO", "TEST_PAY_" + System.currentTimeMillis());
        data.put("REQ_EMPLOYEE_ID", "测试申请人");
        data.put("DOC_DATE", "2025-01-14");
        data.put("SETTLEMENT_OBJECT_TYPE", "供应商");
        data.put("SOURCE_ID", "测试供应商");
        data.put("SETTLEMENT_METHOD_ID", "银行转账");
        data.put("CURRENCY_ID", "人民币");
        data.put("COLLECTING_BANK_ACCOUNT_NO", "**********");
        data.put("COLLECTING_BANK_ACCOUNT_NAME", "测试账户");
        data.put("COLLECTING_BANK_FULL_NAME", "测试银行");
        data.put("COLLECTING_BANK_NO", "123456");
        
        // 添加子表数据
        Map<String, Object> subItem = new HashMap<>();
        subItem.put("PAYMENT_NATURE", "货款");
        subItem.put("PAYMENT_USAGE", "采购付款");
        subItem.put("OTHER_ARAP_ITEM_ID", "其他应付");
        subItem.put("PROJECT_ID", "测试项目");
        subItem.put("SOURCE_ID", "TEST_SOURCE");
        subItem.put("CURRENCY_ID", "人民币");
        subItem.put("INSTALLMENT_FLAG", "N");
        subItem.put("REQ_AMT_TC", "1000.00");
        
        data.put("sub", new Object[]{subItem});
        root.put("data", data);
        
        return root;
    }

    /**
     * 发送POST请求
     */
    private String sendPostRequest(String url, String requestBody, String token) throws Exception {
        URL urlObj = new URL(url);
        HttpURLConnection connection = (HttpURLConnection) urlObj.openConnection();
        
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "application/json;charset=UTF-8");
        connection.setDoOutput(true);
        
        // 如果有token，添加到header中（基于ERP代码）
        if (token != null) {
            connection.setRequestProperty("token", token);
        }
        
        // 发送请求体
        try (OutputStream os = connection.getOutputStream()) {
            byte[] input = requestBody.getBytes(StandardCharsets.UTF_8);
            os.write(input, 0, input.length);
        }
        
        // 读取响应
        StringBuilder response = new StringBuilder();
        try (BufferedReader br = new BufferedReader(
                new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
            String responseLine;
            while ((responseLine = br.readLine()) != null) {
                response.append(responseLine.trim());
            }
        }
        
        return response.toString();
    }

    /**
     * 获取OA系统配置信息
     */
    @GetMapping("/config")
    public Map<String, Object> getOAConfig() {
        Map<String, Object> result = new HashMap<>();
        
        result.put("oaBaseUrl", oaBaseUrl);
        result.put("authUrl", oaBaseUrl + "/token");
        result.put("pushUrl", oaBaseUrl + "/customSendForm");
        result.put("username", oaUsername);
        result.put("templateCode", "YFSHD_001");
        result.put("timestamp", System.currentTimeMillis());
        
        return result;
    }
}
