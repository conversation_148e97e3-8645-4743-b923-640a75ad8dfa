package net.tartan.platform.integration.beans.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.enums.EnumHandoverStatus;
import net.tartan.platform.common.enums.EnumHandoverType;
import net.tartan.platform.common.enums.EnumRiskType;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class HandoverVo {

    /**
     * 交接单id
     */
    private Long id;

    /**
     * 交接单类型
     * 根据类型来决定
     */
    private EnumHandoverType handoverType;

    private Long businessId;

    /**
     * 工单单号
     */
    private String businessNumber;

    /**
     * 仪器品名（如果只显示类型 其它类型的就不知道是什么东西了
     */
    private String invName;

    /**
     * 仪器类型
     */
    private Long deviceType;
    private String deviceTypeStr;

    /**
     * 序列号
     */
    private String serialNumber;

    /**
     * 风险类型
     */
    private EnumRiskType riskType;

    private String versionNumber;

    /**
     * 交接信息
     */
    private Long handoverUserId;
    private String handoverUserName;
    private String handoverDate;

    /**
     * 接收信息
     */
    private Long receiveUserId;
    private String receiveUserName;
    private String receivedDate;

    /**
     * 交接状态
     */
    private EnumHandoverStatus handoverStatus;
    private String handoverStatusStr;

    private Long status;
    private String statusStr;

    // 是否有sn号 0表示无 1表示有
    // 如果是无号 则代表数据是device_no_sn表中的
    // 如果是有号 则代表数据是device表中的
    private Integer hasSn;

    /**
     * kit箱编号
     */
    private String kitNumber;

    /**
     * 使用地点
     */
    private String location;

    /**
     * 完成日期
     */
    private String endDate;

    /**
     * 备注
     */
    private String note;


    private Date createTime;

    private Date updateTime;
}
