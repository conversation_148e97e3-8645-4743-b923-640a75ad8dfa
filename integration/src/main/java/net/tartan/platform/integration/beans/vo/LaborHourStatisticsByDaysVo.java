package net.tartan.platform.integration.beans.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

// 工时管理-总体统计的视图数据
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LaborHourStatisticsByDaysVo {
    private List<LaborHourByDaysVo> laborHourByDaysList;

    // 工时天数 (用总工时/每天工作时长8小时得出)
    private BigDecimal totalLaborDays;

    private BigDecimal totalLaborHours;
}
