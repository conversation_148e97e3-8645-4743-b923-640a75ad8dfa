package net.tartan.platform.integration.beans.query;

import lombok.Data;
import net.tartan.platform.common.enums.EnumHandoverType;

import java.util.List;

@Data
public class HandoverQuery {
    private List<Long> idList;
    private List<Long> mwdIdList;
    private String startDate;
    private String endDate;

    private String handoverType;

    private String mwdNumber;
    private String rmaNumber;
    private String businessNumber;
    private String handoverUsername;
    private String receiveUsername;
    private String serialNumber;
    private Long deviceType;
    private String deviceTypeStr;
    private String handoverStatus;// HANDOVER RECEIVED IGNORE

    private String orderBy;// mwdId handoverStatus endDate完成时间 receiveDate handoverDate
    private String orderType;// desc asc
}
