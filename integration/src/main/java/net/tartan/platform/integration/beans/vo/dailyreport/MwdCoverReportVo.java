package net.tartan.platform.integration.beans.vo.dailyreport;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.enums.EnumReportType;
import net.tartan.platform.integration.entity.dailyreport.MwdBoreholeInfo;
import net.tartan.platform.integration.entity.dailyreport.MwdCoverPersonnel;

import java.math.BigDecimal;
import java.util.List;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MwdCoverReportVo {

    private EnumReportType reportType;

    private long lastModified;

    /**
     * 客户
     */
    private String client;

    /**
     * 井号
     */
    private String wellNumber;
    /**
     * 仪器箱
     */
    private String kit;

    /**
     * 井位置
     */
    private String location;

    /**
     * 区块
     */
    private String field;

    /**
     * 钻井承包商
     */
    private String drillContractor;

    /**
     * 泥浆泵类型、尺寸
     */
    private String mudPump;

    /**
     * 钻具扣型
     */
    private String drillPipe;

    /**
     * 省份
     */
    private String province;

    /**
     * 开钻日期yyyy-MM-dd
     */
    private String dateIn;

    /**
     * 完钻日期yyyy-MM-dd
     */
    private String dateOut;

    /**
     * 仪器下趟数
     */
    private Integer totalMwdRuns;

    /**
     * 伽马/井深系统
     */
    private String gamma;

    /**
     * 仪器失效
     */
    private Integer mwdFailures;

    /**
     * 井型
     */
    private String wellType;

    /**
     * 通电
     */
    private BigDecimal electrical;

    /**
     * 循环
     */
    private BigDecimal circulating;

    /**
     * 纯钻
     */
    private BigDecimal drilling;

    /**
     * 描述
     */
    private String comment;

    /**
     * 人员
     */
    private List<MwdCoverPersonnel> personnelList;

    /**
     * 井眼数据
     */
//    private List<MwdBoreholeInfo> holeInfoList;
    private MwdBoreholeInfo holeInfo;

}
