package net.tartan.platform.integration.entity.dailyreport;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MwdTool {
    /**
     * 存货编码
     */
    private String invCode;
    /**
     * 存货名称
     */
    private String invName;
    /**
     * 存货大类编号
     */
    private String invClassCode;
    /**
     * 存货大类名称
     */
    private String invClassName;

    /**
     * 序列号
     */
    private String serialNumber;

    /**
     * 开始
     */
    private BigDecimal start;

    /**
     * 今天
     */
    private BigDecimal today;

    /**
     * 总计
     */
    private BigDecimal total;
    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 是否在井下或者下过井
     */
    private boolean inTheHule;

    /**
     * 备注
     */
    private String description;

    /**
     * 最后由什么调拨单导入的
     */
    private Long transferId;

    /**
     * 状态
     */
    private String status;
}
