package net.tartan.platform.integration.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.common.beans.ResultCode;
import net.tartan.platform.common.enums.EnumMassFileType;
import net.tartan.platform.integration.beans.vo.MwdMassFileVo;
import net.tartan.platform.integration.entity.massFile.MassFileInfo;
import net.tartan.platform.integration.entity.massFile.MassFileModel;
import net.tartan.platform.integration.exception.BusinessException;
import net.tartan.platform.integration.service.MassFileService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;

/**
 * massFile 工单文件管理
 */
@RestController
@RequestMapping("massFile")
public class MassFileController {

    @Autowired
    private MassFileService massFileService;

    @Autowired
    private HttpServletResponse httpServletResponse;

    //    @ResponseBody
//    @RequestMapping("uploadFiles_v2")
//    public CommonResult uploadFiles_v2(@RequestParam(value = "file") MultipartFile file) throws IOException {
//        String message = fileInfoService.uploadFiles_v2(file);
//        return CommonResult.success(message);
//    }

//    @ResponseBody
//    @RequestMapping("upload")
//    public CommonResult uploadMassFile(@RequestParam(value = "file") MultipartFile file) throws IOException {
//        MassFileModel fileModel = massFileService.uploadMassFile(file);
//        httpServletResponse.setContentType("text/html;charset=UTF-8");
//        return CommonResult.success(fileModel);
//    }

//    @PostMapping("insert")
//    public CommonResult insertMassFileRelation(@RequestBody @Validated MassFileInfo massFileInfo) {
//        httpServletResponse.setContentType("text/html;charset=UTF-8");
//        return CommonResult.success(massFileService.insertMassFileRelation(massFileInfo));
//    }

    // 专门用来上传完井资料文件的接口 // 为了和老版本客户端兼容 暂不做整合
    @ResponseBody
    @RequestMapping("upload_v2")
    public CommonResult uploadMassFile_v2(
            @RequestParam(value = "jobId") Long jobId,
            @RequestParam(value = "file") MultipartFile file) throws IOException {
        httpServletResponse.setContentType("text/html;charset=UTF-8");
        return CommonResult.success(massFileService.upload(jobId, file, EnumMassFileType.WELL_FINISH_FILE));
    }

    // 用来上传井下工具维修工单文件的接口 //被整合
    @ResponseBody
    @RequestMapping("upload/pitToolsWorkOrder")
    public CommonResult uploadMassFile_pitTools(
            @RequestParam(value = "pitToolsId") Long pitToolsId,
            @RequestParam(value = "file") MultipartFile file) throws IOException {
        httpServletResponse.setContentType("text/html;charset=UTF-8");
        return CommonResult.success(massFileService.upload(pitToolsId, file, EnumMassFileType.PIT_TOOLS_WORK_ORDER_FILE));
    }

    // 专门用来上传维修工单资料文件的接口 //被整合
    @ResponseBody
    @RequestMapping("upload/mwdWorkOrder")
    public CommonResult uploadMassFile_repairFile(
            @RequestParam(value = "mwdId") Long mwdId,
            @RequestParam(value = "file") MultipartFile file) throws IOException {
        httpServletResponse.setContentType("text/html;charset=UTF-8");
        return CommonResult.success(massFileService.upload(mwdId, file, EnumMassFileType.MWD_WORK_ORDER_FILE));
    }

    /**
     * 文件上传
     * @param id 根据fileType 可以是 mwdId，pitToolsId, projectId, rmaId, rmaRepairId
     * @param file
     * @param fileType 用来区分文件的用途。
     *                 PROJECT_FILE 研发项目
     *                 PIT_TOOLS_WORK_ORDER_FILE 井下工具维修工单
     *                 MWD_WORK_ORDER_FILE mwd维修工单
     *                 RMA RMA维修工单
     *                 RMA_REPAIR 核心部件返修单
     * @return
     * @throws IOException
     */
    @ResponseBody
    @RequestMapping("upload")
    public CommonResult uploadMassFile_project(
            @RequestParam(value = "id") Long id,
            @RequestParam(value = "file") MultipartFile file,
            @RequestParam(value = "fileType") String fileType) throws IOException {
        httpServletResponse.setContentType("text/html;charset=UTF-8");
        return CommonResult.success(massFileService.upload(id, file, EnumMassFileType.getByType(fileType)));
    }

    @PostMapping("mwdFileList/{current}/{size}")
    public CommonResult selectMwdMassFileList(@PathVariable long current,
                                              @PathVariable long size,
                                              @RequestBody MwdMassFileVo param) {
        if (param == null) {
            param = new MwdMassFileVo();
        }
        IPage<MwdMassFileVo> page = new Page<>(current, size);
        massFileService.selectMwdMassFileList(page, param);
        httpServletResponse.setContentType("text/html;charset=UTF-8");
        return CommonResult.success(page);
    }

    /**
     * 根据文件的业务类型获取所有的文件后缀名
     * 业务类型为mass_file_relation中的mass_file_type字段
     * 完井资料-WELL_FINISH_FILE, 维修单资料-MWD_WORK_ORDER_FILE，车间检查-FAILURE_SHOP_FILE
     * @param massFileType
     * @return
     */
    @GetMapping("typeList")
    public CommonResult selectTypeList(@RequestParam("massFileType") String massFileType) {
        List<String> typeList = massFileService.selectTypeList(massFileType);
        return CommonResult.success(typeList);
    }

    // 专门用来上传车间检查资料文件的接口
    @ResponseBody
    @RequestMapping("upload/failureShop")
    public CommonResult uploadMassFile_failureShopFile(
            @RequestParam(value = "jobId") Long jobId,
            @RequestParam(value = "date") String date,
            @RequestParam(value = "file") MultipartFile file,
            @RequestParam(value = "isUnique", required = false, defaultValue = "N") String isUnique
            ) throws IOException {
        httpServletResponse.setContentType("text/html;charset=UTF-8");
        return CommonResult.success(massFileService.upload(jobId, date, file, EnumMassFileType.FAILURE_SHOP_FILE, isUnique));
    }

//    @ResponseBody
//    @RequestMapping("upload_nas")
//    public CommonResult uploadMassFileToNas(
//            @RequestParam(value = "file") MultipartFile file) throws Exception {
//        httpServletResponse.setContentType("text/html;charset=UTF-8");
//        massFileService.uploadFileToNas(file);
//        return CommonResult.success();
//    }

    // 用来查询日报中的文件的接口
    @PostMapping("listByJob/{current}/{size}")
    public CommonResult selectMassFileListByJob(@PathVariable long current,
                                                @PathVariable long size,
                                                @RequestBody MassFileModel massFileInfo) {
        IPage<MassFileModel> page = new Page<>(current, size);
        massFileService.selectMassFileListByJob(page, massFileInfo, EnumMassFileType.WELL_FINISH_FILE);
        httpServletResponse.setContentType("text/html;charset=UTF-8");
        return CommonResult.success(page);
    }

    // 专门用来查询mwd维修工单资料文件的接口 //被整合
    @PostMapping("listByMwdId/{current}/{size}")
    public CommonResult selectMassFileListByMwd(@PathVariable long current,
                                                @PathVariable long size,
                                                @RequestBody MassFileModel massFileInfo) {
        IPage<MassFileModel> page = new Page<>(current, size);
        massFileService.selectMassFileListByJob(page, massFileInfo, EnumMassFileType.MWD_WORK_ORDER_FILE);
        httpServletResponse.setContentType("text/html;charset=UTF-8");
        return CommonResult.success(page);
    }

    // 用来查询失效报告文件的接口 //被整合
    @PostMapping("listByReport/{current}/{size}")
    public CommonResult selectFailureFileList(@PathVariable long current,
                                              @PathVariable long size,
                                              @RequestBody MassFileModel massFileInfo) {
        IPage<MassFileModel> page = new Page<>(current, size);
        massFileService.selectMassFileListByJob(page, massFileInfo, EnumMassFileType.FAILURE_SHOP_FILE);
        httpServletResponse.setContentType("text/html;charset=UTF-8");
        return CommonResult.success(page);
    }

    // 用来查询失效报告文件的接口 //被整合
    @PostMapping("listByPitToolsId/{current}/{size}")
    public CommonResult selectPitToolsFileList(@PathVariable long current,
                                              @PathVariable long size,
                                              @RequestBody MassFileModel massFileInfo) {
        IPage<MassFileModel> page = new Page<>(current, size);
        massFileService.selectMassFileListByJob(page, massFileInfo, EnumMassFileType.PIT_TOOLS_WORK_ORDER_FILE);
        httpServletResponse.setContentType("text/html;charset=UTF-8");
        return CommonResult.success(page);
    }

    /**
     * 用于 mwd工单、失效报告、井下工具、研发项目管理 的文件列表查询
     * @param current
     * @param size
     * @param massFileInfo
     * @return
     */
    @PostMapping("list/{current}/{size}")
    public CommonResult list(@PathVariable long current,
                                              @PathVariable long size,
                                              @RequestBody MassFileModel massFileInfo) {
        IPage<MassFileModel> page = new Page<>(current, size);
        massFileService.selectMassFileListByJob(page, massFileInfo, massFileInfo.getMassFileType());
        httpServletResponse.setContentType("text/html;charset=UTF-8");
        return CommonResult.success(page);
    }

    // 专门用来删除完井资料文件的接口

    /**
     * @param type       根据作业id JOBID 或者 文件id MASSID 来删除
     * @param massIdList
     * @return
     */
    @PostMapping("delete")
    @ResponseBody
    public CommonResult deleteMassFileByList(@RequestParam("type") String type,
                                             @RequestParam("idList") List<Long> massIdList) {
        massFileService.deleteMassFileByList(type, massIdList);
        httpServletResponse.setContentType("text/html;charset=UTF-8");
        return CommonResult.success();
    }

    // 专门用来删除mwd维修工单资料文件的接口

    /**
     * @param type   根据作业id mwdId 或者 文件id massId 来删除
     * @param idList
     * @return
     */
    @PostMapping("delete/mwdWorkOrder")
    @ResponseBody
    public CommonResult deleteMwdMassFileByList(@RequestParam("type") String type,
                                                @RequestParam("idList") List<Long> idList) {
        massFileService.deleteMassFileByList(type, idList);
        httpServletResponse.setContentType("text/html;charset=UTF-8");
        return CommonResult.success();
    }

    /**
     * @param massIdList 文件id list
     * @return
     */
    @PostMapping("delete/failureFile")
    @ResponseBody
    public CommonResult deleteFailureFileByList(
            @RequestParam("idList") List<Long> massIdList) {
        massFileService.deleteMassFileByList(massIdList);
        httpServletResponse.setContentType("text/html;charset=UTF-8");
        return CommonResult.success();
    }

    /**
     *
     * @param idList 根据文件id MASSID 来下载
     * @return
     */
    @PostMapping("download")
    @ResponseBody
    public CommonResult downloadMassFile(@RequestParam("massIdList") List<Long> idList){
        if(ObjectUtils.isEmpty(idList) ){
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        massFileService.downloadResourceFile(idList, httpServletResponse);
        httpServletResponse.setContentType("text/html;charset=UTF-8");
        return CommonResult.success();
    }

    /**
     *  将指定的文件解压，存放于临时文件夹中，并返回文件路径
     */
    @PostMapping("/preview")
    public CommonResult massFilePreview(@RequestBody MassFileModel massFileInfo) {
        if(massFileInfo == null){
            massFileInfo = new MassFileModel();
        }
        MassFileModel massFileModel = massFileService.massFilePreview(massFileInfo);
        httpServletResponse.setContentType("text/html;charset=UTF-8");
        return CommonResult.success(massFileModel);
    }

}
