package net.tartan.platform.integration.beans.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import net.tartan.platform.common.enums.EnumCirculateType;
import net.tartan.platform.integration.beans.dto.DeviceDTO;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
public class DeviceBatchUpdateVo {

    private List<DeviceDTO> deviceList;

    private Long relateBoxId;

    private EnumCirculateType circulateType;

    private Long businessId;
}
