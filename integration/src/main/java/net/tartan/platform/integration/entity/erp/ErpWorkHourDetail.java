package net.tartan.platform.integration.entity.erp;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Digits;
import java.math.BigDecimal;

/**
 * 工单信息主表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("WIP_TRANSFER_DOC_D")
public class ErpWorkHourDetail {

    /**
     * 主表外键
     * WIP_TRANSFER_DOC_ID
     */
    private String transferDocId;

    /**
     * 通过ITEM_ID与ITEM的表查到的信息
     * 品号
     * ITEM_NAME
     */
    private String invName;

    /**
     * 通过ITEM_ID与ITEM的表查到的信息
     * 序列号
     * ITEM_CODE
     */
    private String serialNumber;

    /**
     * 规格
     */
    private String specification;

    /**
     * 人员工时
     */
    private BigDecimal laborHour;

    /**
     * 人员姓名
     */
    private String memberName;
    private Long memberId;

    /**
     * 机时
     */
    private BigDecimal machineHour;

    /**
     * 部门名
     */
    private String department;
}
