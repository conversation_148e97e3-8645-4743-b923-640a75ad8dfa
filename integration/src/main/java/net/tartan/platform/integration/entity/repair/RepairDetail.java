package net.tartan.platform.integration.entity.repair;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.tartan.platform.common.enums.EnumRepairReceiveStatus;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class RepairDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "repair_detail_id", type = IdType.AUTO)
    private Long repairDetailId;

    /**
     * 返修单号
     */
    private Long repairId;

    /**
     * 品名
     */
    private String invName;

    /**
     * 序列号
     */
    private String serialNumber;
    /**
     * 修正后的序列号
     */
    private String serialNumberUpdated;

    /**
     * 入井时间
     */
    private BigDecimal inWellHour;

    /**
     * 循环时间
     */
    private BigDecimal circulateHrs;

    /**
     * 循环温度
     */
    private BigDecimal circulateBht;

    /**
     * 最高温度
     */
    private BigDecimal maxBht;

    /**
     * 趟次
     */
    private String run;

    /**
     * 返回原因
     */
    private String returnReason;

    /**
     * 备注
     */
    private String note;

    /**
     * 返修单接收状态
     * SUBMITTED - 已提交（未接收）
     * RECEIVED - 已接收
     */
    private EnumRepairReceiveStatus receiveStatus;

    private String receiveUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date receiveDate;

    private Long mwdId;

    //是否存在故障
    private String failureStatus;

    //故障描述
    private String failureDescription;

    //是否有震动超标
    private String vssStatus;

    // 维修等级
    private Integer repairLevel;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


}
