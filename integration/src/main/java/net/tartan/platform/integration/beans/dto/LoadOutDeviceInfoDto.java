package net.tartan.platform.integration.beans.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.integration.beans.vo.ComponentInfoDetailVo;

import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LoadOutDeviceInfoDto {

    private Long deviceId;
    private String invName;
    private String serialNumber;
    private String deviceTypeStr;
    private Long deviceType;
    private List<ComponentInfoDetailVo> componentInfoDetailVos;
}
