package net.tartan.platform.integration.beans.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * VO for Map Job Info Display
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class JobMapInfoVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 作业编号
     */
    private Long id;

    /**
     * 作业号 (报告井名 from job_info)
     */
    private String jobNumber;

    /**
     * 井号 (well_id from job_info/well_info)
     */
    private String wellId;

     /**
     * 井名 (well_number from well_info, used as 'name' in frontend)
     */
    private String name; // Using 'name' to match frontend expectation

    /**
     * 服务类型: 仪器服务/大包服务/工程服务
     */
    private String serveType;

    /**
     * 总趟数
     */
    private Long totalRun;

    /**
     * 失效趟数
     */
    private Long failureRun;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 开钻日期
     */
    private String dateIn;

    /**
     * 完钻日期
     */
    private String dateOut;

    /**
     * 作业状态 (工程状态)
     */
    private Integer jobStatus; // Changed Byte to Integer for easier handling

    /**
     * 经纬度 [longitude, latitude]
     */
    private List<Double> position;

    /**
     * well_info description (原始坐标，调试用，可选)
     */
    private String description;

}
