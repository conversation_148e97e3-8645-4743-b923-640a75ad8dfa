package net.tartan.platform.integration.config;

//import net.tartan.platform.integration.handler.TartanWebSocketHandler;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.scheduling.TaskScheduler;
//import org.springframework.scheduling.concurrent.ConcurrentTaskScheduler;
//import org.springframework.web.socket.config.annotation.EnableWebSocket;
//import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
//import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

//@Configuration
//@EnableWebSocket
public class WebSocketConfig{
//        implements WebSocketConfigurer {
//
//    @Bean
//    public TaskScheduler taskScheduler() {
//        return new ConcurrentTaskScheduler();
//    }
//    @Bean
//    public TartanWebSocketHandler myWebSocketHandler() {
//        return new TartanWebSocketHandler();
//    }
//
//    @Override
//    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
//        // 注册 WebSocket 处理器
//        registry.addHandler(myWebSocketHandler(), "/websocket/{port}").setAllowedOrigins("*");
//    }
//}


//    @Bean
//    public WebSocketHandler myWebSocketHandler() {
//        return new MyWebSocketHandler();
//    }
}