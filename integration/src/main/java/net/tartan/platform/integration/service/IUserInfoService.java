package net.tartan.platform.integration.service;

import com.baomidou.mybatisplus.extension.service.IService;
import net.tartan.platform.integration.beans.vo.UserAndRoleVo;
import net.tartan.platform.integration.beans.vo.UserInfoVo;
import net.tartan.platform.integration.entity.UserInfo;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * 账户信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-29
 */
public interface IUserInfoService extends IService<UserInfo> {

    List<UserAndRoleVo> listAll(UserAndRoleVo userInfo);

    List<UserInfoVo> listByRole(long roleId);

    List<UserInfoVo> listByMenuId(long menuId);

    void add(UserInfo userParams);

    boolean isExist(long userId);

    List<Long> filter(List<Long> userIdList);

    void delete(long userId);

    void batchDelete(List<Long> userIdList);

    boolean isAdmin(Long userId);

    List<Map<String, Objects>> listByOrgTree();

    String getMemberNameByUserId(Long userId);

    UserInfo getUserByName(String userName);
}
