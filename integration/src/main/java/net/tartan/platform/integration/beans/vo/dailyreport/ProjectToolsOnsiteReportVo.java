package net.tartan.platform.integration.beans.vo.dailyreport;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.enums.EnumReportType;
import net.tartan.platform.integration.entity.dailyreport.MwdTool;

import java.util.List;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectToolsOnsiteReportVo {

    private EnumReportType reportType;

    private long lastModified;

    /**
     * 概述
     */
    private List<MwdTool> toolList;

}
