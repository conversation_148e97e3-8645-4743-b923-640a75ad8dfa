package net.tartan.platform.integration.entity.dailyreport;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * tools_daily_device
 * <AUTHOR>
public class ToolsDailyDevice implements Serializable {
    private Long id;

    /**
     * 工具日报id
     */
    private Long toolsReportId;

    /**
     * 目前位置
     */
    private String position;

    /**
     * 部件名和序列号
     */
    private String nameSerialNum;

    /**
     * 井下时间
     */
    private BigDecimal downTime;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getToolsReportId() {
        return toolsReportId;
    }

    public void setToolsReportId(Long toolsReportId) {
        this.toolsReportId = toolsReportId;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getNameSerialNum() {
        return nameSerialNum;
    }

    public void setNameSerialNum(String nameSerialNum) {
        this.nameSerialNum = nameSerialNum;
    }

    public BigDecimal getDownTime() {
        return downTime;
    }

    public void setDownTime(BigDecimal downTime) {
        this.downTime = downTime;
    }
}
