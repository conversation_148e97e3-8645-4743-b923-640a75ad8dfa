package net.tartan.platform.integration.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.tartan.platform.integration.beans.vo.WorkOrderMwdRepairMemberVo;
import net.tartan.platform.integration.beans.vo.WorkOrderRmaRepairMemberVo;
import net.tartan.platform.integration.entity.WorkOrderMwdRepairMember;
import net.tartan.platform.integration.entity.WorkOrderRmaRepairMember;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

public interface WorkOrderRmaRepairMemberMapper extends BaseMapper<WorkOrderRmaRepairMember> {

    List<WorkOrderRmaRepairMemberVo> getByRmaId(@Param("rmaId") long rmaId);

    List<WorkOrderRmaRepairMemberVo> listByRmaIdList(@Param("rmaIdList") List<Long> rmaIdList);

    BigDecimal sumLaborHoursByRmaId(@Param("rmaId")Long rmaId);
}
