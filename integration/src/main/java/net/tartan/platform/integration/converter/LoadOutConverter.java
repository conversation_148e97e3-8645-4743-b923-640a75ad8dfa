package net.tartan.platform.integration.converter;

import net.tartan.platform.integration.beans.dto.LoadOutDTO;
import net.tartan.platform.integration.beans.dto.LoadOutDetailDTO;
import net.tartan.platform.integration.entity.LoadOut;
import net.tartan.platform.integration.entity.LoadOutDetail;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 旋导出库记录转换器
 */
public class LoadOutConverter {
    
    /**
     * 将实体转换为DTO
     */
    public static LoadOutDTO toDTO(LoadOut entity) {
        if (entity == null) {
            return null;
        }
        
        LoadOutDTO dto = new LoadOutDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }
    
    /**
     * 将DTO转换为实体
     */
    public static LoadOut toEntity(LoadOutDTO dto) {
        if (dto == null) {
            return null;
        }
        
        LoadOut entity = new LoadOut();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }
    
    /**
     * 将实体列表转换为DTO列表
     */
    public static List<LoadOutDTO> toDTOList(List<LoadOut> entityList) {
        if (entityList == null || entityList.isEmpty()) {
            return new ArrayList<>();
        }
        
        return entityList.stream()
                .map(LoadOutConverter::toDTO)
                .collect(Collectors.toList());
    }
    
    /**
     * 将实体转换为DTO
     */
    public static LoadOutDetailDTO toDetailDTO(LoadOutDetail entity) {
        if (entity == null) {
            return null;
        }
        
        LoadOutDetailDTO dto = new LoadOutDetailDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }
    
    /**
     * 将DTO转换为实体
     */
    public static LoadOutDetail toDetailEntity(LoadOutDetailDTO dto) {
        if (dto == null) {
            return null;
        }
        
        LoadOutDetail entity = new LoadOutDetail();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }
    
    /**
     * 将实体列表转换为DTO列表
     */
    public static List<LoadOutDetailDTO> toDetailDTOList(List<LoadOutDetail> entityList) {
        if (entityList == null || entityList.isEmpty()) {
            return new ArrayList<>();
        }
        
        return entityList.stream()
                .map(LoadOutConverter::toDetailDTO)
                .collect(Collectors.toList());
    }
} 