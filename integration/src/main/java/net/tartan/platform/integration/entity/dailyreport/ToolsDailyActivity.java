package net.tartan.platform.integration.entity.dailyreport;

import java.io.Serializable;

/**
 * tools_daily_activity
 * <AUTHOR>
public class ToolsDailyActivity implements Serializable {
    private Long id;

    /**
     * 工具日报id
     */
    private Long toolsReportId;

    /**
     * 起止时间
     */
    private String startEndTime;

    /**
     * 施工内容
     */
    private String content;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getToolsReportId() {
        return toolsReportId;
    }

    public void setToolsReportId(Long toolsReportId) {
        this.toolsReportId = toolsReportId;
    }

    public String getStartEndTime() {
        return startEndTime;
    }

    public void setStartEndTime(String startEndTime) {
        this.startEndTime = startEndTime;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
