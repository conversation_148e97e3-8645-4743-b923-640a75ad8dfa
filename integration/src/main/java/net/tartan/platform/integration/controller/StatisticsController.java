package net.tartan.platform.integration.controller;

import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.integration.beans.dto.JobStatisticsByTimeResult;
import net.tartan.platform.integration.beans.dto.JobStatisticsResult;
import net.tartan.platform.integration.beans.request.JobStatisticsByTimeRequest;
import net.tartan.platform.integration.beans.request.JobStatisticsRequest;
import net.tartan.platform.integration.service.statistics.JobStatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("statistics")
public class StatisticsController {

    @Autowired
    private JobStatisticsService jobStatisticsService;

    @PostMapping("job")
    public CommonResult jobStatistics(@RequestBody JobStatisticsRequest request) {
        final List<JobStatisticsResult> statistics = jobStatisticsService.statistics(request.getQueryCondition(), request.getStatisticsTypeList());
        return CommonResult.success(statistics);
    }

    @PostMapping("byTime")
    public CommonResult jobStatisticsByTime(@RequestBody JobStatisticsByTimeRequest request) {
        final List<JobStatisticsByTimeResult> statistics = jobStatisticsService.statisticsByTime(request);
        return CommonResult.success(statistics);
    }
}
