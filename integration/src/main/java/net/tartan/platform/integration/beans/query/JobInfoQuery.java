package net.tartan.platform.integration.beans.query;

import lombok.Data;

import java.util.Date;

@Data
public class JobInfoQuery {

    private String wellNumber;
    private String jobNumber;
    private String blocks;
    private Long jobType;
    private String dateInStart;
    private String dateInEnd;
    private String dateOutStart;
    private String dateOutEnd;
    /**
     * 工程状态（完井/施工中） 0 1
     */
    private Integer jobStatus;
    private Date createTime;//yyyy-MM-dd
    private Integer totalRun;
    private Integer failureRun;

    private String orderBy;// blocks jobType dateIn dateOut jobStatus createTime totalRun failureRun
    private String orderType;// desc asc
}
