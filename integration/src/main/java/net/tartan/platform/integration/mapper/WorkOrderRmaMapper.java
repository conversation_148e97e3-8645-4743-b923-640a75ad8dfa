package net.tartan.platform.integration.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import net.tartan.platform.integration.beans.query.WorkOrderRmaQuery;
import net.tartan.platform.integration.beans.vo.WorkOrderRmaDetailResponse;
import net.tartan.platform.integration.entity.WorkOrderRma;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface WorkOrderRmaMapper extends BaseMapper<WorkOrderRma> {


    IPage<WorkOrderRmaDetailResponse> list(IPage<WorkOrderRmaDetailResponse> page, @Param("query") WorkOrderRmaQuery query);

    List<String> selectDepartmentName(@Param("query") WorkOrderRmaQuery query);

    List<WorkOrderRma> selectByRmaNumber(@Param("rmaNumber") String number);

    List<WorkOrderRma> selectByMwdIdAndComponent(@Param("id") Long mwdId, @Param("invName") String invName, @Param("serialNumber") String serialNumber);
}
