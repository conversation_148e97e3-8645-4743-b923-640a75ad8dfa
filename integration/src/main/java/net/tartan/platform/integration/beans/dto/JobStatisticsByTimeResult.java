package net.tartan.platform.integration.beans.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class JobStatisticsByTimeResult {

    /**
     * 统计时间
     */
    private String time;
    /**
     * 大包服务数量
     */
    private Integer wellCount=0;
    /**
     * 大包服务数量
     */
    private Integer packageCount=0;
    /**
     * 仪器服务数量
     */
    private Integer apparatusCount=0;
    /**
     * 工具服务数量
     */
    private Integer toolCount=0;
    /**
     * 工程服务数量
     */
    private Integer projectCount=0;
    /**
     * 其他服务数量
     */
    private Integer otherCount=0;
    /**
     * 总进尺
     */
    private Double totalFootage =0.0;
    /**
     * 总入井趟次
     */
    private Integer totalRun=0;
    /**
     * 总失效趟次
     */
    private Integer failureRun=0;
    /**
     * 总失效趟次
     */
    private Double failureRate=0.0;

    /**
     * 方位gamma
     */
    private Integer azimuthGamma=0;
    /**
     * 自然gamma
     */
    private Integer naturalGamma=0;
    /**
     * 近钻头
     */
    private Integer atBit=0;
    /**
     * 失效原因
     */
    private FailureReasonStatisticsDto failureReason;

}
