package net.tartan.platform.integration.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.integration.entity.Department;
import net.tartan.platform.integration.service.IDepartmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * department 部门
 */
@RestController
@RequestMapping("department")
public class DepartmentController {

    @Autowired
    private IDepartmentService departmentService;


    @GetMapping("list")
    public CommonResult list() {
        LambdaQueryWrapper<Department> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Department::getIsEnable, 1);
        queryWrapper.eq(Department::getIsDelete, 0);
        List<Department> list = departmentService.list();
        return CommonResult.success(list);
    }
}
