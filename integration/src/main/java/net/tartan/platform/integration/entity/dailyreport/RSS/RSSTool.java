package net.tartan.platform.integration.entity.dailyreport.RSS;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.enums.EnumRSSToolStatusType;

import java.math.BigDecimal;
import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RSSTool {

    /**
     * 存货编码
     */
    private String invCode;

    /**
     * 存货名称
     */
    private String invName;

    /**
     * 存货大类编号
     */
    private String invClassCode;

    /**
     * 存货大类名称
     */
    private String invClassName;

    /**
     * 序列号
     */
    private String serialNumber;

    /**
     * 开始
     */
    private BigDecimal start;

    /**
     * 今天
     */
    private BigDecimal today;

    /**
     * 总计
     */
    private BigDecimal total;
    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 状态
     */
    private String status;

    /**
     * 状态类型
     */
    private EnumRSSToolStatusType statusType;

}
