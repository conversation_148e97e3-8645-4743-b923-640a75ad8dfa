//package net.tartan.platform.integration.beans.dto;
//
//import lombok.Data;
//
//import javax.validation.constraints.NotNull;
//import java.util.List;
//
///**
// * <p>
// * mwd工单信息表
// * </p>
// *
// * <AUTHOR>
// * @since 2022-05-09
// */
//@Data
//public class ComponentRepairMwdDto {
//    /**
//     * 台账id
//     */
//    @NotNull
//    private Long mwdId;
//
////    /**
////     * 母件存货编码
////     */
////    private String parentInvCode;
//    /**
//     * 母件品名
//     */
//    private String parentInvName;
//
//    /**
//     * 母件序列号，erp的批次号
//     */
//    private String parentSerialNumber;
//
//    /**
//     * 存货编码
//     */
//    private List<ComponentRepairRecordDto> repairList;
//}
