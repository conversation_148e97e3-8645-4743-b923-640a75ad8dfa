package net.tartan.platform.integration.controller;


import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.integration.entity.erp.Mo;
import net.tartan.platform.integration.service.erp.IErpMoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 工单/CHT/工單/ENU/MO 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-09
 */
@RestController
@RequestMapping("/mo")
public class MoController {

    @Autowired
    private IErpMoService moService;

    @PostMapping("list")
    public CommonResult list(@RequestParam(value = "startDate") String startDate,
                             @RequestParam(value = "endDate") String endDate) {
        List<Mo> moList = moService.list(startDate, endDate);
        return CommonResult.success(moList);
    }

    @PostMapping("info")
    public CommonResult list(@RequestParam(value = "docNo") String docNo) {
        Mo mo = moService.getDocInfo(docNo);
        return CommonResult.success(mo);
    }
}
