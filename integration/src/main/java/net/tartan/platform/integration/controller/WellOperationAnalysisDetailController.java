package net.tartan.platform.integration.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.integration.beans.vo.WellOperationAnalysisDetailBasicVO;
import net.tartan.platform.integration.beans.vo.WellOperationAnalysisDetailRichContentVO;
import net.tartan.platform.integration.beans.vo.WellOperationAnalysisDetailVO;
import net.tartan.platform.integration.beans.vo.WellSummaryVO;
import net.tartan.platform.integration.service.WellOperationAnalysisDetailService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

/**
 * WellOperationAnalysisDetailController
 * 井作业运行及失效分析详情总表 前端控制器。
 * 提供对井作业分析详情数据的RESTful API接口，包括查询、删除以及数据同步操作。
 */
@RestController
@RequestMapping("/wellAnalysis")
public class WellOperationAnalysisDetailController {

    private static final Logger log = LoggerFactory.getLogger(WellOperationAnalysisDetailController.class);

    private final WellOperationAnalysisDetailService wellOperationAnalysisDetailService;

    /**
     * 构造函数注入 WellOperationAnalysisDetailService。
     *
     * @param wellOperationAnalysisDetailService 井作业分析详情服务
     */
    @Autowired
    public WellOperationAnalysisDetailController(WellOperationAnalysisDetailService wellOperationAnalysisDetailService) {
        this.wellOperationAnalysisDetailService = wellOperationAnalysisDetailService;
    }

    /**
     * 启动新数据同步任务，跳过已存在的记录。
     * 此接口会触发一个后台过程，该过程遍历所有相关的作业信息 (JobInfo)，
     * 但只处理尚未同步的运行趟次 (run)，跳过已经存在于数据库中的记录。
     * 这是一个耗时操作，接口会立即返回，表明任务已启动。
     *
     * @return 任务启动成功的响应
     */
    @PostMapping("/synchronize-new")
    public CommonResult<String> synchronizeNewAnalysisData() {
        try {
            log.info("开始执行新数据同步任务");
            wellOperationAnalysisDetailService.processAndSynchronizeNewAnalysisData();
            log.info("新数据同步任务执行完成");
            return CommonResult.success("新数据同步任务已成功启动并完成");
        } catch (Exception e) {
            // 记录详细的错误信息，包括异常类型和堆栈跟踪
            log.error("新数据同步任务执行失败", e);

            // 检查是否是事务回滚异常
            String errorMessage = e.getMessage();
            if (errorMessage != null && errorMessage.contains("Transaction rolled back")) {
                return CommonResult.failed("新数据同步任务启动失败: 事务已被标记为回滚。这通常是由于处理过程中的某些记录出现错误导致的。请检查日志获取详细信息。");
            }

            return CommonResult.failed("新数据同步任务启动失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID删除井作业运行及失效分析详情。
     * 注意：此操作会直接从数据库中物理删除记录。
     * 如果需要逻辑删除，请确保Service层或Mapper层已正确处理is_deleted字段的更新逻辑，
     * 并修改此处的调用为逻辑删除方法。
     *
     * @param id 要删除的详情ID
     * @return 操作结果，成功则无数据返回，失败则包含错误信息
     */
    @DeleteMapping("/{id}")
    public CommonResult<Void> delete(@PathVariable Long id) {
        boolean success = wellOperationAnalysisDetailService.removeById(id);
        if (success) {
            return CommonResult.success(null, "删除成功");
        } else {
            return CommonResult.failed("删除失败，记录可能不存在或无法删除");
        }
    }

    /**
     * 根据ID获取井作业运行及失效分析详情，包含关联的工具信息。
     *
     * @param id 详情ID
     * @return 操作结果，包含查询到的 WellOperationAnalysisDetailVO 对象（包含工具信息）或错误信息
     */
    @GetMapping("/detail-with-instruments/{id}")
    public CommonResult<WellOperationAnalysisDetailVO> getDetailWithInstrumentsById(@PathVariable Long id) {
        WellOperationAnalysisDetailVO detailVO = wellOperationAnalysisDetailService.getDetailWithInstrumentsById(id);
        if (detailVO != null) {
            return CommonResult.success(detailVO);
        } else {
            return CommonResult.failed("未找到ID为 " + id + " 的记录");
        }
    }

    /**
     * 更新井作业运行及失效分析详情，同时更新关联的工具信息。
     * 此接口会在一个事务中同时处理主表和子表的更新操作，确保数据一致性。
     * 对于子表数据，采用按需更新的策略：
     * - 包含instrumentRecordId的记录将被更新
     * - 不包含instrumentRecordId的记录将被新增
     * - instrumentIdsToDelete中列出的ID对应的记录将被删除
     *
     * @param detailVO 包含主表和子表数据的VO对象
     * @return 操作结果，包含更新后的完整数据（包含工具信息）或错误信息
     */
    @PostMapping("/update-with-instruments")
    public CommonResult<WellOperationAnalysisDetailVO> updateDetailWithInstruments(@RequestBody WellOperationAnalysisDetailVO detailVO) {
        if (detailVO == null || detailVO.getId() == null) {
            return CommonResult.failed("更新失败：参数无效或ID为空");
        }

        try {
            WellOperationAnalysisDetailVO updatedDetail = wellOperationAnalysisDetailService.updateDetailWithInstruments(detailVO);
            if (updatedDetail != null) {
                return CommonResult.success(updatedDetail, "更新成功");
            } else {
                return CommonResult.failed("更新失败：记录可能不存在或更新过程中发生错误");
            }
        } catch (Exception e) {
            return CommonResult.failed("更新失败：" + e.getMessage());
        }
    }

    /**
     * 分页查询井作业运行及失效分析详情的基本信息（包含富文本字段）。
     * 此接口返回的数据包含表格主要显示字段和富文本内容，
     * 用于在表格中直接显示富文本预览。
     *
     * @param current 当前页码，默认为1
     * @param size 每页数量，默认为10
     * @param wellNumber 井号 (可选，模糊查询)
     * @param runNumber 趟次 (可选)
     * @param serviceWellCategory 井类别 (可选)
     * @param isFailure 是否故障 (可选)
     * @return 操作结果，包含分页数据 (IPage) 和基本信息，或错误信息
     */
    @GetMapping("/page-basic-info")
    public CommonResult<IPage<WellOperationAnalysisDetailBasicVO>> pageBasicInfo(
            @RequestParam(value = "current", defaultValue = "1") int current,
            @RequestParam(value = "size", defaultValue = "10") int size,
            @RequestParam(value = "wellNumber", required = false) String wellNumber,
            @RequestParam(value = "runNumber", required = false) Integer runNumber,
            @RequestParam(value = "serviceWellCategory", required = false) String serviceWellCategory,
            @RequestParam(value = "isFailure", required = false) String isFailure,
            @RequestParam(value = "orderBy", required = false) String orderBy,
            @RequestParam(value = "orderType", required = false) String orderType) {

        log.info("分页查询井作业分析基本信息 - 参数: current={}, size={}, wellNumber={}, runNumber={}, serviceWellCategory={}, isFailure={}, orderBy={}, orderType={}",
                current, size, wellNumber, runNumber, serviceWellCategory, isFailure, orderBy, orderType);

        try {
            IPage<WellOperationAnalysisDetailBasicVO> resultPage = wellOperationAnalysisDetailService.pageBasicInfo(
                current, size, wellNumber, runNumber, serviceWellCategory, isFailure, orderBy, orderType);
            return CommonResult.success(resultPage);
        } catch (Exception e) {
            log.error("分页查询井作业分析基本信息失败", e);
            return CommonResult.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据ID获取井作业运行及失效分析详情的富文本内容。
     * 此接口返回的数据包含展开行需要显示的字段，主要是富文本内容和其他详细信息。
     * 当用户点击展开某一行时，前端会根据该行的ID调用此接口获取完整的内容。
     *
     * @param id 详情ID
     * @return 操作结果，包含富文本内容的数据对象或错误信息
     */
    @GetMapping("/rich-text-content/{id}")
    public CommonResult<WellOperationAnalysisDetailRichContentVO> getRichTextContent(@PathVariable Long id) {
        try {
            WellOperationAnalysisDetailRichContentVO richContent = wellOperationAnalysisDetailService.getRichTextContentById(id);
            if (richContent != null) {
                return CommonResult.success(richContent);
            } else {
                return CommonResult.failed("未找到ID为 " + id + " 的记录");
            }
        } catch (Exception e) {
            log.error("获取井作业分析富文本内容失败", e);
            return CommonResult.failed("获取失败：" + e.getMessage());
        }
    }

    /**
     * 更新井作业运行及失效分析详情的单个富文本字段。
     * 此接口专门用于处理大型富文本内容，只更新指定的字段，避免一次性传输所有富文本内容导致的性能问题。
     * 支持的字段包括：failureReasonDescription、downholeInstrumentStatusDesc、fieldIncidentDescription、
     * workshopShopFinding、improvementPlanMeasures。
     *
     * @param params 包含ID、字段名和字段值的参数对象
     * @return 操作结果，成功则返回true，失败则包含错误信息
     */
    @PostMapping("/update-rich-text-field")
    public CommonResult<Boolean> updateRichTextField(@RequestBody Map<String, Object> params) {
        try {
            // 1. 参数校验
            if (params == null || !params.containsKey("id") || !params.containsKey("fieldName") || !params.containsKey("fieldValue")) {
                return CommonResult.failed("更新失败：参数无效，必须包含id、fieldName和fieldValue");
            }

            // 2. 提取参数
            Long id;
            try {
                id = Long.valueOf(params.get("id").toString());
            } catch (NumberFormatException e) {
                return CommonResult.failed("更新失败：id参数必须是有效的数字");
            }

            String fieldName = params.get("fieldName").toString();
            String fieldValue = params.get("fieldValue") != null ? params.get("fieldValue").toString() : "";

            // 3. 调用服务方法更新字段
            boolean success = wellOperationAnalysisDetailService.updateRichTextField(id, fieldName, fieldValue);
            if (success) {
                return CommonResult.success(true, "更新成功");
            } else {
                return CommonResult.failed("更新失败：字段更新过程中发生错误");
            }
        } catch (Exception e) {
            log.error("更新井作业分析富文本字段失败", e);
            return CommonResult.failed("更新失败：" + e.getMessage());
        }
    }

    /**
     * 根据井号获取该井所有趟次的汇总信息，用于井汇总视图的展开行显示。
     * 此接口专门为井汇总视图设计，返回指定井号下所有趟次的基本信息列表。
     *
     * @param wellNumber 井号
     * @return 操作结果，包含该井所有趟次的基本信息列表或错误信息
     */
    @GetMapping("/well-runs-summary/{wellNumber}")
    public CommonResult<java.util.List<WellOperationAnalysisDetailBasicVO>> getWellRunsSummary(@PathVariable String wellNumber) {
        try {
            log.info("获取井号 {} 的所有趟次汇总信息", wellNumber);
            java.util.List<WellOperationAnalysisDetailBasicVO> runsList = wellOperationAnalysisDetailService.getRunsByWellNumber(wellNumber);
            if (runsList != null && !runsList.isEmpty()) {
                log.info("成功获取井号 {} 的 {} 个趟次信息", wellNumber, runsList.size());
                return CommonResult.success(runsList);
            } else {
                log.warn("未找到井号 {} 的趟次信息", wellNumber);
                return CommonResult.failed("未找到井号为 " + wellNumber + " 的趟次记录");
            }
        } catch (Exception e) {
            log.error("获取井号 {} 的趟次汇总信息失败", wellNumber, e);
            return CommonResult.failed("获取失败：" + e.getMessage());
        }
    }

    /**
     * 导出井作业分析详情数据到Excel
     * 支持按照入井日期(dateIn)的年份进行分组，并将同一年份的数据放入同一个Excel工作表(sheet)中
     *
     * @param response HTTP响应对象，用于写入Excel文件
     * @param params 导出参数，包含查询条件
     */
    @GetMapping("/export")
    public void exportWellAnalysisDetails(HttpServletResponse response, @RequestParam Map<String, Object> params) {
        try {
            log.info("开始导出井作业分析详情数据，参数: {}", params);
            wellOperationAnalysisDetailService.exportWellAnalysisDetails(response, params);
            log.info("井作业分析详情数据导出完成");
        } catch (Exception e) {
            log.error("导出井作业分析详情数据失败", e);
            try {
                response.setContentType("application/json");
                response.setCharacterEncoding("utf-8");
                response.getWriter().write("{\"code\":500,\"message\":\"导出失败: " + e.getMessage() + "\"}");
            } catch (IOException ex) {
                log.error("写入错误响应失败", ex);
            }
        }
    }

    /**
     * 分页查询井汇总数据。
     * 按井号聚合数据，返回每个井的统计信息，专门用于井汇总视图。
     * 每页默认15条井记录，包含井号、区块、总趟次数、故障趟次数、故障率、施工周期、主要故障类型、工具总数等聚合字段。
     *
     * @param current 当前页码，默认为1
     * @param size 每页数量，默认为15
     * @param wellNumber 井号 (可选，模糊查询)
     * @param isFailure 是否故障 (可选)
     * @param orderBy 排序字段 (可选)
     * @param orderType 排序方式 (可选，asc或desc)
     * @return 操作结果，包含井汇总数据的分页结果或错误信息
     */
    @GetMapping("/page-well-summary")
    public CommonResult<IPage<WellSummaryVO>> pageWellSummary(
            @RequestParam(value = "current", defaultValue = "1") int current,
            @RequestParam(value = "size", defaultValue = "15") int size,
            @RequestParam(value = "wellNumber", required = false) String wellNumber,
            @RequestParam(value = "isFailure", required = false) String isFailure,
            @RequestParam(value = "orderBy", required = false) String orderBy,
            @RequestParam(value = "orderType", required = false) String orderType) {

        log.info("分页查询井汇总数据 - 参数: current={}, size={}, wellNumber={}, isFailure={}, orderBy={}, orderType={}",
                current, size, wellNumber, isFailure, orderBy, orderType);

        try {
            IPage<WellSummaryVO> resultPage = wellOperationAnalysisDetailService.pageWellSummary(
                current, size, wellNumber, isFailure, orderBy, orderType);
            return CommonResult.success(resultPage);
        } catch (Exception e) {
            log.error("分页查询井汇总数据失败", e);
            return CommonResult.failed("查询失败：" + e.getMessage());
        }
    }
}