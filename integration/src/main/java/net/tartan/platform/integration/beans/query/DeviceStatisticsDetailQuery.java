package net.tartan.platform.integration.beans.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.enums.EnumDeviceUseStatus;
import net.tartan.platform.common.enums.EnumToolType;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeviceStatisticsDetailQuery {

    /**
     * 仪器品名
     */
    private String invName;
    /**
     * 序列号
     */
    private String serialNumber;
    /**
     * 序列号
     */
    private Long deviceType;

    /**
     * 工具类型 MWD，UNDER_WELL
     */
    private EnumToolType toolType;

    private Integer businessType;

    /**
     * 井下工具仪器的使用状态
     * 使用中 - INUSE
     * 未使用 - UNUSED
     */
    private String useStatus;
}
