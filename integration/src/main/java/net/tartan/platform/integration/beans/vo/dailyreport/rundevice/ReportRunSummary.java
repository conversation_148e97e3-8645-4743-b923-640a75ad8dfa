package net.tartan.platform.integration.beans.vo.dailyreport.rundevice;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.constant.PfConstant;
import org.springframework.data.mongodb.core.mapping.Document;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
@Document(collection = PfConstant.REPORT_RUN_SUMMARY)
public class ReportRunSummary {

    private String wellNumber;

    private Long jobId;

    /**
     * 趟次
     */
    private Integer run;
    /**
     * MWD车间发现总结
     */
    private String mwdSummary;
}
