package net.tartan.platform.integration.beans.dto;

import lombok.Data;
import net.tartan.platform.integration.entity.ComponentTest;
import net.tartan.platform.integration.entity.WorkOrderMwdDetail;
import net.tartan.platform.integration.entity.WorkOrderMwdRepairMember;
import net.tartan.platform.integration.entity.deviceassemble.MwdDeviceAssembleRecord;
import net.tartan.platform.integration.entity.deviceassemble.MwdRepairCheckData;
import net.tartan.platform.integration.entity.deviceassemble.MwdRotaryAssembleRecord;

import java.util.List;

/**
 * mwd工单信息表
 *
 * <AUTHOR>
 * @since 2022-05-09
 */
@Data
public class WorkOrderMwdDto {

    private Long mwdId;

    private Long repairDetailId;
    /**
     * 工单单号
     */
    private String mwdNumber;

    /**
     * erp的维修单号
     */
    private String moDocNo;

    /**
     * 作业编号
     */
    private String jobNumber;

    /**
     * 井号
     */
    private String wellNumber;

    /**
     * kit箱编号
     */
    private String kitNumber;

    /**
     * 开始日期
     */
    private String startDate;

    /**
     * 完成日期
     */
    private String endDate;
    /**
     * 维修状态1：已完成，0：未完成，-1：滞留
     */
    private Integer finish;
    /**
     * 备注
     */
    private String notes;

    private List<WorkOrderMwdDetail> workOrderMwdDetailList;

    private MwdDeviceAssembleRecord mwdDeviceAssembleRecord;

    private MwdRepairCheckData mwdRepairCheckData;

    private List<WorkOrderMwdRepairMember> repairMemberList;

//    private List<ComponentTest> componentTestList;

}
