package net.tartan.platform.integration.beans.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.integration.entity.JobFileRelation;

import java.util.Date;
import java.util.List;

/**
 * 作业列表vo
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class JobInfoDetailVo {

    private static final long serialVersionUID = 1L;

    /**
     * 作业编号
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    private Long jobId;


    /**
     * 作业号，工单号
     *  - from JobInfo
     */
    private String jobNumber;

    /**
     * 井号
     *  - from JobInfo
     */
    private String wellId;

    /**
     * job 开钻日期
     */
    private String dateIn;
    /**
     * job 完钻日期
     */
    private String dateOut;

    /**
     * 井名
     *  - from WellInfo
     */
    private String wellNumber;

    /**
     * 服务类型
     *  - count EnumJobStatistics
     *      仪器服务 APPARATUS_COUNT
     *      大包服务 PACKAGE_COUNT
     *      工程服务 PROJECT_COUNT
     */
    private Long jobType;

    /**
     * 工程状态（完井/施工中） 0 1
     */
    private Integer jobStatus;

    /**
     * 总趟数
     * totalMwdRuns
     */
    private Integer totalRun;

    /**
     * 远端方位伽马趟数
     */
    private Integer azimuthGamma;

    /**
     * 自然伽马趟数
     */
    private Integer naturalGamma;

    /**
     * 近钻趟数
     */
    private Integer atBit;

    /**
     * 失效趟数
     * mwdFailures
     */
    private Integer failureRun;

    /**
     * 失效原因
     */
    private String failureReason;

    /**
     * 区块
     *  - from WellInfo
     *  blocks
     */
    private String blocks;

    /**
     * 井型 （对应字典id）
     *  - from WellInfo
     */
    private String wellType;

    /**
     * 层位
     */
    private String drillPos;
    /**
     * 岩性
     */
    private String rockCore;

    /**
     * 完钻井深
     */
    private Double finalDepth;

    /**
     * 施工井段
     */
    private String consDepthRange;

    /**
     * 总进尺
     */
    private Double totalFootage;

    /**
     * 泥浆类型 水基油基
     */
    private String mudType;

    /**
     * 最高温度
     */
    private Double maxTemp;

    /**
     * 最大钻压
     */
    private Double wob;

    /**
     * 最大扭矩
     */
    private Double maxTorque;

    /**
     * 备注
     */
    private String note;

    private List<JobFileRelation> fileList;

    /**
     * 创建时间
     */
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date createTime;

    private Long loadOutId;
    private Long deviceType;// 用来判断是旋导箱还是kit箱

}
