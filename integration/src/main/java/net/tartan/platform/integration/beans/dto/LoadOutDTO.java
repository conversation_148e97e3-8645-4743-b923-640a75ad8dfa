package net.tartan.platform.integration.beans.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 旋导出库记录DTO
 */
@Data
public class LoadOutDTO {
    /**
     * 出库记录ID
     */
    private Long loadOutId;
    
    /**
     * 设备ID
     */
    private Long deviceId;
    
    /**
     * 序列号
     */
    private String serialNumber;
    
    /**
     * 工单号
     */
    private String jobNumber;
    
    /**
     * 仪器状态
     */
    private Long status;
    
    /**
     * 出库日期
     */
    private String dateOut;
    
    /**
     * 入库日期
     */
    private String dateIn;
    
    /**
     * 位置
     */
    private String location;
    
    /**
     * 井号
     */
    private String wellNumber;
    
    /**
     * 入库检查人员
     */
    private String inCheckMember;
    
    /**
     * 出库检查人员
     */
    private String outCheckMember;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 出库记录详情列表
     */
    private List<LoadOutDetailDTO> items;
} 