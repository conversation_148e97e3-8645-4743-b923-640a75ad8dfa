package net.tartan.platform.integration.entity.erp;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 领/退料单单身/CHT/領//退料單單身/ENU/Material Requisition//Return Note Detail
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ISSUE_RECEIPT_D")
public class IssueReceiptD implements Serializable {

    private static final long serialVersionUID = 1L;

    private String docNo;

    @TableField("SequenceNumber")
    private Integer sequencenumber;

    @TableField("ISSUE_RECEIPT_ID")
    private String issueReceiptId;
    /**
     * 主键
     */
    @TableId("ISSUE_RECEIPT_D_ID")
    private String issueReceiptDId;
    /**
     * 批号
     */
    @TableField("ITEM_LOT_ID")
    private String itemLotId;
    private String serialNumber;
    /**
     * 材料品号
     */
    @TableField("ITEM_ID")
    private String itemId;
    private String invCode;

    /**
     * 材料特征码
     */
    @TableField("ITEM_FEATURE_ID")
    private String itemFeatureId;

    /**
     * 单位
     */
    @TableField("UNIT_ID")
    private String unitId;

    /**
     * 材料品名
     */
    @TableField("ITEM_DESCRIPTION")
    private String itemDescription;
    private String invName;

    /**
     * 材料规格
     */
    @TableField("ITEM_SPECIFICATION")
    private String itemSpecification;
    private String invStd;

    /**
     * 实际数量
     */
    @TableField("ACTUAL_ISSUE_RECEIPT_QTY")
    private Double actualIssueReceiptQty;

    /**
     * 领料说明
     */
    @TableField("ISSUE_COMMENT")
    private String issueComment;

    /**
     * 备注
     */
    @TableField("REMARK")
    private String remark;

//    /**
//     * 供料方式
//     */
//    @TableField("ITEM_TYPE")
//    private String itemType;
//
//    /**
//     * 领退料数量
//     */
//    @TableField("ISSUE_RECEIPT_QTY")
//    private Double issueReceiptQty;

//    /**
//     * 领料第二数量
//     */
//    @TableField("SECOND_QTY")
//    private Double secondQty;
//
//    /**
//     * 实际第二数量
//     */
//    @TableField("ACTUAL_SECOND_QTY")
//    private Double actualSecondQty;
//
//    /**
//     * 领料库存数量
//     */
//    @TableField("INVENTORY_QTY")
//    private Double inventoryQty;
//
//    /**
//     * 实际库存数量
//     */
//    @TableField("ACTUAL_INVENTORY_QTY")
//    private Double actualInventoryQty;
//
//    /**
//     * 大包装数量
//     */
//    @TableField("PACKING_QTY1")
//    private Double packingQty1;
//
//    /**
//     * 实际大包装数量
//     */
//    @TableField("ACTUAL_PACKING_QTY1")
//    private Double actualPackingQty1;
//
//    /**
//     * 中包装数量
//     */
//    @TableField("PACKING_QTY2")
//    private Double packingQty2;
//
//    /**
//     * 实际中包装数量
//     */
//    @TableField("ACTUAL_PACKING_QTY2")
//    private Double actualPackingQty2;
//
//    /**
//     * 小包装数量
//     */
//    @TableField("PACKING_QTY3")
//    private Double packingQty3;
//
//    /**
//     * 实际小包装数量
//     */
//    @TableField("ACTUAL_PACKING_QTY3")
//    private Double actualPackingQty3;
//
//    /**
//     * 最小包装数量
//     */
//    @TableField("PACKING_QTY4")
//    private Double packingQty4;
//
//    /**
//     * 实际最小包装数量
//     */
//    @TableField("ACTUAL_PACKING_QTY4")
//    private Double actualPackingQty4;
//
//    /**
//     * 包装数量描述
//     */
//    @TableField("PACKING_QTY")
//    private String packingQty;

//    /**
//     * 源工单
//     */
//    @TableField("SOURCE_MO_ID")
//    private String sourceMoId;
//
//    /**
//     * VMI结算码
//     */
//    @TableField("VMI_SETTLED")
//    private String vmiSettled;
//
//    /**
//     * 包装方式
//     */
//    @TableField("PACKING_MODE_ID")
//    private String packingModeId;
//
//    /**
//     * 工单单号
//     */
//    @TableField("MO_ID")
//    private String moId;
//
//    /**
//     * 大包装单位
//     */
//    @TableField("PACKING1_UNIT_ID")
//    private String packing1UnitId;
//    /**
//     * 中包装单位
//     */
//    @TableField("PACKING2_UNIT_ID")
//    private String packing2UnitId;
//
//    /**
//     * 小包装单位
//     */
//    @TableField("PACKING3_UNIT_ID")
//    private String packing3UnitId;
//
//    /**
//     * 最小包装单位
//     */
//    @TableField("PACKING4_UNIT_ID")
//    private String packing4UnitId;
//
//    /**
//     * 仓库
//     */
//    @TableField("WAREHOUSE_ID")
//    private String warehouseId;
//
//    /**
//     * 工艺
//     */
//    @TableField("OPERATION_ID")
//    private String operationId;
//
//    /**
//     * 库位
//     */
//    @TableField("BIN_ID")
//    private String binId;
//    /**
//     * 领料申请单
//     */
//    @TableField("ISSUE_RECEIPT_REQ_ID")
//    private String issueReceiptReqId;
//
//    /**
//     * 被取替代数量
//     */
//    @TableField("REPLACED_QTY")
//    private Double replacedQty;
//
//    /**
//     * 工单单身
//     */
//    @TableField("MO_D_ID")
//    private String moDId;
//
//    /**
//     * 类别
//     */
//    @TableField("SOURCE_TYPE")
//    private String sourceType;
//
//    /**
//     * 被取替代工单单身
//     */
//    @TableField("REPLACED_MO_D_ID")
//    private String replacedMoDId;
//
//    /**
//     * 检核码
//     */
//    @TableField("BC_CHECK_STATUS")
//    private String bcCheckStatus;
//
//    /**
//     * 项目
//     */
//    @TableField("PROJECT_ID")
//    private String projectId;
//
//    /**
//     * 序列号已采集数量
//     */
//    @TableField("SN_COLLECTED_QTY")
//    private Double snCollectedQty;
//
//    /**
//     * 序列号检核码
//     */
//    @TableField("SN_COLLECTED_STATUS")
//    private String snCollectedStatus;
//
//    @TableField("MO_ROUTING_D_ID")
//    private String moRoutingDId;
//
//    @TableField("WIP_REQUIRED_QTY")
//    private Double wipRequiredQty;
//
//    @TableField("WIP_WAREHOUSE_ID")
//    private String wipWarehouseId;
//
//    @TableField("GENERATE_SOURCE")
//    private String generateSource;
//
//    /**
//     * 单据状态属性
//     */
//    @TableField("ApproveStatus")
//    private String approvestatus;
//
//    /**
//     * 修改日期
//     */
//    @TableField("ApproveDate")
//    private LocalDateTime approvedate;
//
//    /**
//     * 修改人
//     */
//    @TableField("ApproveBy")
//    private String approveby;
//
//    /**
//     * 创建日期
//     */
//    @TableField("CreateDate")
//    private LocalDateTime createdate;
//
//    /**
//     * 最后修改日期
//     */
//    @TableField("LastModifiedDate")
//    private LocalDateTime lastmodifieddate;
//
//    /**
//     * 修改日期
//     */
//    @TableField("ModifiedDate")
//    private LocalDateTime modifieddate;
//
//    /**
//     * 创建者
//     */
//    @TableField("CreateBy")
//    private String createby;
//
//    /**
//     * 最后修改者
//     */
//    @TableField("LastModifiedBy")
//    private String lastmodifiedby;
//
//    /**
//     * 修改者
//     */
//    @TableField("ModifiedBy")
//    private String modifiedby;
//
//    /**
//     * 自定义字段0
//     */
//    @TableField("UDF001")
//    private Double udf001;
//
//    /**
//     * 自定义字段1
//     */
//    @TableField("UDF002")
//    private Double udf002;
//
//    /**
//     * 自定义字段2
//     */
//    @TableField("UDF003")
//    private Double udf003;
//
//    /**
//     * 自定义字段3
//     */
//    @TableField("UDF011")
//    private Double udf011;
//
//    /**
//     * 自定义字段4
//     */
//    @TableField("UDF012")
//    private Double udf012;
//
//    /**
//     * 自定义字段5
//     */
//    @TableField("UDF013")
//    private Double udf013;
//
//    /**
//     * 自定义字段6
//     */
//    @TableField("UDF021")
//    private String udf021;
//
//    /**
//     * 自定义字段7
//     */
//    @TableField("UDF022")
//    private String udf022;
//
//    /**
//     * 自定义字段8
//     */
//    @TableField("UDF023")
//    private String udf023;
//
//    /**
//     * 自定义字段9
//     */
//    @TableField("UDF024")
//    private String udf024;
//
//    /**
//     * 自定义字段10
//     */
//    @TableField("UDF025")
//    private String udf025;
//
//    /**
//     * 自定义字段11
//     */
//    @TableField("UDF026")
//    private String udf026;
//
//    /**
//     * 自定义字段12
//     */
//    @TableField("UDF041")
//    private LocalDateTime udf041;
//
//    /**
//     * 自定义字段13
//     */
//    @TableField("UDF042")
//    private LocalDateTime udf042;
//
//    /**
//     * 自定义字段14
//     */
//    @TableField("UDF051")
//    private String udf051;
//
//    /**
//     * 自定义字段15
//     */
//    @TableField("UDF052")
//    private String udf052;
//
//    /**
//     * 自定义字段16
//     */
//    @TableField("UDF053")
//    private String udf053;
//
//    /**
//     * 自定义字段17
//     */
//    @TableField("UDF054")
//    private String udf054;
//
//    /**
//     * 版本号，不要随意更改
//     */
//    @TableField("Version")
//    private LocalDateTime version;
//
//    @TableField("COST_DOMAIN_ID_RTK")
//    private String costDomainIdRtk;
//
//    @TableField("COST_DOMAIN_ID_ROid")
//    private String costDomainIdRoid;
//
//    @TableField("ISSUE_DESTINATION_RTK")
//    private String issueDestinationRtk;
//
//    @TableField("ISSUE_DESTINATION_ROid")
//    private String issueDestinationRoid;

}
