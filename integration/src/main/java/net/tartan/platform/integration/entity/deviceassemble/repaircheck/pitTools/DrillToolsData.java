package net.tartan.platform.integration.entity.deviceassemble.repaircheck.pitTools;

import lombok.Data;
import lombok.Value;
import net.tartan.platform.integration.entity.deviceassemble.repaircheck.BaseCheckData;
import net.tartan.platform.integration.entity.deviceassemble.repaircheck.FaultDiagnosis;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *  近钻 维修检查配件表
 *  Drill
 */
@Data
public class DrillToolsData extends BaseCheckData {

    private String name;
    //工序内容List 
    private List<Process> processList;
    //《标识、包装标准》
    private List<Map<String, Object>> standerList;


    @Data
    static class Process{
        private String processName;
        private ToolsCommonData toolsCommonData;
    }

    public DrillToolsData init(){
        DrillToolsData drillToolsData = new DrillToolsData();
        //工序内容
        String[] str1 ={
                "清洗所有零配件，保证无铁屑、杂质等异物",
                "将AT BIT BODY固定在卸扣机上，测量螺纹绝缘层电阻为___",
                "把上接头拧入本体上。（以链钳稍稍用力即可）测量绝缘值电阻为___",
                "测量外环尺寸",
                "把上接头拆下测量母扣尺寸，计算内环尺寸",
                "把尺寸交研发部出图纸，等待绝缘内外环加工",
                "在AT BIT BODY内孔装入1个___O圈并涂抹润滑脂，装入内绝缘环",
                "在AT BIT BODY外螺纹根部端面处装入1个___O圈并涂抹润滑脂",
                "在TOB SUB内孔里装入1个___O圈，在母扣端面装入1个___O圈，并在O圈和其装配位置涂抹润滑脂",
                "在AT BIT BODY有绝缘层的一端螺纹上涂抹___螺纹胶",
                "将外绝缘环套在AT BIT BODY有绝缘层一端的外螺纹上",
                "将TOP SUB内孔有O圈的一端内螺纹对准AT BIT BODY有绝缘层一端的外螺纹进行螺纹连接，且上扣扭矩至___",
                "测量AT BIT TOP SUB和AT BIT BODY之间电阻为____",
                "测量外绝缘环两边间隙为___"
        };
        String[] commonDevice = {"气枪","万用表","链钳、万用表","卡尺","卡尺、深度尺","/","O圈挑钩 、润滑脂","O圈挑钩、润滑脂","O圈挑钩、润滑脂","LT680","/","链钳、拆装架","万用表","塞尺"};
        List<Process> processList = new ArrayList<>();
        for(int a = 0; a<str1.length;a++){
            Process process = new Process();
            process.setProcessName(str1[a]);
            ToolsCommonData toolsCommonData = new ToolsCommonData();
            toolsCommonData.setDevice(commonDevice[a]);
            process.setToolsCommonData(toolsCommonData);
            processList.add(process);
        }
        drillToolsData.setProcessList(processList);

        //标准
//        List<List<Map<String, Object>>> standerList = new ArrayList<>();

        List<Map<String, Object>> stander1 = new ArrayList<>();
        Map<String, Object> value1 = new HashMap<>();
        value1.put("key","打磨掉夹痕");
        value1.put("value",false);
        stander1.add(value1);

//        List<Map<String, Object>> stander2 = new ArrayList<>();
        Map<String, Object> value2 = new HashMap<>();
        value2.put("key","打型号、SN号");
        value2.put("value",false);
//        stander2.add(value2);
        stander1.add(value2);

//        List<Map<String, Object>> stander3 = new ArrayList<>();
        Map<String, Object> value3 = new HashMap<>();
        value3.put("key","喷漆、包装");
        value3.put("value",false);
//        stander3.add(value3);
        stander1.add(value3);

//        standerList.add(stander1);
//        standerList.add(stander2);
//        standerList.add(stander3);

//        drillToolsData.setStanderList(standerList);
        drillToolsData.setStanderList(stander1);

//        CommonEntity commonEntity = new CommonEntity();
//        List<CommonEntity> result = new ArrayList<>();
//        commonEntity.setKey("打磨掉夹痕");
//        commonEntity.setValue(false);
//        commonEntity.setKey("打型号、SN号");
//        commonEntity.setValue(false);
//        commonEntity.setKey("喷漆、包装");
//        commonEntity.setValue(false);
//        drillToolsData.setStanderList(result);

//        stander.put("打磨掉夹痕",false);
//        stander.put("打型号、SN号",false);
//        stander.put("喷漆、包装",false);
//        result.add(stander);

        //初始化故障诊断
        drillToolsData.setFaultDiagnosis(new FaultDiagnosis().init());
        return drillToolsData;
    }


}
