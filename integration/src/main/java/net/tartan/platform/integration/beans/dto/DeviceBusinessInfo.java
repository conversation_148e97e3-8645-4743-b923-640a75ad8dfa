package net.tartan.platform.integration.beans.dto;

import lombok.Data;
import net.tartan.platform.common.enums.EnumCirculateType;

@Data
public class DeviceBusinessInfo {

    /**
     * 流转类型
     */
    private EnumCirculateType circulateType;
    /**
     * 业务号
     * 根据流转类型 可以表示不同业务的编号
     */
    private String businessNumber;
    /**
     * 业务id
     * 根据流转类型 可以表示不同业务的id
     */
    private Long businessId;
}
