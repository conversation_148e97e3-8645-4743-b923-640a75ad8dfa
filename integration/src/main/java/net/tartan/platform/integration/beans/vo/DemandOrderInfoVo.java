package net.tartan.platform.integration.beans.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import net.tartan.platform.common.enums.EnumDemandReceiveStatus;
import net.tartan.platform.common.enums.EnumDemandType;
import net.tartan.platform.integration.entity.demandOrder.DemandOrderInfoDetail;

import java.util.Date;
import java.util.List;

/**
 * 需求单
 */
@Data
public class DemandOrderInfoVo {
    private Long demandId;

    /**
     * 需求单号（由系统生成）
     */
    private String demandNumber;

    private String kitNumber;

    /**
     * 作业号，工单号
     *  - from JobInfo
     */
//    private Long jobId;
    private String jobNumber;

    /**
     * 井号
     *  - from JobInfo
     */
//    private String wellId;
    private String wellNumber;

    /**
     * 需求日期
     */
    private String demandDate;

    /**
     * 目的地
     */
    private String destination;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建人用户id
     */
//    private Long createUserId;

    /**
     * 创建时间
     */
    private String demandCreateTime;

    /**
     * 接收状态
     */
    private EnumDemandReceiveStatus receiveStatus;

    /**
     * 接收人
     */
    private String receiveBy;

    /**
     * 接收人id
     */
//    private String receiveUserId;

    /**
     * 接收时间
     */
    private String receiveTime;

    /**
     * 需求单类型
     */
    private EnumDemandType demandType;

    private List<DemandOrderInfoDetail> originInfoDetailList;

    private List<DemandOrderInfoDetail> actualInfoDetailList;

    /**
     * 现场联系人
     */
    private String contactUser;

    /**
     * 现场联系人的联系方式
     */
    private String contactNumber;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    private Boolean isFromOa;

}
