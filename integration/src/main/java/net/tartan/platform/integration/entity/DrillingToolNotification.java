package net.tartan.platform.integration.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 钻具通知单主表实体类
 * 使用MongoDB文档存储，支持复杂的嵌套数据结构
 * 
 * <AUTHOR>
 * @since 2025-07-15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "drilling_tool_notifications")
public class DrillingToolNotification {

    /**
     * MongoDB主键ID
     */
    @Id
    private String id;

    /**
     * 单据编号
     */
    private String no;

    /**
     * 工单号
     */
    private String jobNumber;

    /**
     * 发起人编码
     */
    private String initiatorCode;

    /**
     * 发起人姓名
     */
    private String initiatorName;

    /**
     * 发起人所在部门
     */
    private String initiatorDept;

    /**
     * 来往类型
     */
    private String transactionType;

    /**
     * 来往类型备注
     */
    private String transactionTypeRemark;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 井名
     */
    private String wellName;

    /**
     * 日期
     */
    private Date notificationDate;

    /**
     * Kit号
     */
    private String kitNo;

    /**
     * 现场联系人编码
     */
    private String siteContactCode;

    /**
     * 现场联系人姓名
     */
    private String siteContactName;

    /**
     * 流程处理意见
     */
    private String processOpinion;

    /**
     * 钻具明细列表
     */
    private List<DrillingToolDetail> drillingTools;

    /**
     * 主表扩展数据（用于存储其他非核心字段）
     */
    private Map<String, Object> mainData;

    /**
     * OA同步状态
     * pending - 待同步
     * syncing - 同步中
     * success - 同步成功
     * failed - 同步失败
     */
    private String oaSyncStatus;

    /**
     * OA同步时间
     */
    private Date oaSyncTime;

    /**
     * OA同步错误信息
     */
    private String oaSyncError;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新时间
     */
    private Date updatedTime;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 状态
     * active - 活跃
     * deleted - 已删除
     */
    private String status;

    /**
     * 钻具明细内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DrillingToolDetail {
        /**
         * 序号
         */
        private Integer sequenceNumber;

        /**
         * 名称
         */
        private String name;

        /**
         * 序列号
         */
        private String serialNumber;

        private DrillingToolRepairInfo repairInfo;

        /**
         * 技术规格参数
         */
        private TechnicalSpecs specifications;

        /**
         * 技术参数
         */
        private TechnicalParams technicalParams;

        /**
         * 状态信息
         */
        private StatusInfo statusInfo;
    }

    /**
     * 技术规格参数
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TechnicalSpecs {
        /**
         * 循环温度
         */
        private Double circulationTemperature;

        /**
         * 最高温度
         */
        private Double maxTemperature;

        /**
         * 入井时间
         */
        private Double downholeTime;

        /**
         * 循环时间
         */
        private Double circulationTime;

        /**
         * 耐温
         */
        private Double temperatureResistance;

        /**
         * 弯度
         */
        private Double bendDegree;

        /**
         * 最大外径mm
         */
        private Double maxOuterDiameter;
    }

    /**
     * 技术参数
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TechnicalParams {
        /**
         * 泥浆类型
         */
        private String mudType;

        /**
         * 扣型
         */
        private String threadType;

        /**
         * 扶正器尺寸-类型
         */
        private String centralizerSizeType;
    }

    /**
     * 状态信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StatusInfo {
        /**
         * 数量
         */
        private String quantity;

        /**
         * 返回原因
         */
        private String returnReason;

        /**
         * 是否存在故障及故障描述
         */
        private String faultDescription;

        /**
         * 备注
         */
        private String remark;
    }
    /**
     * 维修信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DrillingToolRepairInfo {

        //是否振动超标 是/否
        private String vibrationOos;

        //是否存在故障 是/否
        private String failureStatus;

        //故障描述
        private String failureDescription;

        //趟次
        private String run;
    }
}
