package net.tartan.platform.integration.beans.vo;

import lombok.Data;
import net.tartan.platform.common.enums.EnumCirculateType;
import net.tartan.platform.integration.entity.DeviceServiceHistory;

import java.math.BigDecimal;
import java.util.List;

/**
 * 仪器服役历史表
 * 第一层信息
 */
@Data
public class DeviceServiceHistoryVo {
    private Long deviceId;
    private Long deviceType;
//    private String deviceTypeStr;

    private String invName;
    private String serialNumber;

    /**
     * 总服役次数
     * 计算at rig状态的数量 19001
     */
    private Integer serveTotalCount;

    /**
     * 总入井时间
     */
    private BigDecimal totalInWellHrs;

    /**
     * 最高温度
     */
    private BigDecimal maxBht;

    /**
     * 总循环时间
     */
    private BigDecimal totalCirculateHrs;


    private List<DeviceServiceHistoryDetailVo> serviceHistoryList;

}
