package net.tartan.platform.integration.beans.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.enums.EnumReportType;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReportDto {
    /**
     * 钻头厂家
     */
    private String drillMan;
    /**
     * 螺杆厂家
     */
    private String screwMan;
    /**
     * 震击器厂家
     */
    private String knMan;
    /**
     * 水力厂家
     */
    private String wpMan;

    private String date;
    /**
     * 开始日期
     */
    private String startDate;
    /**
     * 结束日期
     */
    private String endDate;

    private Long jobId;

    private Integer run;

    private long reportId;

    private EnumReportType reportType;
}
