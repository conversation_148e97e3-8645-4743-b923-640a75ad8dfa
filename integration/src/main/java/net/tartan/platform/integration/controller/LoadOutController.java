package net.tartan.platform.integration.controller;

import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.integration.beans.dto.LoadOutDTO;
import net.tartan.platform.integration.beans.dto.LoadOutDetailDTO;
import net.tartan.platform.integration.beans.dto.LoadOutCreateDTO;
import net.tartan.platform.integration.beans.dto.TemplateDTO;
import net.tartan.platform.integration.beans.query.LoadOutQuery;
import net.tartan.platform.integration.service.LoadOutService;
import net.tartan.platform.integration.service.TemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 旋导出入库
 * 用于处理旋导模板展示和出库记录创建
 */
@RestController
@RequestMapping("/loadout")
public class LoadOutController {

    @Autowired
    private TemplateService templateService;
    
    @Autowired
    private LoadOutService loadOutService;
    
    /**
     * 获取旋导出库模板
     * @return 旋导出库模板及其项目（按类别分组）
     */
    @GetMapping("/getLucidaTemplate")
    public CommonResult<TemplateDTO> getLoadOutTemplate() {
        // 获取旋导模板（ID为1）
        TemplateDTO template = templateService.getFormattedTemplateById(1L);
        
        if (template == null) {
            return CommonResult.failed("未找到旋导模板");
        }
        
        return CommonResult.success(template);
    }
    
    /**
     * 创建旋导出入库记录
     * @param createDTO 创建请求信息
     * @return 创建结果
     */
    @PostMapping("/createLucida")
    public CommonResult<Long> createLoadOut(@RequestBody LoadOutCreateDTO createDTO) {
        // 验证数据
        if (createDTO == null || createDTO.getSerialNumber() == null || createDTO.getSerialNumber().isEmpty()) {
            return CommonResult.validateFailed("设备序列号不能为空");
        }
        
        // 使用序列号初始化出库记录（包含模板生成）
        Long loadOutId = loadOutService.initLoadOutRecord(createDTO.getSerialNumber());
        return CommonResult.success(loadOutId);
    }
    
    /**
     * 获取旋导出库记录详情
     * @param loadOutId 出库记录ID
     * @return 出库记录及其详情
     */
    @GetMapping("/getLucidaDetail")
    public CommonResult<LoadOutDTO> getLoadOutDetail(@RequestParam Long loadOutId) {
        LoadOutDTO loadOutDTO = loadOutService.getLoadOutById(loadOutId);
        if (loadOutDTO == null) {
            return CommonResult.failed("出库记录不存在");
        }
        return CommonResult.success(loadOutDTO);
    }
    
    /**
     * 更新旋导出库记录
     * @param loadOutDTO 出库记录信息
     * @return 更新结果
     */
    @PostMapping("/updateLucida")
    public CommonResult<Boolean> updateLoadOut(@RequestBody LoadOutDTO loadOutDTO) {
        if (loadOutDTO.getLoadOutId() == null) {
            return CommonResult.validateFailed("出库记录ID不能为空");
        }
        boolean success = loadOutService.updateLoadOut(loadOutDTO);
        return success ? CommonResult.success(true) : CommonResult.failed("更新出库记录失败");
    }
    
    /**
     * 获取旋导出库记录列表
     * @param serialNumber 序列号（可选）
     * @param jobNumber 任务编号（可选）
     * @param dateInStart 入库开始日期（可选）
     * @param dateInEnd 入库结束日期（可选）
     * @param dateOutStart 出库开始日期（可选）
     * @param dateOutEnd 出库结束日期（可选）
     * @param page 页码
     * @param size 每页大小
     * @return 出库记录列表
     */
    @GetMapping("/getLucidaList")
    public CommonResult<List<LoadOutDTO>> getLoadOutList(
            @RequestParam(required = false) String serialNumber,
            @RequestParam(required = false) String jobNumber,
            @RequestParam(required = false) String dateInStart,
            @RequestParam(required = false) String dateInEnd,
            @RequestParam(required = false) String dateOutStart,
            @RequestParam(required = false) String dateOutEnd,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        List<LoadOutDTO> loadOutList = loadOutService.getLoadOutList(serialNumber, jobNumber,
                dateInStart, dateInEnd, dateOutStart, dateOutEnd, page, size);
        return CommonResult.success(loadOutList);
    }
    
    /**
     * 添加旋导出库记录详情项
     * @param loadOutId 出库记录ID
     * @param detailDTO 详情项信息
     * @return 创建结果
     */
    @PostMapping("/{loadOutId}/addLucidaDetail")
    public CommonResult<Long> addLoadOutDetail(@PathVariable Long loadOutId, @RequestBody LoadOutDetailDTO detailDTO) {
        detailDTO.setLoadOutId(loadOutId);
        Long detailId = loadOutService.addLoadOutDetail(detailDTO);
        return CommonResult.success(detailId);
    }
    
    /**
     * 批量添加旋导出库记录详情项
     * @param loadOutId 出库记录ID
     * @param detailDTOList 详情项信息列表
     * @return 创建结果
     */
    @PostMapping("/{loadOutId}/batchAddLucidaDetail")
    public CommonResult<List<Long>> batchAddLoadOutDetail(@PathVariable Long loadOutId, @RequestBody List<LoadOutDetailDTO> detailDTOList) {
        // 设置所有详情项的loadOutId
        detailDTOList.forEach(detailDTO -> detailDTO.setLoadOutId(loadOutId));
        List<Long> detailIds = loadOutService.batchAddLoadOutDetail(detailDTOList);
        return CommonResult.success(detailIds);
    }
    
    /**
     * 删除旋导出库记录详情项
     * @param detailId 详情项ID
     * @return 删除结果
     */
    @DeleteMapping("/deleteLucidaDetail/{detailId}")
    public CommonResult<Boolean> deleteLoadOutDetail(@PathVariable Long detailId) {
        boolean success = loadOutService.deleteLoadOutDetail(detailId);
        return success ? CommonResult.success(true) : CommonResult.failed("删除详情项失败");
    }

     @PostMapping("/exportLucidaExcel")
     public CommonResult exportDetail(@RequestBody LoadOutQuery loadOutQuery) {
         loadOutService.exportLucidaExcel(loadOutQuery);
         return CommonResult.success();
     }

}
