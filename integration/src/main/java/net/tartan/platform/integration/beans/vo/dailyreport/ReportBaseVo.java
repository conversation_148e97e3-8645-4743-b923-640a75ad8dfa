package net.tartan.platform.integration.beans.vo.dailyreport;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.enums.EnumReportType;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ReportBaseVo {
    private long id;

    private EnumReportType reportType;

    private String date;

    private String run;

    private String surveyNumber;

    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 姓名
     */
    private String name;

}
