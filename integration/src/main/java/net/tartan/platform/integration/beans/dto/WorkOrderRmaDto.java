package net.tartan.platform.integration.beans.dto;

import lombok.Data;
import net.tartan.platform.integration.entity.RmaCheckData;
import net.tartan.platform.integration.entity.WorkOrderRmaDetail;
import net.tartan.platform.integration.entity.WorkOrderRmaRepairMember;

import java.util.List;


/**
 * <p>
 * RMA工单信息表
 * </p>
 */
@Data
public class WorkOrderRmaDto {

    private Long rmaId;

    /**
     * 与mwd关联的Id
     */
    private Long mwdId;

    /**
     * 工单单号
     */
    private String rmaNumber;

    /**
     * 作业编号
     */
    private String jobNumber;

    /**
     * 井号
     */
    private String wellNumber;

//    /**
//     * kit箱编号
//     */
//    private String kitNumber;

    /**
     * 开始日期
     */
    private String startDate;

    /**
     * 完成日期
     */
    private String endDate;

    /**
     * 维修状态1：已完成，0：未完成，-1：滞留
     */
    private Integer finish;
    /**
     * 备注
     */
    private String notes;

    private WorkOrderRmaDetail workOrderRmaDetail;

    private RmaCheckData rmaCheckData;

    private List<WorkOrderRmaRepairMember> repairMemberList;
}
