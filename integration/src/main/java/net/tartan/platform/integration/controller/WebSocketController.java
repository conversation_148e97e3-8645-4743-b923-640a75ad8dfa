package net.tartan.platform.integration.controller;
//
//import net.tartan.platform.integration.handler.TartanWebSocketHandler;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.*;
//
//@RestController
//@RequestMapping("/websocket")
public class WebSocketController {
//
//    private final TartanWebSocketHandler webSocketEndpoint;
//
//    @Autowired
//    public WebSocketController(TartanWebSocketHandler webSocketEndpoint) {
//        this.webSocketEndpoint = webSocketEndpoint;
//    }
//
//    @PostMapping("/start")
//    public String startWebSocket(@RequestParam int port) {
//        try {
//            // 此处调用 MyWebSocketEndpoint 的 onOpen 方法，启动 WebSocket 监听
////            webSocketEndpoint.onOpen(null, port);
//            return "已启动 WebSocket 监听，端口：" + port;
//        } catch (Exception e) {
//            e.printStackTrace();
//            return "启动 WebSocket 监听失败：" + e.getMessage();
//        }
//    }
//
//    @PostMapping("/stop")
//    public String stopWebSocket(@RequestParam int port) {
//        try {
//            // 此处调用 MyWebSocketEndpoint 的 onClose 方法，关闭 WebSocket 监听
////            webSocketEndpoint.onClose(null, null);
//            return "已停止 WebSocket 监听，端口：" + port;
//        } catch (Exception e) {
//            e.printStackTrace();
//            return "停止 WebSocket 监听失败：" + e.getMessage();
//        }
//    }
}

