package net.tartan.platform.integration.beans.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.enums.EnumTransferReceiveStatus;
import net.tartan.platform.integration.entity.TransferOrderDetail;

import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TransferOrderDto {

    private Long transferId;

    //调拨单号
    private String transferNumber;

    //发出井号
    private String fromWellNumber;
    //发往井号
    private String toWellNumber;

    //发出kit号
    private String fromKitNumber;
    //发往kit号
    private String toKitNumber;

    //创建人
    private String createBy;
    //发出时间
    private String sendDate;

    //接收状态
    private EnumTransferReceiveStatus receiveStatus;

    //接收人
    private String receiveBy;
    //接收时间
    private String receiveDate;

    //现场联系人
    private String contactUser;
    //现场联系人的联系方式
    private String contactNumber;

    private List<TransferOrderDetail> detailList;

    /**
     * 创建时间
     */
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date updateTime;
}
