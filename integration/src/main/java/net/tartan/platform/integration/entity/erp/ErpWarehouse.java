package net.tartan.platform.integration.entity.erp;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 仓库信息/CHT/倉庫資料/ENU/Warehouse Data
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("WAREHOUSE")
public class ErpWarehouse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 关联部门
     */
    @TableField("Owner_Dept")
    private String ownerDept;

    /**
     * 关联员工
     */
    @TableField("Owner_Emp")
    private String ownerEmp;

    /**
     * 主键 
     */
    @TableId("WAREHOUSE_ID")
    private String warehouseId;

    /**
     * 仓库编号
     */
    @TableField("WAREHOUSE_CODE")
    private String warehouseCode;

    /**
     * 仓库名称
     */
    @TableField("WAREHOUSE_NAME")
    private String warehouseName;

    /**
     * 仓库属性
     */
    @TableField("WAREHOUSE_CHARACTER")
    private String warehouseCharacter;

    /**
     * 仓库性质
     */
    @TableField("WAREHOUSE_PROPERTY")
    private String warehouseProperty;

    /**
     * 库位管理
     */
    @TableField("BIN_CONTROL")
    private String binControl;

    /**
     * 纳入可用量计算
     */
    @TableField("INCLUDED_AVAILABLE_QTY")
    private Boolean includedAvailableQty;

    /**
     * 库存量不足准许出库
     */
    @TableField("NEGATIVE_INVENTORY_ALLOWED")
    private Boolean negativeInventoryAllowed;

    /**
     * 备注
     */
    @TableField("REMARK")
    private String remark;

    @TableField("ADDRESS")
    private String address;

    /**
     * 电话
     */
    @TableField("TELEPHONE")
    private String telephone;

    /**
     * 传真
     */
    @TableField("FAX")
    private String fax;

    /**
     * 储存限制编号
     */
    @TableField("STORAGE_LIMIT_ID")
    private String storageLimitId;

    /**
     * 主键
     */
    @TableField("CURRENCY_ID")
    private String currencyId;

    /**
     * 成本域
     */
    @TableField("COST_DOMAIN_ID")
    private String costDomainId;

    /**
     * 所属组织类型
     */
    @TableField("ORG_TYPE")
    private String orgType;

    /**
     * 销售仓库
     */
    @TableField("WAREHOURSE_SALE")
    private Boolean warehourseSale;

    /**
     * 负库存是否纳入计划
     */
    @TableField("NEGATIVE_INV_INCLUDED")
    private Boolean negativeInvIncluded;

//    /**
//     * 自定义字段0
//     */
//    @TableField("UDF001")
//    private Double udf001;
//
//    /**
//     * 自定义字段1
//     */
//    @TableField("UDF002")
//    private Double udf002;
//
//    /**
//     * 自定义字段2
//     */
//    @TableField("UDF003")
//    private Double udf003;
//
//    /**
//     * 自定义字段3
//     */
//    @TableField("UDF011")
//    private Double udf011;
//
//    /**
//     * 自定义字段4
//     */
//    @TableField("UDF012")
//    private Double udf012;
//
//    /**
//     * 自定义字段5
//     */
//    @TableField("UDF013")
//    private Double udf013;
//
//    /**
//     * 自定义字段6
//     */
//    @TableField("UDF021")
//    private String udf021;
//
//    /**
//     * 自定义字段7
//     */
//    @TableField("UDF022")
//    private String udf022;
//
//    /**
//     * 自定义字段8
//     */
//    @TableField("UDF023")
//    private String udf023;
//
//    /**
//     * 自定义字段9
//     */
//    @TableField("UDF024")
//    private String udf024;
//
//    /**
//     * 自定义字段10
//     */
//    @TableField("UDF025")
//    private String udf025;
//
//    /**
//     * 自定义字段11
//     */
//    @TableField("UDF026")
//    private String udf026;
//
//    /**
//     * 自定义字段12
//     */
//    @TableField("UDF041")
//    private LocalDateTime udf041;
//
//    /**
//     * 自定义字段13
//     */
//    @TableField("UDF042")
//    private LocalDateTime udf042;
//
//    /**
//     * 自定义字段14
//     */
//    @TableField("UDF051")
//    private String udf051;
//
//    /**
//     * 自定义字段15
//     */
//    @TableField("UDF052")
//    private String udf052;
//
//    /**
//     * 自定义字段16
//     */
//    @TableField("UDF053")
//    private String udf053;
//
//    /**
//     * 自定义字段17
//     */
//    @TableField("UDF054")
//    private String udf054;

    /**
     * 版本号，不要随意更改
     */
    @TableField("Version")
    private byte[] version;

    /**
     * 单据状态属性
     */
    @TableField("ApproveStatus")
    private String approvestatus;

    /**
     * 修改日期
     */
    @TableField("ApproveDate")
    private Date approvedate;

    /**
     * 修改人
     */
    @TableField("ApproveBy")
    private String approveby;

    /**
     * 创建日期
     */
    @TableField("CreateDate")
    private Date createdate;

    /**
     * 最后修改日期
     */
    @TableField("LastModifiedDate")
    private Date lastmodifieddate;

    /**
     * 修改日期
     */
    @TableField("ModifiedDate")
    private Date modifieddate;

    /**
     * 创建者
     */
    @TableField("CreateBy")
    private String createby;

    /**
     * 最后修改者
     */
    @TableField("LastModifiedBy")
    private String lastmodifiedby;

    /**
     * 修改者
     */
    @TableField("ModifiedBy")
    private String modifiedby;

    /**
     * 附件
     */
    @TableField("Attachments")
    private String attachments;

    /**
     * 表单所在的流程实例的编号
     */
    @TableField("ProcessInstanceId")
    private String processinstanceid;

    @TableField("Owner_Org_RTK")
    private String ownerOrgRtk;

    @TableField("Owner_Org_ROid")
    private String ownerOrgRoid;


}
