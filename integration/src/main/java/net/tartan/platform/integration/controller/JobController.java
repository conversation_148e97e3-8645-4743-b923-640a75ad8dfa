package net.tartan.platform.integration.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.integration.beans.query.JobInfoQuery;
import net.tartan.platform.integration.beans.request.AddJobParams;
import net.tartan.platform.integration.beans.vo.ClientWellVo;
import net.tartan.platform.integration.beans.vo.JobInfoDetailVo;
import net.tartan.platform.integration.beans.vo.JobInfoVo;
import net.tartan.platform.integration.beans.vo.JobMapInfoVo;
import net.tartan.platform.integration.entity.JobInfo;
import net.tartan.platform.integration.service.IJobInfoService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * job 作业接口
 */
@RestController
@Slf4j
@RequestMapping("job")
public class JobController {

    @Autowired
    private IJobInfoService jobInfoService;
//    @Autowired
//    private JobUserRelationService reportWellUserRelationService;

//    //新版暂时不用了
//    /**
//     * 创建作业
//     */
//    @PostMapping("add")
//    public CommonResult add(@Validated @RequestBody AddJobParams request) {
//        JobInfo jobInfo = jobInfoService.add(request);
//        return CommonResult.success(jobInfo);
//    }

//    /**
//     * 更改作业
//     */
//    @PostMapping("update")
//    public CommonResult update(@Validated @RequestBody UpdateJobParams params) {
//        reportWellService.update(params);
//        return CommonResult.success();
//    }

    /**
     * 获取作业信息
     *
     * @param jobId 作业id
     * @return
     */
    @GetMapping("info")
    public CommonResult getInfo(@RequestParam("jobId") long jobId) {
        JobInfoVo info = jobInfoService.getJobInfo(jobId);
        return CommonResult.success(info);
    }

//    //新版暂时不用了
//    /**
//     * 查看所有作业
//     */
//    @GetMapping("list")
//    public CommonResult getInfoList() {
//        List<JobInfo> list = jobInfoService.list();
//        return CommonResult.success(list);
//    }

    @PostMapping("list/{current}/{size}")
    public CommonResult getInfoList(@PathVariable long current,
            @PathVariable long size,
            @RequestBody(required = false) JobInfoVo jobInfoVo){
        IPage<JobInfoVo> page = new Page<>(current, size);
        jobInfoService.getInfoList(page,jobInfoVo);
        return CommonResult.success(page);
    }

    /**
     * 根据井id查询正在执行的作业
     */
    @GetMapping("listByWell")
    public CommonResult listByWell(@RequestParam("wellId") String wellId, @RequestParam(required = false, value = "jobStatus") Integer jobStatus) {
        List<JobInfo> list = jobInfoService.listByWell(wellId, jobStatus);
        return CommonResult.success(list);
    }

    /**
     * 根据井号well_number查询作业
     */
    @GetMapping("listByWellNumber")
    public CommonResult listByWellNumber(@RequestParam("wellNumber") String wellNumber,
                                         @RequestParam(required = false, value = "jobStatus") Integer jobStatus) {
        List<JobInfo> list = jobInfoService.listByWellNumber(wellNumber, jobStatus);
        return CommonResult.success(list);
    }

    /**
     * 查所有作业
     */
    @GetMapping("listAllWellAndJobs")
    public CommonResult listAllWellAndJobs(@RequestParam(required = false, value = "jobStatus") Integer jobStatus,
                                           @RequestParam(required = false, value = "wellNumberKey") String wellNumberKey) {
        if (StringUtils.isNotEmpty(wellNumberKey)) {
            wellNumberKey = wellNumberKey.toLowerCase();
        }
        List<ClientWellVo> list = jobInfoService.listAllWellAndJobs(jobStatus, wellNumberKey);
        return CommonResult.success(list);
    }

    /**
     * 查有作业的井
     */
    @GetMapping("listWellWithJobs")
    public CommonResult listWellWithJobs(@RequestParam(required = false, value = "wellNumberKey") String wellNumberKey) {
        if (StringUtils.isNotEmpty(wellNumberKey)) {
            wellNumberKey = wellNumberKey.toLowerCase();
        }
        List<ClientWellVo> list = jobInfoService.listWellWithJobs(wellNumberKey);
        return CommonResult.success(list);
    }

    /**
     * 查有作业的井
     */
    @PostMapping("listWellWithJobs_v2/{current}/{size}")
    public CommonResult listWellWithJobs_v2(
            @PathVariable long current,
            @PathVariable long size,
            @RequestBody(required = false) JobInfoQuery query) {
        if (ObjectUtils.isEmpty(query)) {
            query = new JobInfoQuery();
        }
        IPage<JobInfoDetailVo> page = new Page<>(current, size);
        return CommonResult.success(jobInfoService.listWellWithJobs_v2(page, query));
    }

    /**
     * 查询所有井和作业的地图信息
     */
    @GetMapping("/listJobMapInfo")
    public CommonResult<List<JobMapInfoVo>> listJobMapInfo() {
        List<JobMapInfoVo> list = jobInfoService.listJobMapInfo();
        return CommonResult.success(list);
    }

    /**
     * 删除作业
     * @param id 作业id
     * @return
     */
    @RequestMapping("delete")
    public CommonResult delete(@RequestParam("jobId") long id) {
        jobInfoService.delete(id);
        return CommonResult.success();
    }

    /**
     * 更新作业施工状态
     * @param id 作业id
     * @return
     */
    @RequestMapping("update/status")
    public CommonResult updateJobStatusByJobId(@Validated @RequestParam("jobId") long id) {

        jobInfoService.updateJobStatusByJobId(id);
        return CommonResult.success();
    }

    /**
     * 更改作业状态
     */
    @PostMapping("updateJobStatus")
    public CommonResult updateJobStatus(@RequestBody AddJobParams request) {
        jobInfoService.updateJobStatus(request.getJobId(), request.getJobStatus());
        return CommonResult.success();
    }

//    //新版暂时不用了
//    /**
//     * 添加作业可操作人员
//     */
//    @PostMapping("operator/add")
//    public CommonResult addOperator(@Validated @RequestBody AddReportOperatorParams request) {
//        reportWellUserRelationService.addOperator(request);
//        return CommonResult.success();
//    }
//
//    //新版暂时不用了
//    /**
//     * 批量添加
//     * @param request
//     * @return
//     */
//    @PostMapping("operator/batchAdd")
//    public CommonResult batchAddOperator(@Validated @RequestBody BatchReportOperatorParams request) {
//        reportWellUserRelationService.batchAddOperator(request);
//        return CommonResult.success();
//    }
//
//    //新版暂时不用了
//    /**
//     * 根据作业称查找可操作人员
//     */
//    @GetMapping("operator/list")
//    public CommonResult listUsersByJob(@RequestParam("jobId") long jobId) {
//        List<ReportUserVo> userList = reportWellUserRelationService.listUsersByJob(jobId);
//        return CommonResult.success(userList);
//    }
//
//    //新版暂时不用了
//    /**
//     * 查找用户可操作作业
//     */
//    @GetMapping("listByUser")
//    public CommonResult listJobsByUser(@RequestParam("userId") long userId) {
//        List<JobInfoVo> wellList = reportWellUserRelationService.listJobsByUser(userId);
//        return CommonResult.success(wellList);
//    }
//
//    //新版暂时不用了
//    /**
//     * 删除可操作人员
//     */
//    @RequestMapping("operator/delete")
//    public CommonResult delete(@RequestParam("jobId") long jobId,
//                               @RequestParam("userId") long userId) {
//        reportWellUserRelationService.delete(jobId, userId);
//        return CommonResult.success();
//    }
//
//    //新版暂时不用了
//    /**
//     * 删除可操作人员
//     */
//    @PostMapping("operator/batchDelete")
//    public CommonResult delete(@Validated @RequestBody BatchReportOperatorParams request) {
//        reportWellUserRelationService.batchDelete(request);
//        return CommonResult.success();
//    }

}
