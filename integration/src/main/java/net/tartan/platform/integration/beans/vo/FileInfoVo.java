package net.tartan.platform.integration.beans.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * menu
 * <AUTHOR>
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileInfoVo implements Serializable {
    private String fileId;

    private String fileName;

    private double fileSize;

    private String fileType;

    private Date uploadTime;

}