package net.tartan.platform.integration.entity.dailyreport;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MwdComponent {
    /**
     * 部件id
     */
    private long componentId;

    /**
     * 部件名称
     */
    private String name;

    /**
     * 是否失效
     */
    private boolean failed;
}
