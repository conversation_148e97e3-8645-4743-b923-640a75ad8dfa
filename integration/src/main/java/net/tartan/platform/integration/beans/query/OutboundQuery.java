package net.tartan.platform.integration.beans.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OutboundQuery {
    private Long id; // 出库单id
    private Long deviceType;
    private String serialNumber;
    private Integer greaterThan;
    private List<Long> deivceIdList;

    private String receiverName;
    private String checkerName;
    private Long mwdId;
    private Boolean hasChecked;
    private Boolean hasMwdId;

    private Integer isInProcess;
}
