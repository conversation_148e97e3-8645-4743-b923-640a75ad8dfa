package net.tartan.platform.integration.entity.deviceassemble.repaircheck.pitTools;

import lombok.Data;
import net.tartan.platform.integration.entity.deviceassemble.repaircheck.BaseCheckData;
import net.tartan.platform.integration.entity.deviceassemble.repaircheck.FaultDiagnosis;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 螺杆维修实体类
 * Spiral
 */
@Data
public class SpiralToolsData extends BaseCheckData {

    private String name;
    //工序内容List
    private List<ProcessDetail> processList;
    //《标识、包装标准》
    private List<Map<String, Object>> standerList;


//    //工序内容
//        /*
//    工序内容
//    顺序装配：是/否
//    总成转动灵活：是/否
//     */
//    @Data
//    static class Process{
//        private String ProcessDetail; //工艺内容
//        //《标识、包装标准》
//        private List<Map<String, Boolean>> stander;
//    }

    /*
        ProcessDetail
        例子：
        String    传动轴-下TC动圈
        String  游隙_____mm
        String   ____KN•m
        Map<String, Boolean>    厌氧胶 true
     */
    @Data
    static class ProcessDetail{

        private String name = null;
        private String value1 = null;
        private String value2 = null;
//        private Map<String, Boolean> value3;
        private Map<String, Object> value3;
        private ToolsCommonData toolsCommonData;
    }

    public SpiralToolsData init(){
        SpiralToolsData spiralToolsData = new SpiralToolsData();
        //工序内容
        String[] str1 = {"传动轴-下TC动圈","传动轴-上TC动圈","传动轴外壳-下TC静圈","水帽-传动轴","万向轴-水帽","转子-万向轴",
                "下可调壳体-传动轴外壳","定位套-下万向轴壳体-弯接头","上可调壳体-弯接头","上可调壳体-定子","防掉接头-定子","防掉螺母-防掉连杆-转子","防掉接头-代用接头/旁通阀"};
        String[] mapStr = {"厌氧胶","锂基润滑脂","厌氧胶","厌氧胶","厌氧胶","厌氧胶","厌氧胶","润滑脂","润滑脂","厌氧胶","厌氧胶","厌氧胶","螺纹脂"};
        String[] str2 = {"","游隙___mm","间隙___mm","","","","","弯度___","","","","",""};
        String[] str3 = {"___KN•m","___KN•m","___KN•m","___KN•m","___KN•m","___KN•m","___KN•m","","___KN•m","___KN•m","___KN•m","___KN•m","___KN•m"};
        String[] commonDevice = {"拆装架","拆装架","拆装架/深度尺","拆装架/深度尺","拆装架","拆装架","拆装架","拆装架","拆装架","拆装架","拆装架","拆装架","拆装架"};
        List<ProcessDetail> processList = new ArrayList<>();
        for(int a = 0; a < str1.length; a++){
            ProcessDetail processDetail = new ProcessDetail();
            processDetail.setName(str1[a]);
            if(!str2[a].equals("")){
                processDetail.setValue1(str2[a]);
            }
            if(!str3[a].equals("")){
                processDetail.setValue2(str3[a]);
            }
            Map<String,Object> map = new HashMap<>();
//            map.put(mapStr[a],true);
            map.put("key",mapStr[a]);
            map.put("value",true);
            processDetail.setValue3(map);
            processList.add(processDetail);

            ToolsCommonData toolsCommonData = new ToolsCommonData();
            toolsCommonData.setDevice(commonDevice[a]);
            processDetail.setToolsCommonData(toolsCommonData);
        }
        spiralToolsData.setProcessList(processList);

        //标识、包装标准
//        List<List<Map<String, Object>> standerList = new ArrayList<>();

        List<Map<String, Object>> stander1 = new ArrayList<>();

        Map<String, Object> value1 = new HashMap<>();
        value1.put("key","顺序装配");
        value1.put("value",false);
        stander1.add(value1);

//        List<Map<String, Object>> stander2 = new ArrayList<>();
        Map<String, Object> value2 = new HashMap<>();
        value2.put("key","总成转动灵活");
        value2.put("value",false);
        stander1.add(value2);

//        List<Map<String, Object>> stander3 = new ArrayList<>();
        Map<String, Object> value3 = new HashMap<>();
        value3.put("key","对弯点、高边线");
        value3.put("value",false);
        stander1.add(value3);

//        List<Map<String, Object>> stander4 = new ArrayList<>();
        Map<String, Object> value4 = new HashMap<>();
        value4.put("key","打磨掉夹痕");
        value4.put("value",false);
        stander1.add(value4);

//        List<Map<String, Object>> stander5 = new ArrayList<>();
        Map<String, Object> value5 = new HashMap<>();
        value5.put("key","打型号、SN号");
        value5.put("value",false);
        stander1.add(value5);

//        List<Map<String, Object>> stander6 = new ArrayList<>();
        Map<String, Object> value6 = new HashMap<>();
        value6.put("key","喷漆、包装");
        value6.put("value",false);
        stander1.add(value6);

//        standerList.add(stander1);
//        standerList.add(stander2);
//        standerList.add(stander3);
//        standerList.add(stander4);
//        standerList.add(stander5);
//        standerList.add(stander6);

        spiralToolsData.setStanderList(stander1);

//        List<Map<String, Object>> stander = new ArrayList<>();
//        stander.put("顺序装配",false);
//        stander.put("总成转动灵活",false);
//        stander.put("对弯点、高边线",false);
//        stander.put("打磨掉夹痕",false);
//        stander.put("打型号、SN号",false);
//        stander.put("喷漆、包装",false);
//        result.add(stander);
//        spiralToolsData.setStander(result);

        //故障诊断
        spiralToolsData.setFaultDiagnosis(new FaultDiagnosis().init());
        return spiralToolsData;
    }
}










