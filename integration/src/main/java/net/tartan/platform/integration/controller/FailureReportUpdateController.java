package net.tartan.platform.integration.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.common.beans.ResultCode;
import net.tartan.platform.integration.beans.dto.ComponentFailedDto;
import net.tartan.platform.integration.beans.dto.FailureReportUpdateDto;
import net.tartan.platform.integration.beans.query.FailureReportQuery;
import net.tartan.platform.integration.beans.vo.FailureReportPageVo;
import net.tartan.platform.integration.entity.MwdFailureShopInspection;
import net.tartan.platform.integration.exception.BusinessException;
import net.tartan.platform.integration.service.IComponentFailedService;
import net.tartan.platform.integration.service.IFailureReportUpdateService;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 失效报告网页端更新
 * </p>
 *
 * @since 2024-03-13
 */
@RestController
@RequestMapping("/failureShopUpdate")
public class FailureReportUpdateController {

    @Autowired
    private IFailureReportUpdateService failureReportUpdateService;
    @Autowired
    private IComponentFailedService componentFailedService;


    /**
     * 查有失效报告的井
     */
    @PostMapping("list/{current}/{size}")
    public CommonResult listWellWithFailure(
            @PathVariable long current,
            @PathVariable long size,
            @RequestBody(required = false) FailureReportQuery query) {
        if (ObjectUtils.isEmpty(query)) {
            query = new FailureReportQuery();
        }
        IPage<FailureReportPageVo> page = new Page<>(current, size);
        return CommonResult.success(failureReportUpdateService.listWellWithFailure(page, query));
    }

    /**
     * 车间更新 插入或更新
     */
    @PostMapping("update")
    public CommonResult update(@RequestBody MwdFailureShopInspection mwdFailureShopInspection) {
        if (ObjectUtils.isEmpty(mwdFailureShopInspection)) {
            return CommonResult.success();
        }
        failureReportUpdateService.updateByParam(mwdFailureShopInspection);
        return CommonResult.success();
    }

    @PostMapping("update/component")
    public CommonResult updateComponent(@RequestBody ComponentFailedDto componentFailedDto) {
        if (ObjectUtils.isEmpty(componentFailedDto)) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        componentFailedService.updateByParam(componentFailedDto);
        return CommonResult.success();
    }


//    /**
//     * 列出某个作业下所有的失效报告
//     */
//    @PostMapping("info")
//    public CommonResult getInfo( @RequestParam("jobId") Long jobId ) {
//        if (ObjectUtils.isEmpty(jobId)) {
//            throw new BusinessException(ResultCode.PARAMS_ERROR);
//        }
//
//        return CommonResult.success(failureReportUpdateService.getInfo(jobId));
//    }

    /**
     * 失效部件更新 插入或更新
     */


}
