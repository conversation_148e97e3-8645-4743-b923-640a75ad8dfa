package net.tartan.platform.integration.entity.deviceassemble.repaircheck;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class BottomEndCheckData extends BaseCheckData {
    private List<CheckDataModel> inCheckList;
    private List<CheckDataModel> outCheckList;

    public BottomEndCheckData init() {
        BottomEndCheckData bottomEndCheckData = new BottomEndCheckData();

        bottomEndCheckData.setInCheckList(inCheckListInit());

        bottomEndCheckData.setOutCheckList(outCheckListInit());

        //初始化故障诊断
        bottomEndCheckData.setFaultDiagnosis(new FaultDiagnosis().init());
        return bottomEndCheckData;
    }

    private List<CheckDataModel> inCheckListInit() {
        List<CheckDataModel> checkDataModelList = new ArrayList<>();

        CheckDataModel checkDataModel0 = new CheckDataModel();
        checkDataModel0.setName("Returned Clean\n返回清洗");
        checkDataModel0.setValue("");
        checkDataModelList.add(checkDataModel0);

        CheckDataModel checkDataModel1 = new CheckDataModel();
        checkDataModel1.setName("Clean All Threads of Dirt &Loctite\n清理所有螺纹脏物及乐泰胶");
        checkDataModel1.setValue("");
        checkDataModelList.add(checkDataModel1);

        CheckDataModel checkDataModel2 = new CheckDataModel();
        checkDataModel2.setName("Main shaft & Remove Poppet Tip\n拆卸主阀杆蘑菇头");
        checkDataModel2.setValue("");
        checkDataModelList.add(checkDataModel2);

        CheckDataModel checkDataModel3 = new CheckDataModel();
        checkDataModel3.setName("RemovePiston Cap& Check for Wash\n取下活塞帽并检查清理");
        checkDataModel3.setValue("");
        checkDataModelList.add(checkDataModel3);

        CheckDataModel checkDataModel4 = new CheckDataModel();
        checkDataModel4.setName("Helix crack check\n引鞋裂纹检查");
        checkDataModel4.setValue("");
        checkDataModelList.add(checkDataModel4);

        CheckDataModel checkDataModel5 = new CheckDataModel();
        checkDataModel5.setName("Main Shaft Orifice\n主阀杆限流环状态");
        checkDataModel5.setValue("");
        checkDataModelList.add(checkDataModel5);

        CheckDataModel checkDataModel6 = new CheckDataModel();
        checkDataModel6.setName("Plenum Ceramic Sleeve\n陶瓷环状态");
        checkDataModel6.setValue("");
        checkDataModelList.add(checkDataModel6);

        CheckDataModel checkDataModel7 = new CheckDataModel();
        checkDataModel7.setName("Piston Cap Condition\n活塞帽状态");
        checkDataModel7.setValue("");
        checkDataModelList.add(checkDataModel7);

        CheckDataModel checkDataModel8 = new CheckDataModel();
        checkDataModel8.setName("Main Shaft Condition\n主阀杆状态");
        checkDataModel8.setValue("");
        checkDataModelList.add(checkDataModel8);

        CheckDataModel checkDataModel9 = new CheckDataModel();
        checkDataModel9.setName("Plenum Condition\n主阀筒状态");
        checkDataModel9.setValue("");
        checkDataModelList.add(checkDataModel9);

        CheckDataModel checkDataModel10 = new CheckDataModel();
        checkDataModel10.setName("Poppet Bolts Condition\n蘑菇头螺丝状态");
        checkDataModel10.setValue("");
        checkDataModelList.add(checkDataModel10);

        CheckDataModel checkDataModel11 = new CheckDataModel();
        checkDataModel11.setName("Main Spring Condition\n弹簧状态");
        checkDataModel11.setValue("");
        checkDataModelList.add(checkDataModel11);

        CheckDataModel checkDataModel12 = new CheckDataModel();
        checkDataModel12.setName("Ram Stop Condition\n限位杆状态");
        checkDataModel12.setValue("");
        checkDataModelList.add(checkDataModel12);

        CheckDataModel checkDataModel13 = new CheckDataModel();
        checkDataModel13.setName("Abrasion Ring Condition\n耐磨环状态");
        checkDataModel13.setValue("");
        checkDataModelList.add(checkDataModel13);

        CheckDataModel checkDataModel14 = new CheckDataModel();
        checkDataModel14.setName("Locking Ring Condition\n防掉卡簧状态");
        checkDataModel14.setValue("");
        checkDataModelList.add(checkDataModel14);

        CheckDataModel checkDataModel15 = new CheckDataModel();
        checkDataModel15.setName("Helix Crown Condition\n引鞋状态");
        checkDataModel15.setValue("");
        checkDataModelList.add(checkDataModel15);

        CheckDataModel checkDataModel16 = new CheckDataModel();
        checkDataModel16.setName("Polyseal Condition\nY圈状态");
        checkDataModel16.setValue("");
        checkDataModelList.add(checkDataModel16);

        return checkDataModelList;
    }

    private List<CheckDataModel> outCheckListInit() {
        List<CheckDataModel> checkDataModelList = new ArrayList<>();

        CheckDataModel checkDataModel0 = new CheckDataModel();
        checkDataModel0.setName("Red loctite 3\" Ceramic Sleeve\n陶瓷环抹263红胶");
        checkDataModel0.setValue("");
        checkDataModelList.add(checkDataModel0);

        CheckDataModel checkDataModel1 = new CheckDataModel();
        checkDataModel1.setName("RTV on Main Shaft Orifice\n限流环抹玻璃胶");
        checkDataModel1.setValue("");
        checkDataModelList.add(checkDataModel1);

        CheckDataModel checkDataModel2 = new CheckDataModel();
        checkDataModel2.setName("Red Loctite On Piston Cap\n活塞帽抹263红胶");
        checkDataModel2.setValue("");
        checkDataModelList.add(checkDataModel2);

        CheckDataModel checkDataModel3 = new CheckDataModel();
        checkDataModel3.setName("Piston Cap Torqued\n活塞帽上扣扭矩（ft·Ibs）");
        checkDataModel3.setValue("");
        checkDataModelList.add(checkDataModel3);

        CheckDataModel checkDataModel4 = new CheckDataModel();
        checkDataModel4.setName("Gap check\n检查陶瓷环与阀筒内壁是否有间隙");
        checkDataModel4.setValue("");
        checkDataModelList.add(checkDataModel4);

        CheckDataModel checkDataModel5 = new CheckDataModel();
        checkDataModel5.setName("QC signature before Aasembly\n组装前质检/签字");
        checkDataModel5.setValue("");
        checkDataModelList.add(checkDataModel5);

        CheckDataModel checkDataModel6 = new CheckDataModel();
        checkDataModel6.setName("Test Pressure Change\n气压变化");
        checkDataModel6.setValue("");
        checkDataModelList.add(checkDataModel6);

        CheckDataModel checkDataModel7 = new CheckDataModel();
        checkDataModel7.setName("Be tight or not\n是否打紧");
        checkDataModel7.setValue("");
        checkDataModelList.add(checkDataModel7);

        return checkDataModelList;
    }
}
