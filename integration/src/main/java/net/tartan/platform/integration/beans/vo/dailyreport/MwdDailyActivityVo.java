package net.tartan.platform.integration.beans.vo.dailyreport;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * mwd_daily_activity
 *
 * <AUTHOR>
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MwdDailyActivityVo {

    /**
     * 起始时间
     */
    private String startDate;

    /**
     * 结束时间
     */
    private String endDate;

    /**
     * 起始井深
     */
    private BigDecimal depthFrom;

    /**
     * 结束井深
     */
    private BigDecimal depthTo;

    /**
     * 工况
     */
    private String activity;
}
