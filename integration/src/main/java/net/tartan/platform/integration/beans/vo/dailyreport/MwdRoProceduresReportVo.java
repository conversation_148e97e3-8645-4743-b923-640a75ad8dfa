package net.tartan.platform.integration.beans.vo.dailyreport;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.enums.EnumReportType;
import net.tartan.platform.integration.entity.dailyreport.MwdRoProceduresAction;

import java.util.List;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MwdRoProceduresReportVo {

    private EnumReportType reportType;

    private long lastModified;

    /**
     * 操作人
     */
    private String operator;

    /**
     * mwd操作人
     */
    private String mwdOperator;

    /**
     * 签名日期yyyy-MM-dd
     */
    private String signatureDate;

    /**
     * 截图
     */
    private String screenShoot;

    /**
     * 工具
     */
    private List<MwdRoProceduresAction> actionList;


}
