package net.tartan.platform.integration.beans.vo;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import net.tartan.platform.common.enums.EnumRiskType;
import net.tartan.platform.common.enums.EnumTransferReceiveStatus;

@Data
public class DeviceTransferInfoVo {
    private Long deviceId;

    /**
     * 仪器类型
     */
    private Long deviceType;

    /**
     * 仪器库存类型 库房盘点按照这个来分类
     */
    private Long stockType;

    /**
     * 品名
     */
    private String invName;

    /**
     * 序列号
     */
    private String serialNumber;
    /**
     * 风险类型
     */
    @TableField(value = "risk_type", updateStrategy= FieldStrategy.IGNORED)
    private EnumRiskType riskType;


    // 调拨单相关信息
    // 最后一条调拨单的接收状态
    private EnumTransferReceiveStatus lastReceiveStatus;
    //最后一次的接收人
    private String lastReceiveBy;
    //最后一次的接收时间
    private String lastReceiveDate;
    // 最后一条调拨单的id
    private Long lastTransferId;

    //  只有当最后一次调拨单未完成的时候，才会去查现场接收状态
    private Long lastDetailId;
    //最后一次现场接收状态
    private EnumTransferReceiveStatus lastFieldReceiveStatus;
    //最后一次现场接收人
    private String lastFieldReceiveBy;
    //最后一次现场接收时间
    private String lastFieldReceiveDate;


}
