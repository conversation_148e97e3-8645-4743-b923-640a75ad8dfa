package net.tartan.platform.integration.entity.oa;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 请购详表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("formson_0760")
public class OaPurchaseOrderDetails implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.NONE)
    private Long id;

    @TableField("formmain_id")
    private Long formmainId;

    @TableField("sort")
    private Integer sort;

    /**
     * 序号（明细表行序号）
     */
    @TableField("field0005")
    private Long field0005;

    /**
     * 存货编码
     */
    @TableField("field0006")
    private String inventoryCode;

    /**
     * 存货名称
     */
    @TableField("field0007")
    private String inventoryName;

    /**
     * 规格型号
     */
    @TableField("field0008")
    private String specifications;

    /**
     * 计量单位
     */
    @TableField("field0009")
    private String unit;

    /**
     * 请购数量
     */
    @TableField("field0010")
    private BigDecimal requisitionQuantity;

    /**
     * 供应商信息
     */
    @TableField("field0011")
    private String supplierInfo;

    /**
     * 单价
     */
    @TableField("field0012")
    private BigDecimal unitPrice;

    /**
     * 税率
     */
    @TableField("field0013")
    private BigDecimal taxRate;

    /**
     * 金额小计
     */
    @TableField("field0016")
    private BigDecimal subtotalAmount;

    /**
     * 需求日期
     */
    @TableField("field0020")
    private Date requiredDate;

    /**
     * 是否紧急采购（下拉选择）
     * -6322045658969865 -> 是
     * -4428837054536597418 -> 否
     */
    @TableField("field0021")
    private String isUrgentPurchase;

    /**
     * 项目
     */
    @TableField("field0023")
    private String project;

    /**
     * 紧急程度
     */
    @TableField("field0024")
    private String urgencyLevel;

    /**
     * 备注意见
     */
    @TableField("field0025")
    private String remarks;

    /**
     * ERP请购序号
     */
    @TableField("field0028")
    private String erpRequisitionNo;

    /**
     * 是否含税
     */
    @TableField("field0029")
    private String isTaxIncluded;

    /**
     * 含税参数
     */
    @TableField("field0030")
    private Long taxIncludedParam;

    /**
     * 供应商编码
     */
    @TableField("field0031")
    private String supplierCode;

    /**
     * 税率编码
     */
    @TableField("field0035")
    private String taxRateCode;

    /**
     * 币种编号
     */
    @TableField("field0036")
    private String currencyCode;

    /**
     * 税率代码
     */
    @TableField("field0037")
    private String taxRateCode2;

    /**
     * 品号描述
     */
    @TableField("field0040")
    private String itemDescription;
}
