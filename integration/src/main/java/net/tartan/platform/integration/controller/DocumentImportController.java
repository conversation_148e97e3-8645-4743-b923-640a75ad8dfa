package net.tartan.platform.integration.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import lombok.extern.slf4j.Slf4j;
import net.tartan.platform.integration.service.DocumentImportMainService;

/**
 * 单据导入控制器
 * 提供与ERP系统兼容的接口，供OA系统调用
 * 替代原ERP系统的ImportPaymentReqDocService接口
 * 支持多种单据类型的状态更新
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Slf4j
@RestController
@RequestMapping("/erp-compat")
public class DocumentImportController {

    @Autowired
    private DocumentImportMainService documentImportMainService;

    /**
     * 导入单据数据接口
     * 兼容ERP系统的ImportData接口格式
     * 接收OA系统发送的各种单据类型状态更新请求
     *
     * 支持的单据类型：
     * - PAYMENT_REQ_DOC: 付款申请单
     * - CONTRACT: 合同
     * - REQUISITION: 请购单
     * - RECEIVABLE_DOC: 应收单
     *
     * 请求格式：
     * {
     *   "std_data": {
     *     "parameter": {
     *       "productName": "XThirdParty",
     *       "templateName": "PAYMENT_REQ_DOC.I01",
     *       "importData": [
     *         {
     *           "DOC_NO": "单据编号",
     *           "DOC_CODE": "PAYMENT_REQ_DOC",
     *           "STATUS": "0"
     *         }
     *       ]
     *     }
     *   }
     * }
     * 
     * 响应格式：
     * {
     *   "std_data": {
     *     "execution": {
     *       "code": "0",
     *       "description": "成功"
     *     },
     *     "parameter": {
     *       "response_result": {
     *         "Status": "0",
     *         "Message": "成功",
     *         "DOC_NO": "处理的单据编号"
     *       }
     *     }
     *   }
     * }
     * 
     * @param requestData 请求数据
     * @return 处理结果
     */
    @PostMapping("/importData")
    public Map<String, Object> importData(@RequestBody Map<String, Object> requestData) {
        log.info("接收到单据导入请求: {}", requestData);

        try {
            // 解析请求参数
            Map<String, Object> stdData = (Map<String, Object>) requestData.get("std_data");
            if (stdData == null) {
                return buildErrorResponse("请求格式错误：缺少std_data");
            }

            Map<String, Object> parameter = (Map<String, Object>) stdData.get("parameter");
            if (parameter == null) {
                return buildErrorResponse("请求格式错误：缺少parameter");
            }

            String productName = (String) parameter.get("productName");
            String templateName = (String) parameter.get("templateName");
            List<Map<String, Object>> importData = (List<Map<String, Object>>) parameter.get("importData");

            // 调用服务处理
            Map<String, Object> serviceResult = documentImportMainService.importData(productName, templateName, importData);

            // 构建成功响应
            Map<String, Object> response = new HashMap<>();
            Map<String, Object> responseStdData = new HashMap<>();
            Map<String, Object> execution = new HashMap<>();
            execution.put("code", "0");
            execution.put("description", "成功");

            Map<String, Object> responseParameter = new HashMap<>();
            responseParameter.put("response_result", serviceResult.get("response_result"));

            responseStdData.put("execution", execution);
            responseStdData.put("parameter", responseParameter);
            response.put("std_data", responseStdData);

            log.info("单据导入处理完成: {}", response);
            return response;

        } catch (Exception e) {
            log.error("单据导入处理失败", e);
            return buildErrorResponse("处理失败: " + e.getMessage());
        }
    }

    /**
     * 构建错误响应
     * 
     * @param errorMessage 错误信息
     * @return 错误响应
     */
    private Map<String, Object> buildErrorResponse(String errorMessage) {
        Map<String, Object> response = new HashMap<>();
        Map<String, Object> stdData = new HashMap<>();
        Map<String, Object> execution = new HashMap<>();
        execution.put("code", "1");
        execution.put("description", errorMessage);

        Map<String, Object> parameter = new HashMap<>();
        Map<String, Object> responseResult = new HashMap<>();
        responseResult.put("Status", "1");
        responseResult.put("Message", errorMessage);
        responseResult.put("DOC_NO", "");
        parameter.put("response_result", responseResult);

        stdData.put("execution", execution);
        stdData.put("parameter", parameter);
        response.put("std_data", stdData);

        return response;
    }

    /**
     * 健康检查接口
     * 
     * @return 服务状态
     */
    @PostMapping("/health")
    public Map<String, Object> health() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "UP");
        result.put("service", "ImportPaymentReqDocService");
        result.put("timestamp", System.currentTimeMillis());
        return result;
    }
}
