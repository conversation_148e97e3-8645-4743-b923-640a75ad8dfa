package net.tartan.platform.integration.entity.dailyreport;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * project_well_statistic
 * <AUTHOR>
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProjectDailyStatistic{
    private Long id;

    /**
     * 井日报id
     */
    private Long wellReportId;

    /**
     * 钻进方式
     */
    private String type;

    /**
     * 地层
     */
    private String formation;

    /**
     * 岩芯
     */
    private String rockCore;

    /**
     * 段长
     */
    private BigDecimal segLength;

    /**
     * 时间
     */
    private BigDecimal time;

    /**
     * 机械钻速
     */
    private BigDecimal drillRate;
}
