package net.tartan.platform.integration.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.common.beans.ResultCode;
import net.tartan.platform.integration.beans.dto.LaborHourBatchDto;
import net.tartan.platform.integration.beans.dto.LaborHourDto;
import net.tartan.platform.integration.beans.dto.LaborHourReviewDto;
import net.tartan.platform.integration.beans.query.ProjectLaborHourQuery;
import net.tartan.platform.integration.beans.vo.*;
import net.tartan.platform.integration.exception.BusinessException;
import net.tartan.platform.integration.service.ILaborHourService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.text.ParseException;
import java.util.List;

/**
 * 项目工时管理
 */
@RestController
@RequestMapping("/projectLaborHour")
public class ProjectLaborHourController {

    @Autowired
    private ILaborHourService laborHourService;

    @PostMapping("add")
    public CommonResult add(@RequestBody @Valid LaborHourBatchDto dto) {
        laborHourService.addLaborHourRecord(dto);
        return CommonResult.success();
    }

    @PostMapping("list/{current}/{size}")
    public CommonResult list(@PathVariable long current,
                             @PathVariable long size,
                             @RequestBody(required = false) ProjectLaborHourQuery query) {
        IPage<LaborHourVo> page = new Page<>(current, size);
        if (query == null) {
            query = new ProjectLaborHourQuery();
        }
        laborHourService.listLaborHourRecord(page, query);
        return CommonResult.success(page);
    }

    /**
     * 精简模式
     * 根据时间范围，列出这段时间内，每一天的工时数据
     * @param query
     * @return
     */
    @PostMapping("listByDays")
    public CommonResult listByDays(@RequestBody @Valid ProjectLaborHourQuery query) {
        return CommonResult.success(laborHourService.listLaborHourByDayRecord(query));
    }

    @GetMapping("info")
    public CommonResult info(@RequestParam("id") Long laborHourRecordId) {
        LaborHourVo laborHourVo = laborHourService.getInfo(laborHourRecordId);
        return CommonResult.success(laborHourVo);
    }

    @PostMapping("update")
    public CommonResult update(@RequestBody @Valid LaborHourDto dto) {
        if (dto.getId() == null) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        laborHourService.update(dto);
        LaborHourVo laborHourVo = laborHourService.getInfo(dto.getId());
        return CommonResult.success(laborHourVo);
    }

    @PostMapping("delete")
    public CommonResult delete(@RequestBody @Valid LaborHourDto dto) {
        if (dto.getId() == null) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        laborHourService.delete(dto.getId());
        return CommonResult.success();
    }


    /**
     * 提交审批
     * 项目+日期+员工 来区分
     * @param laborHourReviewDtoList
     * @return
     */
    @PostMapping("addReview")
    public CommonResult addReview(@RequestBody @Valid List<LaborHourReviewDto> laborHourReviewDtoList) {
        laborHourService.addLaborHourReview(laborHourReviewDtoList);
        return CommonResult.success();
    }

    @PostMapping("listReview")
    public CommonResult listReview(@RequestBody @Valid ProjectLaborHourQuery query) {
        return CommonResult.success(laborHourService.listLaborHourReview(query));
    }

    @PostMapping("revokeReview")
    public CommonResult revokeReview(@RequestBody @Valid ProjectLaborHourQuery query) {
        laborHourService.revokeReview(query);
        return CommonResult.success();
    }

    @PostMapping("getReviewTodoList")
    public CommonResult getReviewTodoList(@RequestBody @Valid ProjectLaborHourQuery query) throws ParseException {
        return CommonResult.success(laborHourService.getReviewTodoList(query));
    }

    @PostMapping("review")
    public CommonResult review(@RequestBody @Valid ProjectLaborHourQuery query) throws ParseException {
        if(StringUtils.isEmpty(query.getReviewResult()) || ObjectUtils.isEmpty(query.getReviewIdList())){
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        laborHourService.review(query);
        return CommonResult.success();
    }

    /**
     * 工时明细
     * @param current
     * @param size
     * @param query
     * @return
     */
    @PostMapping("detailList/{current}/{size}")
    public CommonResult detailList(@PathVariable long current,
                             @PathVariable long size,
                             @RequestBody(required = false) ProjectLaborHourQuery query) {
        IPage<LaborHourVo> page = new Page<>(current, size);
        if (query == null) {
            query = new ProjectLaborHourQuery();
        }
        LaborHourStatisticsVo vo = laborHourService.listLaborHourDetailList(page, query);
        return CommonResult.success(vo);
    }

    /**
     * 个人工时求和
     * @return
     */
    @PostMapping("getTotal")
    public CommonResult getTotal(@RequestBody @Valid ProjectLaborHourQuery query) {
        if(query == null){
            return null;
        }
        LaborHourStatisticsVo vo = laborHourService.getTotal(query);
        return CommonResult.success(vo);
    }

    @PostMapping("detailListByDay")
    public CommonResult listLaborHourByDaysList(@RequestBody(required = false) ProjectLaborHourQuery query) {
        if (query == null) {
            query = new ProjectLaborHourQuery();
        }
        LaborHourStatisticsByDaysVo vo = laborHourService.listLaborHourByDaysList(query);
        return CommonResult.success(vo);
    }
    @PostMapping("statistics")
    public CommonResult listProjectStatistics(@RequestBody(required = false) ProjectLaborHourQuery query) {
        if (query == null) {
            query = new ProjectLaborHourQuery();
        }
        List<ProjectStatisticsVo> vo = laborHourService.listProjectStatistics(query);
        return CommonResult.success(vo);
    }

    @PostMapping("memberStatistics/{current}/{size}")
    public CommonResult getMemberLaborHourStatisticsPage(@PathVariable long current,
                             @PathVariable long size,
                             @RequestBody(required = false) ProjectLaborHourQuery query) {
        IPage<MemberLaborHourStatisticsVo> page = new Page<>(current, size);
        if (query == null) {
            query = new ProjectLaborHourQuery();
        }
        laborHourService.getMemberLaborHourStatistics(page, query);
        return CommonResult.success(page);
    }

    /**
     * 获取所有的参项成员
     * @param query
     * @return
     */
    @PostMapping("getMember")
    public CommonResult getProjectMember(@RequestBody(required = false) ProjectLaborHourQuery query) {
        if (query == null) {
            query = new ProjectLaborHourQuery();
        }
        List<ProjectMemberInfoVo> vo = laborHourService.getProjectMember(query);
        return CommonResult.success(vo);
    }
    /**
     * 获取所有的参项成员
     * @param query
     * @return
     */
    @PostMapping("getFillStatus")
    public CommonResult getLaborHourFillStatusList(@RequestBody(required = false) ProjectLaborHourQuery query) {
        if (query == null) {
            query = new ProjectLaborHourQuery();
        }
        return CommonResult.success(laborHourService.selectLaborHourFillStatusList(query));
    }
}
