package net.tartan.platform.integration.beans.query;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.tartan.platform.integration.entity.process.ProcessFlowDetail;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 工艺流程卡
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-17
 */
@Data
public class ProcessFlowCardQuery {

    private Long processFlowId;

    /**
     * 工艺编号
     */
    private String processFlowCode;
    private Date startDate;
    private Date endDate;

    /**
     * 工艺名称
     */
    private String processFlowName;

    /**
     * 材质
     */
    private String materialQuality;

    /**
     * 是否完成 0：未完成 1：已完成
     */
    private Integer finished;

}
