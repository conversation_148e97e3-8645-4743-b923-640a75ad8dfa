package net.tartan.platform.integration.beans.vo.devicerisktype;

import lombok.Data;
import net.tartan.platform.common.enums.EnumRiskType;

import java.math.BigDecimal;

@Data
public class ComponentRiskTypeVo {

    private Long componentId;
    /**
     * 品名
     */
    private String invName;

    /**
     * 序列号，erp的批次号
     */
    private String serialNumber;

    /**
     * 数量
     */
    private Integer quantity;
    /**
     * 风险类型
     */
    private EnumRiskType riskType;
    /**
     * 是否风险
     */
    private boolean risk;

    /**
     * 额定温度
     */
    private BigDecimal standardBht;

    /**
     * 最高温度
     */
    private BigDecimal maxBht;
    /**
     * 使用时长
     */
    private BigDecimal totalHours;

    /**
     * 最大时长
     */
    private BigDecimal maxHours;
}
