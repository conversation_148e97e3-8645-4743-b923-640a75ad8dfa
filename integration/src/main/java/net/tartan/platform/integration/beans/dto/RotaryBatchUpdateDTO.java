package net.tartan.platform.integration.beans.dto;

import lombok.Data;

import java.util.List;

/**
 * 更新旋导节点信息DTO
 */
@Data
public class RotaryBatchUpdateDTO {

    // 旋导工单id
    private Long mwdId;

    // 旋导部件工单id
    private Long eaId;

    // 仪器序列号
    private String serialNumber;

    /**
     * 更新部件sort值 + 服役历史
     *      in 入库 更新结束服役记录
     *      out 出库 更新开始服役记录
     */
    private List<ComponentHierarchyDTO> inComponentFirstLevel; // 第一层部件，无parentId
    private List<ComponentHierarchyDTO> outComponentFirstLevel; // 第一层部件，无parentId
    // 如果拆或者装上的节点含有子节点，则需要吧所有子节点都给后端（用来更新服役历史）
    private List<ComponentHierarchyDTO> inComponent;
    private List<ComponentHierarchyDTO> outComponent;

    /**
     * 只更新节点信息（不包括parentId）
     */
    private List<ComponentHierarchyDTO> componentUpdateList; // 更新ComponentHierarchyDTO里的所有参数

}
