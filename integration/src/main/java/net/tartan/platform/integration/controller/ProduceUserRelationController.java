package net.tartan.platform.integration.controller;

import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.integration.entity.ProduceUserRelation;
import net.tartan.platform.integration.service.ProduceUserRelationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("producerUser")
public class ProduceUserRelationController {
    @Autowired
    private ProduceUserRelationService produceUserRelationService;


    @PostMapping("add")
    public CommonResult addUser(@RequestBody ProduceUserRelation produceUserRelation) {
        produceUserRelationService.save(produceUserRelation);
        return CommonResult.success();
    }

    @PostMapping("delete")
    public CommonResult deleteUser(@RequestBody ProduceUserRelation produceUserRelation) {
        produceUserRelationService.removeById(produceUserRelation.getId());
        return CommonResult.success();
    }

    @GetMapping("query")
    public CommonResult query(@RequestParam("produceId") long produceId) {
        return CommonResult.success(produceUserRelationService.getProduceUsers(produceId));
    }

    @PostMapping("hours/update")
    public CommonResult updateHours(@RequestBody ProduceUserRelation produceUserRelation) {
        produceUserRelationService.updateById(produceUserRelation);
        return CommonResult.success();
    }

}
