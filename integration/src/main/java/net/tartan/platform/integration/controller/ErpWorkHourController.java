package net.tartan.platform.integration.controller;

import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.integration.beans.query.ErpWorkHourQuery;
import net.tartan.platform.integration.beans.query.ToolQuery;
import net.tartan.platform.integration.entity.Device;
import net.tartan.platform.integration.entity.erp.ErpWorkHourDetail;
import net.tartan.platform.integration.entity.erp.ErpWorkHourMainBoard;
import net.tartan.platform.integration.service.erp.IErpWorkHourService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/erpWorkHour")
public class ErpWorkHourController {

    @Autowired
    private IErpWorkHourService erpWorkHourService;

    @PostMapping("/baseInfoList")
    public CommonResult selectErpWorkHourMainBoard(@RequestBody ErpWorkHourQuery query) {
        List<ErpWorkHourMainBoard> list = erpWorkHourService.selectErpWorkHourMainBoard(query);
        return CommonResult.success(list);
    }

    @PostMapping("/detailInfoList")
    public CommonResult selectErpWorkHourDetail(@RequestBody ErpWorkHourQuery query) {
        List<ErpWorkHourDetail> list = erpWorkHourService.selectErpWorkHourDetail(query);
        return CommonResult.success(list);
    }


}
