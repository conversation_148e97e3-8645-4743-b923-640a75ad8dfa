package net.tartan.platform.integration.beans.vo;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.enums.EnumServiceStatus;

import java.math.BigDecimal;

/**
 * <p>
 * 测试部件服役表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-15
 */
@Data
@NoArgsConstructor
public class ComponentTestServiceHistoryVo {

    private Long componentTestServiceHistoryId;

    /**
     * 品名
     */
    private String invName;

    /**
     * 序列号
     */
    private String serialNumber;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 试用件从开始服役-》服役中（继续服役）-》结束服役为一个周期
     * 每个服役周期的母件序列号相同
     * *
     * 服役状态，如果序列号为空，表示新增，此时是开始服役
     * 如果前端给了服役标示，则按照前端给的标志标记服役状态，一般是结束服役
     * 如果前端没有给服役状态，且序列号不为空，则表示继续服役。
     * *
     * 如果是继续服役的状态，那么母件的序列号需要与库中的保持一致，否则需要报错
     */
    private EnumServiceStatus serviceStatus;

    /**
     * 台账id
     */
    private Long mwdId;
    /**
     * 工单号
     */
    private String mwdNumber;

    /**
     * 母件品名
     */
    private String parentInvName;

    /**
     * 母件序列号，erp的批次号
     */
    private String parentSerialNumber;

    /**
     * 描述
     */
    private String description;
    /**
     * 作业号
     */
    private String jobNumber;
    /**
     * 井号
     */
    private String wellNumber;
    /**
     * 循环时间
     */
    private BigDecimal circulateHrs;
    /**
     * 最高温度
     */
    private BigDecimal maxBht;
    /**
     * 入井时长
     */
    private BigDecimal inWellHour;
    /**
     * 入井趟次
     */
    private Integer run;
}
