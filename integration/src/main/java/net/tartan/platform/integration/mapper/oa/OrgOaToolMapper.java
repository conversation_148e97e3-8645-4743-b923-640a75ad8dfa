package net.tartan.platform.integration.mapper.oa;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.tartan.platform.integration.beans.query.OaToolQuery;
import net.tartan.platform.integration.entity.oa.OaToolDetailInfo;
import net.tartan.platform.integration.entity.oa.OaToolInfo;
import net.tartan.platform.integration.entity.oa.OaToolSaveInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface OrgOaToolMapper extends BaseMapper<OaToolInfo> {

    List<OaToolInfo> selectBaseInfoByQuery(@Param("query") OaToolQuery query);
    List<OaToolDetailInfo> selectToolListById(@Param("no") String no);

    // 过滤条件: orderNumberList, wellNumberList, finishedflag = 1, start_date > 2025-05-22
    List<OaToolSaveInfo> selectOaToolRequestsForImport(@Param("query") OaToolQuery query);
}
