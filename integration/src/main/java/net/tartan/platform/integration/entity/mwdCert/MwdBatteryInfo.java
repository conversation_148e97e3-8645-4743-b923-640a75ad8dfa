package net.tartan.platform.integration.entity.mwdCert;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.constant.PfConstant;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 锂电池维护报告（合格证）
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = PfConstant.MWD_CERT_REPORT)
public class MwdBatteryInfo extends MwdCertBaseInfo {

    /**
     * 出厂电池芯类型
     */
    private String temp = "";

    /**
     * 外观检查
     */
    private String surfaceCheck = "合格";

    /**
     * 针孔测试
     */
    private String needleCheck = "合格";

    /**
     * 电压测试
     */
    private String voltageCheck = "合格";

    /**
     * Ringout测试
     */
    private String ringoutCheck = "合格";

    /**
     * 未加载电压
     */
    private String unloadCheck = "";

    /**
     * 加载电压
     */
    private String onloadCheck = "";

    /**
     * 检测结果
     */
    private String checkResult = "按照达坦锂电池检查测试规范进行出厂检测并通过";

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
    }
}
