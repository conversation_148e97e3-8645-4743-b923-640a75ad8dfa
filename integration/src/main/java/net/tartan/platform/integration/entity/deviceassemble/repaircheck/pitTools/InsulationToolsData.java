package net.tartan.platform.integration.entity.deviceassemble.repaircheck.pitTools;

import lombok.Data;
import net.tartan.platform.integration.entity.deviceassemble.repaircheck.BaseCheckData;
import net.tartan.platform.integration.entity.deviceassemble.repaircheck.FaultDiagnosis;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 绝缘短接维修 实体类
 * Insulation
 */
@Data
public class InsulationToolsData extends BaseCheckData {
//    private String name;
    //工序内容List
    private List<Process> processList;
    //《标识、包装标准》
    private List<Map<String, Object>> standerList;



    @Data
    static class Process{
        private String processName; //工艺内容

        private ToolsCommonData toolsCommonData;
    }

    public InsulationToolsData init(){

        InsulationToolsData insulationToolsData = new InsulationToolsData();

        //工序内容
        List<Process> processList = new ArrayList<>();
        String[] str1 = {"清洗所有零配件，保证无铁屑、杂质等异物",
                "在LOWER SUB内孔装入2个___O圈并涂抹润滑脂和装入内绝缘环",
                "测量螺纹绝缘层电阻为___",
                "在TOB SUB内孔里装入2个___O圈，在其一端母扣端面装入___O圈，并在O圈和其装配位置涂抹润滑脂",
                "将LOWER SUB固定在卸扣机上，在其有绝缘层的一端螺纹上涂抹___螺纹胶",
                "将外绝缘环套在LOWER SUB有绝缘层一端的外螺纹上",
                "将TOB SUB内孔有O圈的一端内螺纹对准LOWER SUB有绝缘层一端的外螺纹进行螺纹连接，且上扣扭矩至___",
                "测量LOWER SUB和TOB SUB之间电阻为___",
                "测量外绝缘环两边间隙为___"
        };
        String[] str2 = {"","装配平台","万用表","装配平台","卸扣机","/","卸扣机","万用表","塞尺"};
        for(int a = 0; a < str1.length; a++){
            Process process = new Process();
            process.setProcessName(str1[a]);
            ToolsCommonData toolsCommonData = new ToolsCommonData();
            toolsCommonData.setDevice(str2[a]);
            process.setToolsCommonData(toolsCommonData);
            processList.add(process);
        }
        insulationToolsData.setProcessList(processList);

        //标识、包装标准

//        List<List<Map<String, Object>>> standerList = new ArrayList<>();

        List<Map<String, Object>> stander1 = new ArrayList<>();


//        List<Map<String, Object>> stander4 = new ArrayList<>();
        Map<String, Object> value1 = new HashMap<>();
        value1.put("key","打磨掉夹痕");
        value1.put("value",false);
        stander1.add(value1);

//        List<Map<String, Object>> stander5 = new ArrayList<>();
        Map<String, Object> value2 = new HashMap<>();
        value2.put("key","打型号、SN号");
        value2.put("value",false);
        stander1.add(value2);

//        List<Map<String, Object>> stander6 = new ArrayList<>();
        Map<String, Object> value3 = new HashMap<>();
        value3.put("key","喷漆、包装");
        value3.put("value",false);
        stander1.add(value3);

        insulationToolsData.setStanderList(stander1);

        //初始化故障诊断
        insulationToolsData.setFaultDiagnosis(new FaultDiagnosis().init());
        return insulationToolsData;
    }
}
