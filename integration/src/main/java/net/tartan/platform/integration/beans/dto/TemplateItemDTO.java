package net.tartan.platform.integration.beans.dto;

import lombok.Data;
import java.util.List;

/**
 * 模板项目DTO
 */
@Data
public class TemplateItemDTO {
    /**
     * 项目ID
     */
    private Integer id;
    
    /**
     * 模板ID
     */
    private Integer templateId;
    
    /**
     * 父项目ID
     */
    private Integer parentId;
    
    /**
     * 零件编号
     */
    private String unitNumber;

    /**
     * 零件名称
     */
    private String unitName;
    
    /**
     * 类型名称
     */
    private String typeName;
    
    /**
     * 设备ID
     */
    private Integer deviceId;
    
    /**
     * 序列号
     */
    private String serialNumber;
    
    /**
     * 需求数量
     */
    private Integer requiredAmount;
    
    /**
     * 实际数量
     */
    private Integer actualAmount;
    
    /**
     * 入库数量
     */
    private Integer loadInAmount;
    
    /**
     * 设备类型列表
     */
    private String deviceTypeList;
    
    /**
     * 备注
     */
    private String note;
    
    /**
     * 显示顺序
     */
    private Integer displayOrder;
    
    /**
     * 是否为分类
     */
    private Boolean isCategory;
    
    
    /**
     * 子项目列表（新名称）
     */
    private List<TemplateItemDTO> units;
}