package net.tartan.platform.integration.controller;

import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.integration.beans.request.*;
import net.tartan.platform.integration.entity.Role;
import net.tartan.platform.integration.service.RoleMenuInfoService;
import net.tartan.platform.integration.service.RoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RestController
@RequestMapping("role")
public class RoleContoller {

    @Autowired
    private RoleService roleService;
    @Autowired
    private RoleMenuInfoService roleMenuInfoService;

    /**
     * 添加角色
     * @param roleParams
     * @return
     */
    @PostMapping("add")
    public CommonResult addRole(@RequestBody AddRoleParams roleParams) {
        roleService.add(roleParams);
        return CommonResult.success(null);
    }

    /**
     * 查询角色列表
     * @return
     */
    @GetMapping("list")
    public CommonResult list() {
        List<Role> roleList = roleService.listAll();
        return CommonResult.success(roleList);
    }

    /**
     * 更新角色
     * @param updateRoleParams
     * @return
     */
    @PostMapping("update")
    public CommonResult update(@RequestBody @Validated UpdateRoleParams updateRoleParams) {
        roleService.update(updateRoleParams);
        return CommonResult.success();
    }

    /**
     * 删除角色
     * @param id 角色id
     * @return
     */
    @RequestMapping("delete")
    public CommonResult delete(@RequestParam("id") long id) {
        roleService.delete(id);
        return CommonResult.success();
    }

    /**
     * 查询用户下所有角色
     * @param userId
     * @return
     */
    @GetMapping("listByUser")
    public CommonResult listByUser(@RequestParam("userId") long userId) {
        List<Role> roleList = roleService.listByUser(userId);
        return CommonResult.success(roleList);
    }

    /**
     * 给角色分配菜单权限
     * @param menuAssignParams
     * @return
     */
    @PostMapping("menu/assign")
    public CommonResult assign(@RequestBody @Validated MenuAssignParams menuAssignParams) {
        roleMenuInfoService.assign(menuAssignParams);
        return CommonResult.success(null);
    }

    /**
     * 删除角色菜单
     * @param params
     * @return
     */
    @PostMapping("menu/delete")
    public CommonResult delete(@RequestBody @Validated DeleteRoleMenuRelationParams params) {
        roleMenuInfoService.delete(params);
        return CommonResult.success();
    }

    /**
     * 批量删除角色菜单
     * @param params
     * @return
     */
    @PostMapping("menu/batchDelete")
    public CommonResult batchDelete(@RequestBody @Validated BatchDeleteRoleMenuRelationParams params) {
        roleMenuInfoService.batchDelete(params);
        return CommonResult.success();
    }
}
