package net.tartan.platform.integration.controller;

import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.integration.beans.query.ToolQuery;
import net.tartan.platform.integration.beans.vo.DeviceRiskStatisticsVo;
import net.tartan.platform.integration.service.IDeviceService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.apache.commons.lang3.StringUtils;
import net.tartan.platform.integration.beans.vo.HomeDeviceStatisticsVo;
import org.springframework.web.bind.annotation.GetMapping;
import net.tartan.platform.integration.beans.query.DeviceRiskQuery;
import net.tartan.platform.integration.entity.DeviceAvailableCount;

import javax.annotation.Resource;
import java.util.List;

/**
 * home 首页
 */
@RestController
@RequestMapping("/home")
public class HomeController {
    @Resource
    private IDeviceService deviceService;

    /**
     * 根据仪器类型统计数据
     * 
     * @param toolQuery 查询参数，包含:
     *                  deviceType - 仪器类型
     *                  conditionFieldNameList - 需要分组的统计参数列表
     *                  counterFieldName - 要统计的属性
     *                  counterValueList - 统计时只关注的参数值列表
     * @return 统计结果列表
     */
    @PostMapping("statisticsByType")
    public CommonResult statisticsByType(@RequestBody ToolQuery toolQuery) {
        return CommonResult.success(
                deviceService.groupByStatusAndCountX(toolQuery)
        );
    }
    
    /**
     * 获取首页仪器状态统计
     * 统计指定仪器类型的状态分布(上井中/车间可用/维修中/滞留)
     * 
     * @return 统计结果
     */
    @PostMapping("/deviceStatistics")
    public CommonResult getDeviceStatistics(@RequestBody(required = false) DeviceRiskQuery query) {
        String owner = query != null ? query.getOwner() : null;
        List<HomeDeviceStatisticsVo> statistics = deviceService.getHomeDeviceStatistics(owner);
        
        // 计算使用率
        for (HomeDeviceStatisticsVo vo : statistics) {
            // 初始化可能为null的字段
            if (vo.getAtRig() == null) vo.setAtRig(0);
            if (vo.getRepair() == null) vo.setRepair(0);
            if (vo.getReady() == null) vo.setReady(0);
            if (vo.getPending() == null) vo.setPending(0);
            if (vo.getTotal() == null) vo.setTotal(0);
            
            // 计算使用率
            if (vo.getTotal() > 0) {
                double rate = (double)(vo.getAtRig()) / vo.getTotal() * 100;
                vo.setUsageRate(String.format("%.1f%%", rate));
            } else {
                vo.setUsageRate("0.0%");
            }
            
            // 处理近钻头的显示名称
            if (vo.getDeviceType() != null && vo.getDeviceType() == 16001L && StringUtils.isNotEmpty(vo.getSpecification())) {
                vo.setDeviceTypeStr(vo.getSpecification() + "近钻");
            }
        }
        
        return CommonResult.success(statistics);
    }

    /**
     * 获取设备风险统计
     */
    @PostMapping("/deviceRiskStatistics")
    public CommonResult getDeviceRiskStatistics(@RequestBody(required = false) DeviceRiskQuery query) {
        Long status = query != null ? query.getStatus() : null;
        String owner = query != null ? query.getOwner() : null;
        
        List<DeviceRiskStatisticsVo> statistics = deviceService.getDeviceRiskStatistics(status, owner);
        for (DeviceRiskStatisticsVo vo : statistics) {
            if (vo.getDeviceType() != null && vo.getDeviceType() == 16001L && StringUtils.isNotEmpty(vo.getSpecification())) {
                vo.setDeviceTypeStr(vo.getSpecification() + "近钻");
            }
        }

        return CommonResult.success(statistics);
    }
    
    /**
     * 获取设备可用数量
     */
    @GetMapping("/device-available-count")
    public CommonResult getDeviceAvailableCount() {
        return CommonResult.success(deviceService.getDeviceAvailableCount());
    }

    /**
     * 更新设备可用数量
     */
    @PostMapping("/device-available-count")
    public CommonResult updateDeviceAvailableCount(
            @RequestBody DeviceAvailableCount deviceAvailableCount) {
        String deviceType = deviceAvailableCount.getDeviceType();
        Integer count = deviceAvailableCount.getAvailableCount();
        boolean success = deviceService.updateDeviceAvailableCount(deviceType, count);
        return success ? CommonResult.success() : CommonResult.failed("更新失败");
    }
}
