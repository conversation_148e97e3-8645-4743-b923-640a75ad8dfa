package net.tartan.platform.integration.controller;


import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.common.beans.ResultCode;
import net.tartan.platform.integration.beans.request.*;
import net.tartan.platform.integration.beans.vo.UserAndRoleVo;
import net.tartan.platform.integration.beans.vo.UserInfoVo;
import net.tartan.platform.integration.entity.UserInfo;
import net.tartan.platform.integration.service.IUserInfoRoleRelationService;
import net.tartan.platform.integration.service.IUserInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * user 用户管理
 */
@RestController
@RequestMapping("user")
public class UserInfoController {
    @Autowired
    private PasswordEncoder passwordEncoder;
    @Autowired
    private IUserInfoService userInfoService;
    @Autowired
    private IUserInfoRoleRelationService userInfoRoleRelationService;

    /**
     * 查询所有用户
     * @return
     */
    @PostMapping("list")
    public CommonResult list(@RequestBody(required = false) UserAndRoleVo userInfo) {
        List<UserAndRoleVo> accountInfoVoList = userInfoService.listAll(userInfo);
        return CommonResult.success(accountInfoVoList);
    }

    /**
     * 查询拥有某个角色的所有用户
     *
     * @param roleId 角色id
     * @return
     */
    @GetMapping("listByRole")
    public CommonResult list(@RequestParam("roleId") long roleId) {
        List<UserInfoVo> accountInfoVoList = userInfoService.listByRole(roleId);
        return CommonResult.success(accountInfoVoList);
    }

    /**
     * 查询拥有某个菜单权限的所有用户
     *
     * @param menuId 菜单id
     * @return
     */
    @GetMapping("listByMenu")
    public CommonResult listByMenu(@RequestParam("menuId") long menuId) {
        List<UserInfoVo> accountInfoVoList = userInfoService.listByMenuId(menuId);
        return CommonResult.success(accountInfoVoList);
    }

    /**
     * 批量更改用户状态
     *
     * @param params
     * @return
     */
    @PostMapping("batchDelete")
    public CommonResult batchDelete(@RequestBody @Validated BatchUpdateUserStatusParams params) {
        userInfoService.batchDelete(params.getUserIdList());
        return CommonResult.success();
    }

    /**
     * 删除用户
     *
     * @param userId
     * @return
     */
    @RequestMapping("delete")
    public CommonResult delete(@RequestParam("userId") long userId) {
        userInfoService.delete(userId);
        return CommonResult.success();
    }

    /**
     * 添加账户
     *
     * @param userInfo
     * @return
     */
    @PostMapping("add")
    public CommonResult add(@RequestBody UserInfo userInfo) {
        if (Objects.isNull(userInfo.getMemberId()) ||
                Objects.isNull(userInfo.getUsername()) ||
                Objects.isNull(userInfo.getPassword())) {
            return CommonResult.failed(ResultCode.PARAMS_ERROR);
        }
        userInfoService.add(userInfo);
        return CommonResult.success();
    }

    /**
     * 更新用户
     *
     * @param accountInfo
     * @return
     */
    @PostMapping("update")
    public CommonResult update(@RequestBody UserInfo accountInfo) {
        if (Objects.isNull(accountInfo.getUserId())) {
            return CommonResult.failed(ResultCode.PARAMS_ERROR);
        }
        String password = accountInfo.getPassword();
        if (password != null) {
            accountInfo.setPassword(passwordEncoder.encode(password));
        }
        userInfoService.updateById(accountInfo);
        return CommonResult.success();
    }

    /**
     * 给用户分配角色
     *
     * @param roleAssignParams
     * @return
     */
    @PostMapping("role/assign")
    public CommonResult assign(@RequestBody @Validated RoleAssignParams roleAssignParams) {
        userInfoRoleRelationService.assign(roleAssignParams);
        return CommonResult.success(null);
    }

    /**
     * 批量给用户分配角色
     * @param roleAssignParams
     * @return
     */
    @PostMapping("role/batchAssign")
    public CommonResult batchAssign(@RequestBody @Validated RoleBatchAssignParams roleAssignParams) {
        userInfoRoleRelationService.batchAssign(roleAssignParams);
        return CommonResult.success(null);
    }

    /**
     * 删除用户下的角色
     * @param params
     * @return
     */
    @PostMapping("role/delete")
    public CommonResult delete(@RequestBody @Validated DeleteUserRoleParams params) {
        userInfoRoleRelationService.delete(params);
        return CommonResult.success();
    }

    /**
     * 批量删除用户下的角色
     * @param params
     * @return
     */
    @PostMapping("role/batchDelete")
    public CommonResult batchDelete(@RequestBody @Validated DeleteUserRolesParams params) {
        userInfoRoleRelationService.batchDelete(params);
        return CommonResult.success();
    }

    /**
     * 获取进度管理下人员的组织树
     */
    @GetMapping("listByOrgTree")
    public CommonResult listByOrgTree() {
        List<Map<String, Objects>> orgList = userInfoService.listByOrgTree();
        return CommonResult.success(orgList);
    }

}
