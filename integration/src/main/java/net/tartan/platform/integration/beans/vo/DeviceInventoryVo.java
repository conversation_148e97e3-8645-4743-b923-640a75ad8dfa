package net.tartan.platform.integration.beans.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class DeviceInventoryVo {
    /**
     * 仪器类型
     */
    private Long stockType;
    
    /**
     * 仪器类型名称
     */
//    private String invName;
    
    /**
     * 车间可用数量 (READY - 19002)
     */
    private Integer ready;
    
    /**
     * 上井数量 (AT_RIG - 19001)
     */ 
    private Integer atRig;
    
    /**
     * 维修数量 (REPAIR - 19003)
     */
    private Integer repair;
    
    /**
     * 其他状态数量
     */
    private Integer other;
    
    /**
     * 总计
     */
    private Integer total;
}