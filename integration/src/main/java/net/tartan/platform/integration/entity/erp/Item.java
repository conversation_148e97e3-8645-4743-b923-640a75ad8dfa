package net.tartan.platform.integration.entity.erp;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 通用品号信息/CHT/通用品號資料/ENU/General Item Data
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ITEM")
public class Item implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 关联部门
     */
    @TableField("Owner_Dept")
    private String ownerDept;

    /**
     * 关联员工
     */
    @TableField("Owner_Emp")
    private String ownerEmp;

    /**
     * 主键
     */
    @TableId("ITEM_BUSINESS_ID")
    private String itemBusinessId;

    /**
     * 存货管理
     */
    @TableField("INVENTORY_MANAGEMENT")
    private Boolean inventoryManagement;

    /**
     * 品号
     */
    @TableField("ITEM_CODE")
    private String itemCode;

    @TableField("ITEM_NAME")
    private String itemName;

    /**
     * 备注
     */
    @TableField("REMARK")
    private String remark;

    /**
     * 归类品
     */
    @TableField("CHANGE_ITEM_SPEC")
    private Boolean changeItemSpec;

    @TableField("SHORTCUT")
    private String shortcut;

    @TableField("ITEM_SPECIFICATION")
    private String itemSpecification;

    /**
     * 批号管理
     */
    @TableField("LOT_CONTROL")
    private String lotControl;

    /**
     * 批号有效天数
     */
    @TableField("LOT_EXPIRE_DAY")
    private Integer lotExpireDay;

    /**
     * 批号等待天数
     */
    @TableField("LOT_WAITING_DAY")
    private Integer lotWaitingDay;

    /**
     * 批号复检天数
     */
    @TableField("LOT_REINSPECTION_DAY")
    private Integer lotReinspectionDay;

    /**
     * 修改批号有效日期
     */
    @TableField("LOT_FAILURE_CONTROL")
    private String lotFailureControl;

    /**
     * 计量体系
     */
    @TableField("UNIT_MODE")
    private String unitMode;

    /**
     * 库存检查方式
     */
    @TableField("INVENTORY_CHECK_BY")
    private String inventoryCheckBy;

    /**
     * 序列号管理
     */
    @TableField("ITEM_SN_MANAGEMENT")
    private Boolean itemSnManagement;

    /**
     * 序列号输入模式
     */
    @TableField("SN_ENTRY_MODE")
    private String snEntryMode;

    /**
     * 启用特征码
     */
    @TableField("ITEM_FEATURE_CONTROL")
    private Boolean itemFeatureControl;

    /**
     * 以包装方式输入数量
     */
    @TableField("PACKING_UNIT")
    private Boolean packingUnit;

    /**
     * 状态
     */
    @TableField("STATUS")
    private String status;

    /**
     * 工程品号
     */
    @TableField("E_ITEM")
    private String eItem;

    /**
     * 工程码
     */
    @TableField("E_CODE")
    private String eCode;

    /**
     * 特征码产生模式
     */
    @TableField("FEATURE_GENERATE_MODE")
    private String featureGenerateMode;

    /**
     * CKD母件
     */
    @TableField("CKD")
    private Boolean ckd;

    /**
     * 图片
     */
    @TableField("ITEM_PICTURE")
    private String itemPicture;

    /**
     * 启用联产品
     */
    @TableField("JOINT_PRODUCT_CONTROL")
    private Boolean jointProductControl;

    /**
     * 需要做料件认可
     */
    @TableField("NEED_CERTIFICATION")
    private Boolean needCertification;

    /**
     * 批号编码规则
     */
    @TableField("LOT_NO_RULE_ID")
    private String lotNoRuleId;

    /**
     * 序号编码规则
     */
    @TableField("SN_NO_RULE_ID")
    private String snNoRuleId;

    /**
     * 品号群组
     */
    @TableField("FEATURE_GROUP_ID")
    private String featureGroupId;

    /**
     * 品号群组CODE
     */
    private String featureGroupCode;

    /**
     * 第二单位
     */
    @TableField("SECOND_UNIT_ID")
    private String secondUnitId;

    /**
     * 库存单位
     */
    @TableField("STOCK_UNIT_ID")
    private String stockUnitId;

    /**
     * 生命周期
     */
    @TableField("LIFECYCLE_ID")
    private String lifecycleId;

    /**
     * 物流单位
     */
    @TableField("LOGISTIC_UNIT_ID")
    private String logisticUnitId;

    /**
     * 电子称重
     */
    @TableField("ISWEIGHT")
    private Boolean isweight;

    /**
     * 电子称变价
     */
    @TableField("WEIGHT_PRICE")
    private Boolean weightPrice;

    /**
     * 电子称PLU
     */
    @TableField("WEIGHT_PLU")
    private String weightPlu;

    /**
     * 电子称单位
     */
    @TableField("WEIGHT_UNIT_ID")
    private String weightUnitId;

    /**
     * 品号来源
     */
    @TableField("SOURCE")
    private String source;

    /**
     * PLM传输批次号
     */
    @TableField("PLM_DATAKEY")
    private String plmDatakey;

    /**
     * 品号图片
     */
    @TableField("ITEM_PIC")
    private String itemPic;

    /**
     * 商品描述(作废)
     */
    @TableField("ITEM_DESC2")
    private String itemDesc2;

    /**
     * 品号描述
     */
    @TableField("ITEM_DESC")
    private String itemDesc;

    /**
     * 图号
     */
    @TableField("DRAWING_NO")
    private String drawingNo;

    /**
     * 图号取自
     */
    @TableField("DRAWING_NO_FROM")
    private String drawingNoFrom;

    /**
     * 品号净重
     */
    @TableField("ITEM_NET_WEIGHT")
    private Double itemNetWeight;

    /**
     * 重量单位
     */
    @TableField("NET_WEIGHT_UNIT_ID")
    private String netWeightUnitId;

    /**
     * 服务品号
     */
    @TableField("SERVICE_ITEM")
    private Boolean serviceItem;

    /**
     * 资产
     */
    @TableField("ASSET")
    private Boolean asset;

    /**
     * 创建日期
     */
    @TableField("CreateDate")
    private Date createdate;

    /**
     * 最后修改日期
     */
    @TableField("LastModifiedDate")
    private Date lastmodifieddate;

    /**
     * 修改日期
     */
    @TableField("ModifiedDate")
    private Date modifieddate;

    /**
     * 创建者
     */
    @TableField("CreateBy")
    private String createby;

    /**
     * 最后修改者
     */
    @TableField("LastModifiedBy")
    private String lastmodifiedby;

    /**
     * 修改者
     */
    @TableField("ModifiedBy")
    private String modifiedby;

    /**
     * 附件
     */
    @TableField("Attachments")
    private String attachments;

    /**
     * 表单所在的流程实例的编号
     */
    @TableField("ProcessInstanceId")
    private String processinstanceid;

    /**
     * 版本号，不要随意更改
     */
    @TableField("Version")
    private byte[] version;

    /**
     * 单据状态属性
     */
    @TableField("ApproveStatus")
    private String approvestatus;

    /**
     * 修改日期
     */
    @TableField("ApproveDate")
    private Date approvedate;

    /**
     * 修改人
     */
    @TableField("ApproveBy")
    private String approveby;

    @TableField("Owner_Org_RTK")
    private String ownerOrgRtk;

    @TableField("Owner_Org_ROid")
    private String ownerOrgRoid;


}
