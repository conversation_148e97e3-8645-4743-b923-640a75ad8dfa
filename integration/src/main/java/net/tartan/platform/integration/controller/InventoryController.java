package net.tartan.platform.integration.controller;


import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.integration.beans.dto.InventoryDto;
import net.tartan.platform.integration.beans.dto.InventoryTemplateDto;
import net.tartan.platform.integration.beans.query.ToolQuery;
import net.tartan.platform.integration.beans.vo.InventoryTreeVo;
import net.tartan.platform.integration.entity.InventoryInfo;
import net.tartan.platform.integration.mapper.InventoryInfoMapper;
import net.tartan.platform.integration.service.IInventoryInfoService;
import net.tartan.platform.integration.service.IInventoryRelationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 库存控制器
 *
 * <AUTHOR>
 * @since 2021-06-22
 */
@RestController
@RequestMapping("/inventory")
public class InventoryController {
    @Autowired
    IInventoryInfoService inventoryInfoService;
    
    @Autowired
    IInventoryRelationService inventoryRelationService;

    // 2025/04/17 update 由于原本从erp同步过来的current_stock_info已不在同步，则将查找改为component表
    @PostMapping("/invName")
    public CommonResult queryInventory(@RequestBody(required = false) InventoryDto dto) {
        return CommonResult.success(inventoryInfoService.queryInventoryByName(dto));
    }

    @PostMapping("/selectInvName")
    public CommonResult inventoryInfoByName(@RequestBody ToolQuery toolQuery) {
        List<InventoryInfo> list = inventoryInfoService.inventoryInfoByName(toolQuery);
        return CommonResult.success(list);
    }
    
    /**
     * 根据工具名称查询模版信息
     * 
     * @param dto 请求参数
     * @return 模版信息
     */
    @PostMapping("/template")
    public CommonResult<InventoryTreeVo> getInventoryTemplate(@RequestBody InventoryTemplateDto dto) {
        InventoryTreeVo treeVo = inventoryRelationService.buildInventoryTree(dto.getInvCode());
        return CommonResult.success(treeVo);
    }
}
