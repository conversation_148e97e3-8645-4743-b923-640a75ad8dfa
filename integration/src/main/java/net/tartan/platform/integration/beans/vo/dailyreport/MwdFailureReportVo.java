package net.tartan.platform.integration.beans.vo.dailyreport;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.tartan.platform.common.enums.EnumReportType;
import net.tartan.platform.integration.entity.dailyreport.MwdComponent;
import net.tartan.platform.integration.entity.dailyreport.MwdTool;

import java.math.BigDecimal;
import java.util.List;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MwdFailureReportVo {

    private EnumReportType reportType;

    private long lastModified;

    /**
     * 仪器箱
     */
    private String kit;
    /**
     * 日期
     */
    private String date;
    /**
     * 工程师
     */
    private String operator;

    /**
     * 客户公司
     */
    private String client;

    /**
     * 井号
     */
    private String wellNumber;

    /**
     * 位置
     */
    private String location;

    /**
     * 区块
     */
    private String field;

    /**
     * 井队号
     */
    private String drillingRig;

    /**
     * 失效日期yyyy-MM-dd
     */
    private String failureDate;
    /**
     * 入井日期
     */
    private String inHoleDate;

    /**
     * 报告日期yyyy-MM-dd
     */
    private String reportDate;

    /**
     * 失效描述
     */
    private String failureSymptoms;

    /**
     * 采取措施及效果
     */
    private String actionTaken;

    /**
     * 地面检查及结果
     */
    private String surfaceInspection;

    /**
     * 车间调查结果
     */
    private String shopInspectionc;

//    /**
//     * 是否地面损坏
//     */
//    private Integer surfaceFailed;
//
//    /**
//     * 是否脉冲损坏
//     */
//    private Integer pulserFailed;
//
//    /**
//     * 是否探管损坏
//     */
//    private Integer dmFailed;
//
//    /**
//     * 是否电池1损坏
//     */
//    private Integer bat1Failed;
//
//    /**
//     * 是否电池2损坏
//     */
//    private Integer bat2Failed;
//
//    /**
//     * 是否司显损坏
//     */
//    private Integer rfdFailed;
//
//    /**
//     * 是否伽马损坏
//     */
//    private Integer gammaFailed;
//
//    /**
//     * 是否井下损坏
//     */
//    private Integer downholeFailed;
//
//    /**
//     * 是否传感器损坏
//     */
//    private Integer transducerFailed;
//
//    /**
//     * 是否线缆损坏
//     */
//    private Integer cablesFailed;
//
//    /**
//     * 是否电脑损坏
//     */
//    private Integer computersFailed;
//
//    /**
//     * 是否软件损坏
//     */
//    private Integer softwareFailed;
//
//    /**
//     * 是否司显接受盒损坏
//     */
//    private Integer rfdBoxFailed;
//
//    /**
//     * 是否其他损坏
//     */
//    private Integer otherFailed;

    /**
     * 井下总时间
     */
    private BigDecimal totalDowntime;

    /**
     * 是否发现问题（是：1，否：2，其他：3）
     */
    private Integer findProblem;

    /**
     * 排量
     */
    private BigDecimal flowRate;

    /**
     * 限流环
     */
    private BigDecimal orfice;

    /**
     * 蘑菇头
     */
    private BigDecimal poppet;

    /**
     * 脉宽
     */
    private BigDecimal pulsewth;

    /**
     * 井下时间
     */
    private BigDecimal runHours;

    /**
     * 测深
     */
    private BigDecimal measDepth;

    /**
     * 井斜
     */
    private BigDecimal inc;

    /**
     * 方位
     */
    private BigDecimal azm;

    /**
     * 磁偏角
     */
    private BigDecimal magdec;

    /**
     * 温度
     */
    private BigDecimal temp;

    /**
     * 电池电压
     */
    private BigDecimal batteryVolts;
    /**
     * 趟次
     */
    private Integer run;
    /**
     * 工具
     */
    private List<MwdTool> toolList;

    private List<MwdComponent> componentList;

}
