package net.tartan.platform.integration.beans.query;

import lombok.Data;
import net.tartan.platform.common.enums.EnumRiskType;
import net.tartan.platform.integration.entity.deviceassemble.AssembleComponent;

import java.util.List;

/**
 * mwd工单信息表
 *
 * <AUTHOR>
 * @since 2022-05-09
 */
@Data
public class WorkOrderMwdQuery {

    private Long mwdId;

    /**
     * 工单单号
     */
    private String mwdNumber;

    /**
     * 开始的开始日期
     */
    private String startStartTime;

    /**
     * 开始的结束日期
     */
    private String startEndTime;
    /**
     * 完成的开始日期
     */
    private String finishStartTime;

    /**
     * 完成的结束日期
     */
    private String finishEndTime;
    /**
     * 开始日期
     */
    private String startTime;

    /**
     * 结束日期
     */
    private String endTime;

    /**
     * 序列号
     */
    private String serialNumber;
    /**
     * 作业号
     */
    private String jobNumber;

    /**
     * 仪器类型（前端按照字典里面的来，传的是字典的id）
     */
    private Long deviceType;
    private List<Long> deviceTypeList;

    /**
     * 维修状态1：已完成，0：未完成，-1：滞留
     */
    private Integer finish;
    private List<Integer> finishList;

    /**
     * kit箱
     */
    private String kitNumber;
    /**
     * 仪器风险类型
     */
    private EnumRiskType riskType;
    private List<EnumRiskType> riskTypeList;
    /**
     * 项目号（井号）
     */
    private String wellNumber;

    /**
     * 所有者
     */
    private String owner;
    /**
     * 故障类型
     */
    private Long failureType;
    private List<Long> failureTypeList;
    /**
     * 失效部件分类
     */
    private Long failureComponentType;
    private List<Long> failureComponentTypeList;

    private String orderBy;

    private String orderType;

    private String receiveDate;

    private String editor;

    private AssembleComponent assembleComponent;

    private String repairCode;
}
