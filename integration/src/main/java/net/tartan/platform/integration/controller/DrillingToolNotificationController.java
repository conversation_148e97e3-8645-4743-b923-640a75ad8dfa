package net.tartan.platform.integration.controller;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import lombok.extern.slf4j.Slf4j;
import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.integration.entity.DrillingToolNotification;
import net.tartan.platform.integration.service.DrillingToolNotificationService;

/**
 * 钻具通知单控制器
 * 提供钻具通知单的CRUD操作和OA送签功能
 * 
 * <AUTHOR>
 * @since 2025-07-15
 */
@Slf4j
@RestController
@RequestMapping("/drillingToolNotification")
public class DrillingToolNotificationController {

    @Autowired
    private DrillingToolNotificationService drillingToolNotificationService;

    /**
     * 创建钻具通知单
     * 
     * @param notification 钻具通知单数据
     * @return 创建结果
     */
    @PostMapping("/create")
    public CommonResult<DrillingToolNotification> createNotification(@Valid @RequestBody DrillingToolNotification notification) {
        log.info("创建钻具通知单: {}", notification.getNo());
        
        try {
            DrillingToolNotification result = drillingToolNotificationService.createNotification(notification);
            return CommonResult.success(result, "钻具通知单创建成功");
        } catch (Exception e) {
            log.error("创建钻具通知单失败", e);
            return CommonResult.failed("创建钻具通知单失败: " + e.getMessage());
        }
    }
    /**
     * 创建钻具通知单
     *
     * @param notification 钻具通知单数据
     * @return 创建结果
     */
    @PostMapping("/sent")
    public CommonResult<Map<String, Object>> sentNotification(@Valid @RequestBody DrillingToolNotification notification) {
        log.info("发送钻具通知单: {}", notification.getNo());

        try {
            boolean success = drillingToolNotificationService.sentNotification(notification);

            Map<String, Object> result = new HashMap<>();
            result.put("success", success);
            result.put("no", notification.getNo());
            result.put("executeTime", new Date());

            if (success) {
                return CommonResult.success(result, "钻具通知单发送成功");
            } else {
                return CommonResult.failed("钻具通知单发送失败");
            }
        } catch (Exception e) {
            log.error("发送钻具通知单失败", e);
            return CommonResult.failed("发送钻具通知单失败: " + e.getMessage());
        }
    }
    /**
     * 初始化钻具通知单
     *
     * @return 创建结果
     */
    @GetMapping("/init")
    public CommonResult<DrillingToolNotification> initNotification() {
        log.info("初始化钻具通知单");

        try {
            DrillingToolNotification result = drillingToolNotificationService.initNotification();
            return CommonResult.success(result, "钻具通知单初始化成功");
        } catch (Exception e) {
            log.error("创建钻具通知单失败", e);
            return CommonResult.failed("创建钻具通知单失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询钻具通知单
     * 
     * @param id 钻具通知单ID
     * @return 钻具通知单详情
     */
    @GetMapping("/{id}")
    public CommonResult<DrillingToolNotification> getNotificationById(@PathVariable String id) {
        log.info("查询钻具通知单: {}", id);
        
        try {
            DrillingToolNotification notification = drillingToolNotificationService.getNotificationById(id);
            return CommonResult.success(notification);
        } catch (Exception e) {
            log.error("查询钻具通知单失败", e);
            return CommonResult.failed("查询钻具通知单失败: " + e.getMessage());
        }
    }

    /**
     * 根据单据编号查询钻具通知单
     * 
     * @param no 单据编号
     * @return 钻具通知单详情
     */
    @GetMapping("/by-no/{no}")
    public CommonResult<DrillingToolNotification> getNotificationByNo(@PathVariable String no) {
        log.info("根据单据编号查询钻具通知单: {}", no);
        
        try {
            DrillingToolNotification notification = drillingToolNotificationService.getNotificationByNo(no);
            return CommonResult.success(notification);
        } catch (Exception e) {
            log.error("查询钻具通知单失败", e);
            return CommonResult.failed("查询钻具通知单失败: " + e.getMessage());
        }
    }

    /**
     * 更新钻具通知单
     * 
     * @param notification 钻具通知单数据
     * @return 更新结果
     */
    @PutMapping("/update")
    public CommonResult<DrillingToolNotification> updateNotification(@Valid @RequestBody DrillingToolNotification notification) {
        log.info("更新钻具通知单: {}", notification.getId());
        
        try {
            DrillingToolNotification result = drillingToolNotificationService.updateNotification(notification);
            return CommonResult.success(result, "钻具通知单更新成功");
        } catch (Exception e) {
            log.error("更新钻具通知单失败", e);
            return CommonResult.failed("更新钻具通知单失败: " + e.getMessage());
        }
    }

    /**
     * 删除钻具通知单
     * 
     * @param id 钻具通知单ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    public CommonResult<Void> deleteNotification(@PathVariable String id) {
        log.info("删除钻具通知单: {}", id);
        
        try {
            drillingToolNotificationService.deleteNotification(id);
            return CommonResult.success(null, "钻具通知单删除成功");
        } catch (Exception e) {
            log.error("删除钻具通知单失败", e);
            return CommonResult.failed("删除钻具通知单失败: " + e.getMessage());
        }
    }

    /**
     * 查询所有钻具通知单
     * 
     * @return 钻具通知单列表
     */
    @GetMapping("/list")
    public CommonResult<List<DrillingToolNotification>> getAllNotifications() {
        log.info("查询所有钻具通知单");
        
        try {
            List<DrillingToolNotification> notifications = drillingToolNotificationService.getAllNotifications();
            return CommonResult.success(notifications);
        } catch (Exception e) {
            log.error("查询钻具通知单列表失败", e);
            return CommonResult.failed("查询钻具通知单列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据条件查询钻具通知单
     * 
     * @param conditions 查询条件
     * @return 钻具通知单列表
     */
    @PostMapping("/search")
    public CommonResult<List<DrillingToolNotification>> searchNotifications(@RequestBody Map<String, Object> conditions) {
        log.info("根据条件查询钻具通知单: {}", conditions);
        
        try {
            List<DrillingToolNotification> notifications = drillingToolNotificationService.getNotificationsByConditions(conditions);
            return CommonResult.success(notifications);
        } catch (Exception e) {
            log.error("查询钻具通知单失败", e);
            return CommonResult.failed("查询钻具通知单失败: " + e.getMessage());
        }
    }

    /**
     * 根据日期范围查询钻具通知单
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 钻具通知单列表
     */
    @GetMapping("/by-date-range")
    public CommonResult<List<DrillingToolNotification>> getNotificationsByDateRange(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {
        log.info("根据日期范围查询钻具通知单: {} - {}", startDate, endDate);
        
        try {
            List<DrillingToolNotification> notifications = drillingToolNotificationService.getNotificationsByDateRange(startDate, endDate);
            return CommonResult.success(notifications);
        } catch (Exception e) {
            log.error("查询钻具通知单失败", e);
            return CommonResult.failed("查询钻具通知单失败: " + e.getMessage());
        }
    }

    /**
     * 查询待同步到OA的钻具通知单
     * 
     * @return 待同步的钻具通知单列表
     */
    @GetMapping("/pending-sync")
    public CommonResult<List<DrillingToolNotification>> getPendingSyncNotifications() {
        log.info("查询待同步到OA的钻具通知单");
        
        try {
            List<DrillingToolNotification> notifications = drillingToolNotificationService.getPendingSyncNotifications();
            return CommonResult.success(notifications);
        } catch (Exception e) {
            log.error("查询待同步钻具通知单失败", e);
            return CommonResult.failed("查询待同步钻具通知单失败: " + e.getMessage());
        }
    }

    /**
     * 同步单个钻具通知单到OA系统
     * 
     * @param id 钻具通知单ID
     * @return 同步结果
     */
    @PostMapping("/sync-to-oa/{id}")
    public CommonResult<Map<String, Object>> syncNotificationToOA(@PathVariable String id) {
        log.info("同步钻具通知单到OA系统: {}", id);
        
        try {
            boolean success = drillingToolNotificationService.syncNotificationToOA(id);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", success);
            result.put("id", id);
            result.put("executeTime", new Date());
            
            if (success) {
                return CommonResult.success(result, "钻具通知单同步成功");
            } else {
                return CommonResult.failed("钻具通知单同步失败");
            }
        } catch (Exception e) {
            log.error("同步钻具通知单失败", e);
            return CommonResult.failed("同步钻具通知单失败: " + e.getMessage());
        }
    }

    /**
     * 批量同步功能已移除，改为手动触发模式
     * 此方法保留接口兼容性，但不再执行批量同步
     *
     * @return 提示信息
     */
    @PostMapping("/batch-sync-to-oa")
    public CommonResult<Map<String, Object>> batchSyncNotificationsToOA() {

        Map<String, Object> result = new HashMap<>();
        result.put("message", "批量同步功能已移除，请使用手动触发模式");
        result.put("executeTime", new Date());

        return CommonResult.success(result, "批量同步功能已移除");
    }

    /**
     * 生成OA送签数据预览
     * 
     * @param id 钻具通知单ID
     * @return OA送签数据
     */
    @GetMapping("/oa-data-preview/{id}")
    public CommonResult<Map<String, Object>> getOADataPreview(@PathVariable String id) {
        log.info("生成钻具通知单OA送签数据预览: {}", id);
        
        try {
            DrillingToolNotification notification = drillingToolNotificationService.getNotificationById(id);
            Map<String, Object> oaData = drillingToolNotificationService.generateOASubmitData(notification);
            return CommonResult.success(oaData);
        } catch (Exception e) {
            log.error("生成OA送签数据预览失败", e);
            return CommonResult.failed("生成OA送签数据预览失败: " + e.getMessage());
        }
    }

    /**
     * 统计钻具通知单数量
     * 
     * @return 统计结果
     */
    @GetMapping("/statistics")
    public CommonResult<Map<String, Long>> getStatistics() {
        log.info("统计钻具通知单数量");
        
        try {
            Map<String, Long> statistics = drillingToolNotificationService.countNotifications();
            return CommonResult.success(statistics);
        } catch (Exception e) {
            log.error("统计钻具通知单数量失败", e);
            return CommonResult.failed("统计钻具通知单数量失败: " + e.getMessage());
        }
    }
}
