package net.tartan.platform.integration.controller;

import net.tartan.platform.common.beans.CommonResult;
import net.tartan.platform.common.beans.ResultCode;
import net.tartan.platform.integration.beans.query.MwdLoadOutQuery;
import net.tartan.platform.integration.beans.vo.MwdLoadOutVo;
import net.tartan.platform.integration.exception.BusinessException;
import net.tartan.platform.integration.service.IMwdLoadOutService;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

/**
 * mwdLoadOut kit箱出入库
 */
@RestController
@RequestMapping("/mwdLoadOut")
public class MwdLoadOutController {
    @Autowired
    private IMwdLoadOutService mwdLoadOutService;

    @GetMapping("getTemplate")
    public CommonResult getTemplate(@RequestParam(value = "templateType", required = false) String templateType) {
        return CommonResult.success(mwdLoadOutService.getTemplate(templateType));
    }

//    @PostMapping("loadOut")
//    public CommonResult loadOut(@RequestBody MwdLoadOutVo mwdLoadOutVo) {
//        MwdLoadOutVo vo = mwdLoadOutService.loadOut(mwdLoadOutVo);
//        return CommonResult.success(vo);
//    }
//    @PostMapping("loadIn")
//    public CommonResult loadIn(@RequestBody MwdLoadOutVo mwdLoadOutVo) {
//        MwdLoadOutVo vo = mwdLoadOutService.loadIn(mwdLoadOutVo);
//        return CommonResult.success(vo);
//    }

    @PostMapping("update")
    public CommonResult update(@RequestBody MwdLoadOutVo mwdLoadOutVo) {
        Long loadOutId = mwdLoadOutService.update(mwdLoadOutVo);
        return CommonResult.success(
                mwdLoadOutService.info(
                        MwdLoadOutQuery
                                .builder()
                                .loadOutId(loadOutId)
                                .build()
                )
        );
    }

    @PostMapping("info")
    public CommonResult info(@RequestBody MwdLoadOutQuery mwdLoadOutQuery) {
        MwdLoadOutVo vo = mwdLoadOutService.info(mwdLoadOutQuery);
        return CommonResult.success(vo);
    }
    @PostMapping("exportDetail")
    public CommonResult exportRepair(@RequestBody MwdLoadOutQuery mwdLoadOutQuery) {
        if (ObjectUtils.isEmpty(mwdLoadOutQuery)) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        mwdLoadOutService.exportDetail(mwdLoadOutQuery);
        return CommonResult.success();
    }

    @PostMapping("exportRiskAssessment")
    public CommonResult exportRiskAssessment(@RequestBody MwdLoadOutQuery mwdLoadOutQuery) throws IOException {
        if (ObjectUtils.isEmpty(mwdLoadOutQuery)) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        mwdLoadOutService.exportRiskAssessment(mwdLoadOutQuery);
        return CommonResult.success();
    }

    @PostMapping("serviceHistory")
    public CommonResult kitBoxTransferHistory(@RequestBody MwdLoadOutQuery mwdLoadOutQuery) {
        if (ObjectUtils.isEmpty(mwdLoadOutQuery) || ObjectUtils.isEmpty(mwdLoadOutQuery.getSerialNumber())) {
            throw new BusinessException(ResultCode.PARAMS_ERROR);
        }
        return CommonResult.success(mwdLoadOutService.kitBoxTransferHistory(mwdLoadOutQuery));
    }
}
